#!/usr/bin/env python3
"""
Test script to verify AG-Grid data extraction for chart 938
"""

import asyncio
import sys
import os
from playwright.async_api import async_playwright

# Add the lib directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

from pattern.sampackag.validate_metrics import SpecialMetricsValidator

async def test_ag_grid_extraction():
    """Test the AG-Grid extraction functionality for chart 938"""
    
    print("🧪 Testing AG-Grid extraction for chart 938...")
    
    # Create validator instance
    validator = SpecialMetricsValidator()
    
    async with async_playwright() as playwright:
        # Create browser context
        browser = await playwright.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            # Load the HTML file with AG-Grid data
            html_file_path = os.path.join(os.path.dirname(__file__), 'lib/pattern/sampackag/validate_metrics_html_for_938.html')
            
            if os.path.exists(html_file_path):
                # Load the HTML file
                await page.goto(f"file://{os.path.abspath(html_file_path)}")
                await asyncio.sleep(2)
                
                print("📄 Loaded HTML file with AG-Grid data")
                
                # Test extraction with different dataset labels
                test_cases = [
                    "6 Month Return Rate",
                    "12 Month Return Rate", 
                    "6 Months Return Rate",
                    "12 Months Return Rate",
                    "Other Dataset"  # Test default case
                ]
                
                for dataset_label in test_cases:
                    print(f"\n🔍 Testing extraction with dataset: '{dataset_label}'")
                    
                    try:
                        ag_grid_data = await validator.extract_ag_grid_data_for_938(page, dataset_label)
                        
                        if ag_grid_data:
                            print(f"✅ Successfully extracted {len(ag_grid_data)} containers")
                            
                            for i, container in enumerate(ag_grid_data):
                                print(f"   Container {i}:")
                                print(f"     Selector: {container.get('selector_used', 'Unknown')}")
                                print(f"     Items: {len(container.get('items', []))}")
                                
                                for j, item in enumerate(container.get('items', [])):
                                    title = item.get('title', 'Unknown')
                                    value = item.get('value', 'Unknown')
                                    print(f"       Item {j}: {title} = {value}")
                        else:
                            print("⚠️ No data extracted")
                            
                    except Exception as e:
                        print(f"❌ Error during extraction: {e}")
                
            else:
                print(f"❌ HTML file not found: {html_file_path}")
                
                # Alternative: Create a simple test HTML with AG-Grid structure
                test_html = """
                <!DOCTYPE html>
                <html>
                <head><title>Test AG-Grid</title></head>
                <body>
                    <div id="returnRateDrilldown" class="ag-theme-balham">
                        <div class="ag-row">
                            <div class="ag-cell">08/24</div>
                            <div class="ag-cell">08/01/24</div>
                            <div class="ag-cell">4706</div>
                            <div class="ag-cell">6955</div>
                            <div class="ag-cell">423</div>
                            <div class="ag-cell">577</div>
                            <div class="ag-cell">8.99</div>
                            <div class="ag-cell">8.30</div>
                        </div>
                    </div>
                </body>
                </html>
                """
                
                await page.set_content(test_html)
                await asyncio.sleep(1)
                
                print("📄 Created test HTML with AG-Grid structure")
                
                # Test extraction
                ag_grid_data = await validator.extract_ag_grid_data_for_938(page, "6 Month Return Rate")
                
                if ag_grid_data:
                    print(f"✅ Successfully extracted {len(ag_grid_data)} containers from test HTML")
                    for container in ag_grid_data:
                        for item in container.get('items', []):
                            print(f"   {item.get('title', 'Unknown')}: {item.get('value', 'Unknown')}")
                else:
                    print("⚠️ No data extracted from test HTML")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            
        finally:
            await browser.close()
    
    print("\n🏁 Test completed!")

if __name__ == "__main__":
    asyncio.run(test_ag_grid_extraction())
