
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>UI vs DB Comparison Report</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { padding: 20px; font-family: Arial, sans-serif; }
            .badge-pass { background-color: #28a745; color: white; }
            .badge-fail { background-color: #dc3545; color: white; }
            .card-header { cursor: pointer; background-color: #cfe2f3; }
            .comparison-section { display: flex; justify-content: space-between; margin-bottom: 15px; }
            .chart-info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }
            .match-status { background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 0 0 200px; }
            .value-comparison { display: flex; justify-content: space-between; margin-top: 15px; }
            .ui-extracted { background-color: #f8f9fa; padding: 15px; border-radius: 5px; flex: 1; margin-right: 10px; }
            .db-calculated { background-color: #e9ecef; padding: 15px; border-radius: 5px; flex: 1; }
            .match-indicator { font-weight: bold; padding: 8px 15px; border-radius: 5px; display: inline-block; }
            .match-true { background-color: #d4edda; color: #155724; }
            .match-false { background-color: #f8d7da; color: #721c24; }
            .section-title { font-weight: bold; margin-bottom: 8px; color: #333; }
            .field-value { margin-bottom: 5px; }
            .badge-all-passed { background-color: #28a745; color: white; }
            .badge-has-failures { background-color: #dc3545; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-4">UI vs DB Comparison Report</h1>
            <div class="mb-4">
                <strong>Tenant:</strong> fopc_simt_prime<br>
                <strong>Store:</strong> Carriage Kia of Woodstock<br>
                <strong>Role:</strong> Admin<br>
                <strong>Generated At:</strong> 2025-09-19T14:58:55.709505<br>
                <strong>Report Timestamp:</strong> 20250919_145855<br>
            </div>

            <div class="d-flex gap-3 mb-4">
                <span class="badge bg-success">Passed: 0</span>
                <span class="badge bg-danger">Failed: 55</span>
                <span class="badge bg-secondary">Total: 55</span>
                <span class="badge bg-info">Match Rate: 0.0%</span>
            </div>

            <div class="accordion" id="reportAccordion">
    
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart0">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart0" aria-expanded="false" aria-controls="chart0">
                    Revenue - Shop Supplies(chart_1239) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(6 comparisons)</small>
                </button>
            </h2>
            <div id="chart0" class="accordion-collapse collapse" aria-labelledby="heading-chart0" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>RO Count</strong> (Customer Pay (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Customer Pay (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 59116.59</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 592.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Shop Supplies - Customer Pay</strong> (Customer Pay (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Customer Pay (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 59116.59</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Shop Supplies - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> 59116.59</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Shop Supplies - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>RO Count</strong> (Internal (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Internal (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1531.18</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 10.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Shop Supplies - Internal</strong> (Internal (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Internal (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1531.18</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Shop Supplies - Internal</div>
                            <div class="field-value"><strong>Value:</strong> 1531.18</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Shop Supplies - Internal</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>RO Count</strong> (Warranty (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Warranty (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 73.23</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 3.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Shop Supplies - Warranty</strong> (Warranty (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Warranty (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 73.23</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Shop Supplies - Warranty</div>
                            <div class="field-value"><strong>Value:</strong> 73.23</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Shop Supplies - Warranty</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart1">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart1" aria-expanded="false" aria-controls="chart1">
                    CP Parts to Labor Ratio By Category(chart_936) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(9 comparisons)</small>
                </button>
            </h2>
            <div id="chart1" class="accordion-collapse collapse" aria-labelledby="heading-chart1" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Maintenance</strong> (Maintenance (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Maintenance (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.91</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> 104115.89</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts Sale - Maintenance</strong> (Maintenance (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Maintenance (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.91</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> 94490.54</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts To Labor Ratio - Maintenance</strong> (Maintenance (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Maintenance (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.91</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> 0.91</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Competitive</strong> (Competitive (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Competitive (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.93</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> 3372.75</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts Sale - Competitive</strong> (Competitive (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Competitive (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.93</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> 6495.5</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts To Labor Ratio - Competitive</strong> (Competitive (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Competitive (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.93</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> 1.93</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Competitive</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Repair</strong> (Repair (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Repair (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.02</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Repair</div>
                            <div class="field-value"><strong>Value:</strong> 26993.11</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Repair</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts Sale - Repair</strong> (Repair (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Repair (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.02</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Repair</div>
                            <div class="field-value"><strong>Value:</strong> 27540.1</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts Sale - Repair</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts To Labor Ratio - Repair</strong> (Repair (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Repair (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 1.02</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Repair</div>
                            <div class="field-value"><strong>Value:</strong> 1.02</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio - Repair</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart2">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart2" aria-expanded="false" aria-controls="chart2">
                    Labor Sold Hours Percentage By Pay Type(chart_935) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(3 comparisons)</small>
                </button>
            </h2>
            <div id="chart2" class="accordion-collapse collapse" aria-labelledby="heading-chart2" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - All Categories</strong> (Maintenance Plan (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Maintenance Plan (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.08</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> 1570.1</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - All Categories</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours - Maintenance</strong> (Maintenance Plan (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Maintenance Plan (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.08</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> 133.2</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours % - Maintenance</strong> (Maintenance Plan (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Maintenance Plan (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.08</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> 8.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours % - Maintenance</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart3">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart3" aria-expanded="false" aria-controls="chart3">
                    CP 1-Line-RO Count(chart_948) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(9 comparisons)</small>
                </button>
            </h2>
            <div id="chart3" class="accordion-collapse collapse" aria-labelledby="heading-chart3" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Under 60K</strong> (Single line Mileage Under 60k (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Under 60k (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 309.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> 309.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale</strong> (Single line Mileage Under 60k (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Under 60k (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 309.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> 30079.29</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours</strong> (Single line Mileage Under 60k (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Under 60k (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 309.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> 244.9</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Over 60K</strong> (Single line Mileage Over 60k (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Over 60k (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 110.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> 110.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale</strong> (Single line Mileage Over 60k (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Over 60k (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 110.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> 15779.54</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours</strong> (Single line Mileage Over 60k (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Over 60k (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 110.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> 104.3</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Shop</strong> (Single line Total Shop (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Total Shop (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 419.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> 419.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale</strong> (Single line Total Shop (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Total Shop (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 419.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> 45858.83</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sold Hours</strong> (Single line Total Shop (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Total Shop (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 419.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> 349.2</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sold Hours</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart4">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart4" aria-expanded="false" aria-controls="chart4">
                    CP 1-Line-RO Count Percentage(chart_923) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(9 comparisons)</small>
                </button>
            </h2>
            <div id="chart4" class="accordion-collapse collapse" aria-labelledby="heading-chart4" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Single line Mileage Under 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Under 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 69.12751677852349</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 447.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>1 Line Count</strong> (Single line Mileage Under 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Under 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 69.12751677852349</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> 309.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Under 60K</strong> (Single line Mileage Under 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Under 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 69.12751677852349</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> 69.13</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Single line Mileage Over 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Over 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 51.***************</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 214.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>1 Line Count</strong> (Single line Mileage Over 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Over 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 51.***************</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> 110.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Over 60K</strong> (Single line Mileage Over 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Mileage Over 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 51.***************</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> 51.4</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Single line Total Shop perc (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Total Shop perc (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 63.38880484114977</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 661.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>1 Line Count</strong> (Single line Total Shop perc (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Total Shop perc (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 63.38880484114977</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> 419.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Shop</strong> (Single line Total Shop perc (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Single line Total Shop perc (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 63.38880484114977</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> 63.39</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart5">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart5" aria-expanded="false" aria-controls="chart5">
                    Multi-Line-RO Count Percentage(chart_1355) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(9 comparisons)</small>
                </button>
            </h2>
            <div id="chart5" class="accordion-collapse collapse" aria-labelledby="heading-chart5" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Multi line Mileage Under 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Mileage Under 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 30.87248322147651</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 447.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>1 Line Count</strong> (Multi line Mileage Under 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Mileage Under 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 30.87248322147651</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> 138.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Under 60K</strong> (Multi line Mileage Under 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Mileage Under 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 30.87248322147651</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> 30.87</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Under 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Multi line Mileage Over 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Mileage Over 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 48.598130841121495</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 214.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>1 Line Count</strong> (Multi line Mileage Over 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Mileage Over 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 48.598130841121495</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> 104.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Mileage Over 60K</strong> (Multi line Mileage Over 60K (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Mileage Over 60K (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 48.598130841121495</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> 48.6</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Mileage Over 60K</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Multi line Total Shop perc (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Total Shop perc (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 36.61119515885023</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 661.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>1 Line Count</strong> (Multi line Total Shop perc (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Total Shop perc (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 36.61119515885023</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> 242.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> 1 Line Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Shop</strong> (Multi line Total Shop perc (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Total Shop perc (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 36.61119515885023</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> 15.73</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart6">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart6" aria-expanded="false" aria-controls="chart6">
                    Multi-Line-RO Count(chart_1354) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(1 comparisons)</small>
                </button>
            </h2>
            <div id="chart6" class="accordion-collapse collapse" aria-labelledby="heading-chart6" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Shop</strong> (Multi line Total Shop (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Multi line Total Shop (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 242.0</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> 242.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Shop</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart7">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart7" aria-expanded="false" aria-controls="chart7">
                    CP Parts to Labor Ratio(chart_930) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(3 comparisons)</small>
                </button>
            </h2>
            <div id="chart7" class="accordion-collapse collapse" aria-labelledby="heading-chart7" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Labor Sale - Customer Pay</strong> (Parts to Labor Ratio (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Parts to Labor Ratio (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.96</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> 134481.75</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Labor Sale - Customer Pay</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total Parts Sale</strong> (Parts to Labor Ratio (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Parts to Labor Ratio (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.96</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Parts Sale</div>
                            <div class="field-value"><strong>Value:</strong> 128526.14</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total Parts Sale</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Parts To Labor Ratio</strong> (Parts to Labor Ratio (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Parts to Labor Ratio (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 0.96</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio</div>
                            <div class="field-value"><strong>Value:</strong> 0.96</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Parts To Labor Ratio</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart8">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart8" aria-expanded="false" aria-controls="chart8">
                    MPI Penetration Percentage(chart_1316) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(3 comparisons)</small>
                </button>
            </h2>
            <div id="chart8" class="accordion-collapse collapse" aria-labelledby="heading-chart8" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>MPI Count</strong> (MPI Penetration Percentage (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> MPI Penetration Percentage (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 2.34</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> MPI Count</div>
                            <div class="field-value"><strong>Value:</strong> 22.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> MPI Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (MPI Penetration Percentage (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> MPI Penetration Percentage (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 2.34</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 1017.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>MPI Penetration %</strong> (MPI Penetration Percentage (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> MPI Penetration Percentage (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 2.34</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> MPI Penetration %</div>
                            <div class="field-value"><strong>Value:</strong> 2.16</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> MPI Penetration %</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-chart9">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chart9" aria-expanded="false" aria-controls="chart9">
                    Menu Penetration Percentage(chart_1317) <span class="ms-3 badge badge-has-failures">Has Failures</span>
                    <small class="ms-2 text-muted">(3 comparisons)</small>
                </button>
            </h2>
            <div id="chart9" class="accordion-collapse collapse" aria-labelledby="heading-chart9" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Menu Count</strong> (Menu Penetration Percentage (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Menu Penetration Percentage (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 2.34</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Menu Count</div>
                            <div class="field-value"><strong>Value:</strong> 22.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Menu Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Total RO Count</strong> (Menu Penetration Percentage (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Menu Penetration Percentage (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 2.34</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> 661.0</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Total RO Count</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header">
                    <strong>Menu Penetration %</strong> (Menu Penetration Percentage (2025-07-01)) <span class="ms-2 badge badge-fail">Failed</span>
                </div>
                <div class="card-body">
                    <div class="comparison-section">
                        <div class="chart-info">
                            <div class="section-title">Chart Information:</div>
                            <div class="field-value"><strong>Line Name:</strong> Menu Penetration Percentage (2025-07-01)</div>
                            <div class="field-value"><strong>UI Tooltip Value:</strong> 2.34</div>
                            <div class="field-value"><strong>DB Tooltip Value:</strong> Missing</div>
                        </div>
                        <div class="match-status">
                            <div class="section-title">Match Status:</div>
                            <span class="match-indicator match-false">
                                ✗ MISMATCH
                            </span>
                        </div>
                    </div>
                    
                    <div class="value-comparison">
                        <div class="ui-extracted">
                            <div class="section-title">UI Extracted Value:</div>
                            <div class="field-value"><strong>Field:</strong> Menu Penetration %</div>
                            <div class="field-value"><strong>Value:</strong> 3.33</div>
                        </div>
                        <div class="db-calculated">
                            <div class="section-title">DB Calculated Value:</div>
                            <div class="field-value"><strong>Field:</strong> Menu Penetration %</div>
                            <div class="field-value"><strong>Value:</strong> Missing</div>
                        </div>
                    </div>
                </div>
            </div>
            
                </div>
            </div>
        </div>
        
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    