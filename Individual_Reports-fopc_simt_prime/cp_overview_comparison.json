{"tenant": "fopc_simt_prime", "store": "Carriage Kia of Woodstock", "role": "Admin", "generatedAt": "2025-09-19T14:58:55.707899", "results": [{"chart_name_with_id": "Revenue - Shop Supplies(chart_1239)", "line_name_legend": "Customer Pay (2025-07-01)", "drilldown_field": "RO Count", "match": false, "ui": {"line_value": 59116.59, "drilldown_value": 592.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Revenue - Shop Supplies(chart_1239)", "line_name_legend": "Customer Pay (2025-07-01)", "drilldown_field": "Shop Supplies - Customer Pay", "match": false, "ui": {"line_value": 59116.59, "drilldown_value": 59116.59}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Revenue - Shop Supplies(chart_1239)", "line_name_legend": "Internal (2025-07-01)", "drilldown_field": "RO Count", "match": false, "ui": {"line_value": 1531.18, "drilldown_value": 10.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Revenue - Shop Supplies(chart_1239)", "line_name_legend": "Internal (2025-07-01)", "drilldown_field": "Shop Supplies - Internal", "match": false, "ui": {"line_value": 1531.18, "drilldown_value": 1531.18}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Maintenance (2025-07-01)", "drilldown_field": "Labor Sale - Maintenance", "match": false, "ui": {"line_value": 0.91, "drilldown_value": 104115.89}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Maintenance (2025-07-01)", "drilldown_field": "Parts Sale - Maintenance", "match": false, "ui": {"line_value": 0.91, "drilldown_value": 94490.54}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Maintenance (2025-07-01)", "drilldown_field": "Parts To Labor Ratio - Maintenance", "match": false, "ui": {"line_value": 0.91, "drilldown_value": 0.91}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Revenue - Shop Supplies(chart_1239)", "line_name_legend": "Warranty (2025-07-01)", "drilldown_field": "RO Count", "match": false, "ui": {"line_value": 73.23, "drilldown_value": 3.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Revenue - Shop Supplies(chart_1239)", "line_name_legend": "Warranty (2025-07-01)", "drilldown_field": "Shop Supplies - Warranty", "match": false, "ui": {"line_value": 73.23, "drilldown_value": 73.23}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Maintenance Plan (2025-07-01)", "drilldown_field": "Labor Sold Hours - All Categories", "match": false, "ui": {"line_value": 0.08, "drilldown_value": 1570.1}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Maintenance Plan (2025-07-01)", "drilldown_field": "Labor Sold Hours - Maintenance", "match": false, "ui": {"line_value": 0.08, "drilldown_value": 133.2}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Labor Sold Hours Percentage By Pay Type(chart_935)", "line_name_legend": "Maintenance Plan (2025-07-01)", "drilldown_field": "Labor Sold Hours % - Maintenance", "match": false, "ui": {"line_value": 0.08, "drilldown_value": 8.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Single line Mileage Under 60k (2025-07-01)", "drilldown_field": "Mileage Under 60K", "match": false, "ui": {"line_value": 309.0, "drilldown_value": 309.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Single line Mileage Under 60k (2025-07-01)", "drilldown_field": "Labor Sale", "match": false, "ui": {"line_value": 309.0, "drilldown_value": 30079.29}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Single line Mileage Under 60k (2025-07-01)", "drilldown_field": "Labor Sold Hours", "match": false, "ui": {"line_value": 309.0, "drilldown_value": 244.9}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Single line Mileage Over 60k (2025-07-01)", "drilldown_field": "Mileage Over 60K", "match": false, "ui": {"line_value": 110.0, "drilldown_value": 110.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Single line Mileage Over 60k (2025-07-01)", "drilldown_field": "Labor Sale", "match": false, "ui": {"line_value": 110.0, "drilldown_value": 15779.54}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Single line Mileage Over 60k (2025-07-01)", "drilldown_field": "Labor Sold Hours", "match": false, "ui": {"line_value": 110.0, "drilldown_value": 104.3}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Single line Total Shop (2025-07-01)", "drilldown_field": "Total Shop", "match": false, "ui": {"line_value": 419.0, "drilldown_value": 419.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Single line Total Shop (2025-07-01)", "drilldown_field": "Labor Sale", "match": false, "ui": {"line_value": 419.0, "drilldown_value": 45858.83}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count(chart_948)", "line_name_legend": "Single line Total Shop (2025-07-01)", "drilldown_field": "Labor Sold Hours", "match": false, "ui": {"line_value": 419.0, "drilldown_value": 349.2}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count Percentage(chart_923)", "line_name_legend": "Single line Mileage Under 60K (2025-07-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 69.12751677852349, "drilldown_value": 447.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count Percentage(chart_923)", "line_name_legend": "Single line Mileage Under 60K (2025-07-01)", "drilldown_field": "1 Line Count", "match": false, "ui": {"line_value": 69.12751677852349, "drilldown_value": 309.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count Percentage(chart_923)", "line_name_legend": "Single line Mileage Under 60K (2025-07-01)", "drilldown_field": "Mileage Under 60K", "match": false, "ui": {"line_value": 69.12751677852349, "drilldown_value": 69.13}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count Percentage(chart_923)", "line_name_legend": "Single line Mileage Over 60K (2025-07-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 51.401869158878505, "drilldown_value": 214.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count Percentage(chart_923)", "line_name_legend": "Single line Mileage Over 60K (2025-07-01)", "drilldown_field": "1 Line Count", "match": false, "ui": {"line_value": 51.401869158878505, "drilldown_value": 110.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count Percentage(chart_923)", "line_name_legend": "Single line Mileage Over 60K (2025-07-01)", "drilldown_field": "Mileage Over 60K", "match": false, "ui": {"line_value": 51.401869158878505, "drilldown_value": 51.4}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count Percentage(chart_923)", "line_name_legend": "Single line Total Shop perc (2025-07-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 63.38880484114977, "drilldown_value": 661.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count Percentage(chart_923)", "line_name_legend": "Single line Total Shop perc (2025-07-01)", "drilldown_field": "1 Line Count", "match": false, "ui": {"line_value": 63.38880484114977, "drilldown_value": 419.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP 1-Line-RO Count Percentage(chart_923)", "line_name_legend": "Single line Total Shop perc (2025-07-01)", "drilldown_field": "Total Shop", "match": false, "ui": {"line_value": 63.38880484114977, "drilldown_value": 63.39}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Multi line Mileage Under 60K (2025-07-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 30.87248322147651, "drilldown_value": 447.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Multi line Mileage Under 60K (2025-07-01)", "drilldown_field": "1 Line Count", "match": false, "ui": {"line_value": 30.87248322147651, "drilldown_value": 138.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Multi line Mileage Under 60K (2025-07-01)", "drilldown_field": "Mileage Under 60K", "match": false, "ui": {"line_value": 30.87248322147651, "drilldown_value": 30.87}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Multi line Mileage Over 60K (2025-07-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 48.598130841121495, "drilldown_value": 214.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Multi line Mileage Over 60K (2025-07-01)", "drilldown_field": "1 Line Count", "match": false, "ui": {"line_value": 48.598130841121495, "drilldown_value": 104.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Multi line Mileage Over 60K (2025-07-01)", "drilldown_field": "Mileage Over 60K", "match": false, "ui": {"line_value": 48.598130841121495, "drilldown_value": 48.6}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count(chart_1354)", "line_name_legend": "Multi line Total Shop (2025-07-01)", "drilldown_field": "Total Shop", "match": false, "ui": {"line_value": 242.0, "drilldown_value": 242.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Multi line Total Shop perc (2025-07-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 36.61119515885023, "drilldown_value": 661.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Multi line Total Shop perc (2025-07-01)", "drilldown_field": "1 Line Count", "match": false, "ui": {"line_value": 36.61119515885023, "drilldown_value": 242.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Multi-Line-RO Count Percentage(chart_1355)", "line_name_legend": "Multi line Total Shop perc (2025-07-01)", "drilldown_field": "Total Shop", "match": false, "ui": {"line_value": 36.61119515885023, "drilldown_value": 15.73}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Competitive (2025-07-01)", "drilldown_field": "Labor Sale - Competitive", "match": false, "ui": {"line_value": 1.93, "drilldown_value": 3372.75}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Competitive (2025-07-01)", "drilldown_field": "Parts Sale - Competitive", "match": false, "ui": {"line_value": 1.93, "drilldown_value": 6495.5}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Competitive (2025-07-01)", "drilldown_field": "Parts To Labor Ratio - Competitive", "match": false, "ui": {"line_value": 1.93, "drilldown_value": 1.93}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Repair (2025-07-01)", "drilldown_field": "Labor Sale - Repair", "match": false, "ui": {"line_value": 1.02, "drilldown_value": 26993.11}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Repair (2025-07-01)", "drilldown_field": "Parts Sale - Repair", "match": false, "ui": {"line_value": 1.02, "drilldown_value": 27540.1}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio By Category(chart_936)", "line_name_legend": "Repair (2025-07-01)", "drilldown_field": "Parts To Labor Ratio - Repair", "match": false, "ui": {"line_value": 1.02, "drilldown_value": 1.02}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio(chart_930)", "line_name_legend": "Parts to Labor Ratio (2025-07-01)", "drilldown_field": "Labor Sale - Customer Pay", "match": false, "ui": {"line_value": 0.96, "drilldown_value": 134481.75}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio(chart_930)", "line_name_legend": "Parts to Labor Ratio (2025-07-01)", "drilldown_field": "Total Parts Sale", "match": false, "ui": {"line_value": 0.96, "drilldown_value": 128526.14}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "CP Parts to Labor Ratio(chart_930)", "line_name_legend": "Parts to Labor Ratio (2025-07-01)", "drilldown_field": "Parts To Labor Ratio", "match": false, "ui": {"line_value": 0.96, "drilldown_value": 0.96}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "MPI Penetration Percentage(chart_1316)", "line_name_legend": "MPI Penetration Percentage (2025-07-01)", "drilldown_field": "MPI Count", "match": false, "ui": {"line_value": 2.34, "drilldown_value": 22.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "MPI Penetration Percentage(chart_1316)", "line_name_legend": "MPI Penetration Percentage (2025-07-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 2.34, "drilldown_value": 1017.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "MPI Penetration Percentage(chart_1316)", "line_name_legend": "MPI Penetration Percentage (2025-07-01)", "drilldown_field": "MPI Penetration %", "match": false, "ui": {"line_value": 2.34, "drilldown_value": 2.16}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Menu Penetration Percentage(chart_1317)", "line_name_legend": "Menu Penetration Percentage (2025-07-01)", "drilldown_field": "<PERSON><PERSON>", "match": false, "ui": {"line_value": 2.34, "drilldown_value": 22.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Menu Penetration Percentage(chart_1317)", "line_name_legend": "Menu Penetration Percentage (2025-07-01)", "drilldown_field": "Total RO Count", "match": false, "ui": {"line_value": 2.34, "drilldown_value": 661.0}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}, {"chart_name_with_id": "Menu Penetration Percentage(chart_1317)", "line_name_legend": "Menu Penetration Percentage (2025-07-01)", "drilldown_field": "Menu Penetration %", "match": false, "ui": {"line_value": 2.34, "drilldown_value": 3.33}, "db": {"line_value": "Missing", "drilldown_value": "Missing"}}]}