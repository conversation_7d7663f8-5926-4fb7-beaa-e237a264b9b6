<div id="returnRateDrilldown" class="ag-theme-balham" style="height: 129px; width: 1060px; margin: 8px; display: block;"><div style="height: 100%;"><div ref="eRootWrapper" class="ag-root-wrapper ag-layout-normal ag-ltr ag-keyboard-focus">
                <!--AG-GRID-HEADER-DROP-ZONES--><div class="ag-column-drop-wrapper"><div class="ag-unselectable ag-column-drop ag-column-drop-horizontal ag-column-drop-empty ag-hidden"><div class="ag-column-drop-title-bar ag-column-drop-horizontal-title-bar"><span class="ag-icon ag-icon-group ag-column-drop-icon ag-column-drop-horizontal-icon" unselectable="on"></span></div><div class="ag-column-drop-list ag-column-drop-horizontal-list"><span class="ag-column-drop-empty-message ag-column-drop-horizontal-empty-message">Drag here to set row groups</span></div></div><div class="ag-unselectable ag-column-drop ag-column-drop-horizontal ag-column-drop-empty ag-hidden"><div class="ag-column-drop-title-bar ag-column-drop-horizontal-title-bar"><span class="ag-icon ag-icon-pivot ag-column-drop-icon ag-column-drop-horizontal-icon" unselectable="on"></span></div><div class="ag-column-drop-list ag-column-drop-horizontal-list"><span class="ag-column-drop-empty-message ag-column-drop-horizontal-empty-message">Drag here to set column labels</span></div></div></div>
                <div class="ag-root-wrapper-body ag-layout-normal ag-focus-managed" ref="rootWrapperBody"><div class="ag-tab-guard ag-tab-guard-top" tabindex="0"></div>
                    <!--AG-GRID-COMP--><div class="ag-root ag-unselectable ag-layout-normal" role="grid" unselectable="on" ref="gridPanel" aria-rowcount="3" aria-colcount="8">
        <!--AG-HEADER-ROOT--><div class="ag-header ag-focus-managed ag-pivot-off" role="presentation" ref="headerRoot" unselectable="on" style="height: 83px; min-height: 83px;">
            <div class="ag-pinned-left-header ag-hidden" ref="ePinnedLeftHeader" role="presentation" style="width: 0px; max-width: 0px; min-width: 0px;"><div class="ag-header-row ag-header-row-column" role="row" aria-rowindex="1" style="top: 0px; height: 50px; width: 0px;"></div><div class="ag-header-row ag-header-row-floating-filter" role="row" aria-rowindex="2" style="top: 50px; height: 32px; width: 0px;"></div></div>
            <div class="ag-header-viewport" ref="eHeaderViewport" role="presentation">
                <div class="ag-header-container" ref="eHeaderContainer" role="rowgroup" style="width: 1058px; transform: translateX(0px);"><div class="ag-header-row ag-header-row-column" role="row" aria-rowindex="1" style="top: 0px; height: 50px; width: 1058px;"><div class="ag-header-cell ag-focus-managed ag-header-cell-sortable" role="presentation" unselectable="on" tabindex="-1" col-id="monthYear" style="width: 100px; left: 0px;">
            <div ref="eResize" class="ag-header-cell-resize" role="presentation"></div>
            <!--AG-CHECKBOX--><div role="presentation" ref="cbSelectAll" class="ag-header-select-all ag-labeled ag-label-align-right ag-checkbox ag-input-field ag-hidden">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-checkbox-label" for="ag-input-id-19"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-checkbox-input" type="checkbox" id="ag-input-id-19" aria-label="Toggle Selection of All Rows">
            </div>
        </div>
        <div class="ag-cell-label-container ag-header-cell-sorted-none">
            
            <div ref="eLabel" class="ag-header-cell-label" role="presentation" unselectable="on">
                <span ref="eText" class="ag-header-cell-text" role="columnheader" unselectable="on" aria-colindex="1">Month</span>
                <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-filter" unselectable="on"></span></span>
                <span ref="eSortOrder" class="ag-header-icon ag-header-label-icon ag-sort-order ag-hidden" aria-hidden="true"></span>
                <span ref="eSortAsc" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-asc" unselectable="on"></span></span>
                <span ref="eSortDesc" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-desc" unselectable="on"></span></span>
                <span ref="eSortNone" class="ag-header-icon ag-header-label-icon ag-sort-none-icon" aria-hidden="true"><span class="ag-icon ag-icon-none" unselectable="on"></span></span>
            </div>
        </div></div><div class="ag-header-cell ag-focus-managed ag-header-cell-sortable" role="presentation" unselectable="on" tabindex="-1" col-id="rodate" style="width: 120px; left: 100px;">
            <div ref="eResize" class="ag-header-cell-resize" role="presentation"></div>
            <!--AG-CHECKBOX--><div role="presentation" ref="cbSelectAll" class="ag-header-select-all ag-labeled ag-label-align-right ag-checkbox ag-input-field ag-hidden">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-checkbox-label" for="ag-input-id-22"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-checkbox-input" type="checkbox" id="ag-input-id-22" aria-label="Toggle Selection of All Rows">
            </div>
        </div>
        <div class="ag-cell-label-container ag-header-cell-sorted-none">
            
            <div ref="eLabel" class="ag-header-cell-label" role="presentation" unselectable="on">
                <span ref="eText" class="ag-header-cell-text" role="columnheader" unselectable="on" aria-colindex="2">RO Closed Date</span>
                <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-filter" unselectable="on"></span></span>
                <span ref="eSortOrder" class="ag-header-icon ag-header-label-icon ag-sort-order ag-hidden" aria-hidden="true"></span>
                <span ref="eSortAsc" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-asc" unselectable="on"></span></span>
                <span ref="eSortDesc" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-desc" unselectable="on"></span></span>
                <span ref="eSortNone" class="ag-header-icon ag-header-label-icon ag-sort-none-icon" aria-hidden="true"><span class="ag-icon ag-icon-none" unselectable="on"></span></span>
            </div>
        </div></div><div class="ag-header-cell ag-focus-managed ag-header-cell-sortable" role="presentation" unselectable="on" tabindex="-1" col-id="totalVinsInLastSixMonths" style="width: 135px; left: 220px;">
            <div ref="eResize" class="ag-header-cell-resize" role="presentation"></div>
            <!--AG-CHECKBOX--><div role="presentation" ref="cbSelectAll" class="ag-header-select-all ag-labeled ag-label-align-right ag-checkbox ag-input-field ag-hidden">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-checkbox-label" for="ag-input-id-28"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-checkbox-input" type="checkbox" id="ag-input-id-28" aria-label="Toggle Selection of All Rows">
            </div>
        </div>
        <div class="ag-cell-label-container ag-header-cell-sorted-none">
            
            <div ref="eLabel" class="ag-header-cell-label" role="presentation" unselectable="on">
                <span ref="eText" class="ag-header-cell-text" role="columnheader" unselectable="on" aria-colindex="3">Vin Count
6 Months</span>
                <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-filter" unselectable="on"></span></span>
                <span ref="eSortOrder" class="ag-header-icon ag-header-label-icon ag-sort-order ag-hidden" aria-hidden="true"></span>
                <span ref="eSortAsc" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-asc" unselectable="on"></span></span>
                <span ref="eSortDesc" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-desc" unselectable="on"></span></span>
                <span ref="eSortNone" class="ag-header-icon ag-header-label-icon ag-sort-none-icon" aria-hidden="true"><span class="ag-icon ag-icon-none" unselectable="on"></span></span>
            </div>
        </div></div><div class="ag-header-cell ag-focus-managed ag-header-cell-sortable" role="presentation" unselectable="on" tabindex="-1" col-id="totalVinsInLastTwelveMonths" style="width: 135px; left: 355px;">
            <div ref="eResize" class="ag-header-cell-resize" role="presentation"></div>
            <!--AG-CHECKBOX--><div role="presentation" ref="cbSelectAll" class="ag-header-select-all ag-labeled ag-label-align-right ag-checkbox ag-input-field ag-hidden">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-checkbox-label" for="ag-input-id-31"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-checkbox-input" type="checkbox" id="ag-input-id-31" aria-label="Toggle Selection of All Rows">
            </div>
        </div>
        <div class="ag-cell-label-container ag-header-cell-sorted-none">
            
            <div ref="eLabel" class="ag-header-cell-label" role="presentation" unselectable="on">
                <span ref="eText" class="ag-header-cell-text" role="columnheader" unselectable="on" aria-colindex="4">Vin Count
12 Months</span>
                <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-filter" unselectable="on"></span></span>
                <span ref="eSortOrder" class="ag-header-icon ag-header-label-icon ag-sort-order ag-hidden" aria-hidden="true"></span>
                <span ref="eSortAsc" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-asc" unselectable="on"></span></span>
                <span ref="eSortDesc" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-desc" unselectable="on"></span></span>
                <span ref="eSortNone" class="ag-header-icon ag-header-label-icon ag-sort-none-icon" aria-hidden="true"><span class="ag-icon ag-icon-none" unselectable="on"></span></span>
            </div>
        </div></div><div class="ag-header-cell ag-focus-managed ag-header-cell-sortable" role="presentation" unselectable="on" tabindex="-1" col-id="lastSixMonVinsRevInCurrMon" style="width: 150px; left: 490px;">
            <div ref="eResize" class="ag-header-cell-resize" role="presentation"></div>
            <!--AG-CHECKBOX--><div role="presentation" ref="cbSelectAll" class="ag-header-select-all ag-labeled ag-label-align-right ag-checkbox ag-input-field ag-hidden">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-checkbox-label" for="ag-input-id-34"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-checkbox-input" type="checkbox" id="ag-input-id-34" aria-label="Toggle Selection of All Rows">
            </div>
        </div>
        <div class="ag-cell-label-container ag-header-cell-sorted-none">
            
            <div ref="eLabel" class="ag-header-cell-label" role="presentation" unselectable="on">
                <span ref="eText" class="ag-header-cell-text" role="columnheader" unselectable="on" aria-colindex="5">Vin Count
Last 6 Months</span>
                <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-filter" unselectable="on"></span></span>
                <span ref="eSortOrder" class="ag-header-icon ag-header-label-icon ag-sort-order ag-hidden" aria-hidden="true"></span>
                <span ref="eSortAsc" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-asc" unselectable="on"></span></span>
                <span ref="eSortDesc" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-desc" unselectable="on"></span></span>
                <span ref="eSortNone" class="ag-header-icon ag-header-label-icon ag-sort-none-icon" aria-hidden="true"><span class="ag-icon ag-icon-none" unselectable="on"></span></span>
            </div>
        </div></div><div class="ag-header-cell ag-focus-managed ag-header-cell-sortable" role="presentation" unselectable="on" tabindex="-1" col-id="lastTwelveMonVinsRevInCurrMon" style="width: 150px; left: 640px;">
            <div ref="eResize" class="ag-header-cell-resize" role="presentation"></div>
            <!--AG-CHECKBOX--><div role="presentation" ref="cbSelectAll" class="ag-header-select-all ag-labeled ag-label-align-right ag-checkbox ag-input-field ag-hidden">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-checkbox-label" for="ag-input-id-37"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-checkbox-input" type="checkbox" id="ag-input-id-37" aria-label="Toggle Selection of All Rows">
            </div>
        </div>
        <div class="ag-cell-label-container ag-header-cell-sorted-none">
            
            <div ref="eLabel" class="ag-header-cell-label" role="presentation" unselectable="on">
                <span ref="eText" class="ag-header-cell-text" role="columnheader" unselectable="on" aria-colindex="6">Vin Count
Last 12 Months</span>
                <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-filter" unselectable="on"></span></span>
                <span ref="eSortOrder" class="ag-header-icon ag-header-label-icon ag-sort-order ag-hidden" aria-hidden="true"></span>
                <span ref="eSortAsc" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-asc" unselectable="on"></span></span>
                <span ref="eSortDesc" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-desc" unselectable="on"></span></span>
                <span ref="eSortNone" class="ag-header-icon ag-header-label-icon ag-sort-none-icon" aria-hidden="true"><span class="ag-icon ag-icon-none" unselectable="on"></span></span>
            </div>
        </div></div><div class="ag-header-cell ag-focus-managed ag-header-cell-sortable" role="presentation" unselectable="on" tabindex="-1" col-id="sixMonthReturnrate" style="width: 135px; left: 790px;">
            <div ref="eResize" class="ag-header-cell-resize" role="presentation"></div>
            <!--AG-CHECKBOX--><div role="presentation" ref="cbSelectAll" class="ag-header-select-all ag-labeled ag-label-align-right ag-checkbox ag-input-field ag-hidden">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-checkbox-label" for="ag-input-id-40"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-checkbox-input" type="checkbox" id="ag-input-id-40" aria-label="Toggle Selection of All Rows">
            </div>
        </div>
        <div class="ag-cell-label-container ag-header-cell-sorted-none">
            
            <div ref="eLabel" class="ag-header-cell-label" role="presentation" unselectable="on">
                <span ref="eText" class="ag-header-cell-text" role="columnheader" unselectable="on" aria-colindex="7">6 Month Return Rate</span>
                <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-filter" unselectable="on"></span></span>
                <span ref="eSortOrder" class="ag-header-icon ag-header-label-icon ag-sort-order ag-hidden" aria-hidden="true"></span>
                <span ref="eSortAsc" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-asc" unselectable="on"></span></span>
                <span ref="eSortDesc" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-desc" unselectable="on"></span></span>
                <span ref="eSortNone" class="ag-header-icon ag-header-label-icon ag-sort-none-icon" aria-hidden="true"><span class="ag-icon ag-icon-none" unselectable="on"></span></span>
            </div>
        </div></div><div class="ag-header-cell ag-focus-managed ag-header-cell-sortable" role="presentation" unselectable="on" tabindex="-1" col-id="twelveMonthReturnrate" style="width: 133px; left: 925px;">
            <div ref="eResize" class="ag-header-cell-resize" role="presentation"></div>
            <!--AG-CHECKBOX--><div role="presentation" ref="cbSelectAll" class="ag-header-select-all ag-labeled ag-label-align-right ag-checkbox ag-input-field ag-hidden">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-checkbox-label" for="ag-input-id-43"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-checkbox-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-checkbox-input" type="checkbox" id="ag-input-id-43" aria-label="Toggle Selection of All Rows">
            </div>
        </div>
        <div class="ag-cell-label-container ag-header-cell-sorted-none">
            
            <div ref="eLabel" class="ag-header-cell-label" role="presentation" unselectable="on">
                <span ref="eText" class="ag-header-cell-text" role="columnheader" unselectable="on" aria-colindex="8">12 Month Return Rate</span>
                <span ref="eFilter" class="ag-header-icon ag-header-label-icon ag-filter-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-filter" unselectable="on"></span></span>
                <span ref="eSortOrder" class="ag-header-icon ag-header-label-icon ag-sort-order ag-hidden" aria-hidden="true"></span>
                <span ref="eSortAsc" class="ag-header-icon ag-header-label-icon ag-sort-ascending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-asc" unselectable="on"></span></span>
                <span ref="eSortDesc" class="ag-header-icon ag-header-label-icon ag-sort-descending-icon ag-hidden" aria-hidden="true"><span class="ag-icon ag-icon-desc" unselectable="on"></span></span>
                <span ref="eSortNone" class="ag-header-icon ag-header-label-icon ag-sort-none-icon" aria-hidden="true"><span class="ag-icon ag-icon-none" unselectable="on"></span></span>
            </div>
        </div></div></div><div class="ag-header-row ag-header-row-floating-filter" role="row" aria-rowindex="2" style="top: 50px; height: 32px; width: 1058px;"><div class="ag-header-cell ag-focus-managed" role="presentation" tabindex="-1" style="width: 100px; left: 0px;">
            <div ref="eFloatingFilterBody" role="columnheader" class="ag-floating-filter-body" aria-colindex="1"><div class="ag-floating-filter-input" role="presentation">
                <!--AG-INPUT-TEXT-FIELD--><div role="presentation" ref="eFloatingFilterText" class="ag-labeled ag-label-align-left ag-text-field ag-input-field ag-disabled" disabled="">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-text-field-label" for="ag-input-id-47"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-text-field-input" type="text" id="ag-input-id-47" disabled="" aria-label="Month Filter Input">
            </div>
        </div>
            </div></div>
            <div class="ag-floating-filter-button" ref="eButtonWrapper" role="presentation">
                <button type="button" aria-label="Open Filter Menu" class="ag-floating-filter-button-button" ref="eButtonShowMainFilter" tabindex="-1"><span class="ag-icon ag-icon-filter" unselectable="on"></span></button>
            </div>
        </div><div class="ag-header-cell ag-focus-managed" role="presentation" tabindex="-1" style="width: 120px; left: 100px;">
            <div ref="eFloatingFilterBody" role="columnheader" class="ag-floating-filter-body" aria-colindex="2"><div class="ag-floating-filter-input" role="presentation">
                <!--AG-INPUT-TEXT-FIELD--><div role="presentation" ref="eFloatingFilterText" class="ag-labeled ag-label-align-left ag-text-field ag-input-field ag-disabled" disabled="">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-text-field-label" for="ag-input-id-50"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-text-field-input" type="text" id="ag-input-id-50" disabled="" aria-label="RO Closed Date Filter Input">
            </div>
        </div>
            </div></div>
            <div class="ag-floating-filter-button" ref="eButtonWrapper" role="presentation">
                <button type="button" aria-label="Open Filter Menu" class="ag-floating-filter-button-button" ref="eButtonShowMainFilter" tabindex="-1"><span class="ag-icon ag-icon-filter" unselectable="on"></span></button>
            </div>
        </div><div class="ag-header-cell ag-focus-managed" role="presentation" tabindex="-1" style="width: 135px; left: 220px;">
            <div ref="eFloatingFilterBody" role="columnheader" class="ag-floating-filter-body" aria-colindex="3"><div class="ag-floating-filter-input" role="presentation">
                <!--AG-INPUT-TEXT-FIELD--><div role="presentation" ref="eFloatingFilterText" class="ag-labeled ag-label-align-left ag-text-field ag-input-field ag-disabled" disabled="">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-text-field-label" for="ag-input-id-56"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-text-field-input" type="text" id="ag-input-id-56" disabled="" aria-label="Vin Count
6 Months Filter Input">
            </div>
        </div>
            </div></div>
            <div class="ag-floating-filter-button" ref="eButtonWrapper" role="presentation">
                <button type="button" aria-label="Open Filter Menu" class="ag-floating-filter-button-button" ref="eButtonShowMainFilter" tabindex="-1"><span class="ag-icon ag-icon-filter" unselectable="on"></span></button>
            </div>
        </div><div class="ag-header-cell ag-focus-managed" role="presentation" tabindex="-1" style="width: 135px; left: 355px;">
            <div ref="eFloatingFilterBody" role="columnheader" class="ag-floating-filter-body" aria-colindex="4"><div class="ag-floating-filter-input" role="presentation">
                <!--AG-INPUT-TEXT-FIELD--><div role="presentation" ref="eFloatingFilterText" class="ag-labeled ag-label-align-left ag-text-field ag-input-field ag-disabled" disabled="">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-text-field-label" for="ag-input-id-59"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-text-field-input" type="text" id="ag-input-id-59" disabled="" aria-label="Vin Count
12 Months Filter Input">
            </div>
        </div>
            </div></div>
            <div class="ag-floating-filter-button" ref="eButtonWrapper" role="presentation">
                <button type="button" aria-label="Open Filter Menu" class="ag-floating-filter-button-button" ref="eButtonShowMainFilter" tabindex="-1"><span class="ag-icon ag-icon-filter" unselectable="on"></span></button>
            </div>
        </div><div class="ag-header-cell ag-focus-managed" role="presentation" tabindex="-1" style="width: 150px; left: 490px;">
            <div ref="eFloatingFilterBody" role="columnheader" class="ag-floating-filter-body" aria-colindex="5"><div class="ag-floating-filter-input" role="presentation">
                <!--AG-INPUT-TEXT-FIELD--><div role="presentation" ref="eFloatingFilterText" class="ag-labeled ag-label-align-left ag-text-field ag-input-field ag-disabled" disabled="">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-text-field-label" for="ag-input-id-62"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-text-field-input" type="text" id="ag-input-id-62" disabled="" aria-label="Vin Count
Last 6 Months Filter Input">
            </div>
        </div>
            </div></div>
            <div class="ag-floating-filter-button" ref="eButtonWrapper" role="presentation">
                <button type="button" aria-label="Open Filter Menu" class="ag-floating-filter-button-button" ref="eButtonShowMainFilter" tabindex="-1"><span class="ag-icon ag-icon-filter" unselectable="on"></span></button>
            </div>
        </div><div class="ag-header-cell ag-focus-managed" role="presentation" tabindex="-1" style="width: 150px; left: 640px;">
            <div ref="eFloatingFilterBody" role="columnheader" class="ag-floating-filter-body" aria-colindex="6"><div class="ag-floating-filter-input" role="presentation">
                <!--AG-INPUT-TEXT-FIELD--><div role="presentation" ref="eFloatingFilterText" class="ag-labeled ag-label-align-left ag-text-field ag-input-field ag-disabled" disabled="">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-text-field-label" for="ag-input-id-65"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-text-field-input" type="text" id="ag-input-id-65" disabled="" aria-label="Vin Count
Last 12 Months Filter Input">
            </div>
        </div>
            </div></div>
            <div class="ag-floating-filter-button" ref="eButtonWrapper" role="presentation">
                <button type="button" aria-label="Open Filter Menu" class="ag-floating-filter-button-button" ref="eButtonShowMainFilter" tabindex="-1"><span class="ag-icon ag-icon-filter" unselectable="on"></span></button>
            </div>
        </div><div class="ag-header-cell ag-focus-managed" role="presentation" tabindex="-1" style="width: 135px; left: 790px;">
            <div ref="eFloatingFilterBody" role="columnheader" class="ag-floating-filter-body" aria-colindex="7"><div class="ag-floating-filter-input" role="presentation">
                <!--AG-INPUT-TEXT-FIELD--><div role="presentation" ref="eFloatingFilterText" class="ag-labeled ag-label-align-left ag-text-field ag-input-field ag-disabled" disabled="">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-text-field-label" for="ag-input-id-68"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-text-field-input" type="text" id="ag-input-id-68" disabled="" aria-label="6 Month Return Rate Filter Input">
            </div>
        </div>
            </div></div>
            <div class="ag-floating-filter-button" ref="eButtonWrapper" role="presentation">
                <button type="button" aria-label="Open Filter Menu" class="ag-floating-filter-button-button" ref="eButtonShowMainFilter" tabindex="-1"><span class="ag-icon ag-icon-filter" unselectable="on"></span></button>
            </div>
        </div><div class="ag-header-cell ag-focus-managed" role="presentation" tabindex="-1" style="width: 133px; left: 925px;">
            <div ref="eFloatingFilterBody" role="columnheader" class="ag-floating-filter-body" aria-colindex="8"><div class="ag-floating-filter-input" role="presentation">
                <!--AG-INPUT-TEXT-FIELD--><div role="presentation" ref="eFloatingFilterText" class="ag-labeled ag-label-align-left ag-text-field ag-input-field ag-disabled" disabled="">
            <label ref="eLabel" class="ag-input-field-label ag-label ag-hidden ag-text-field-label" for="ag-input-id-71"></label>
            <div ref="eWrapper" class="ag-wrapper ag-input-wrapper ag-text-field-input-wrapper" role="presentation">
                <input ref="eInput" class="ag-input-field-input ag-text-field-input" type="text" id="ag-input-id-71" disabled="" aria-label="12 Month Return Rate Filter Input">
            </div>
        </div>
            </div></div>
            <div class="ag-floating-filter-button" ref="eButtonWrapper" role="presentation">
                <button type="button" aria-label="Open Filter Menu" class="ag-floating-filter-button-button" ref="eButtonShowMainFilter" tabindex="-1"><span class="ag-icon ag-icon-filter" unselectable="on"></span></button>
            </div>
        </div></div></div>
            </div>
            <div class="ag-pinned-right-header ag-hidden" ref="ePinnedRightHeader" role="presentation" style="width: 0px; max-width: 0px; min-width: 0px;"><div class="ag-header-row ag-header-row-column" role="row" aria-rowindex="1" style="top: 0px; height: 50px; width: 0px;"></div><div class="ag-header-row ag-header-row-floating-filter" role="row" aria-rowindex="2" style="top: 50px; height: 32px; width: 0px;"></div></div>
        </div>
        <div class="ag-floating-top" ref="eTop" role="presentation" unselectable="on" style="min-height: 0px; height: 0px; display: none; overflow-y: hidden;">
            <div class="ag-pinned-left-floating-top ag-hidden" ref="eLeftTop" role="presentation" unselectable="on"></div>
            <div class="ag-floating-top-viewport" ref="eTopViewport" role="presentation" unselectable="on">
                <div class="ag-floating-top-container" ref="eTopContainer" role="presentation" unselectable="on" style="width: 1058px; transform: translateX(0px);"></div>
            </div>
            <div class="ag-pinned-right-floating-top ag-hidden" ref="eRightTop" role="presentation" unselectable="on"></div>
            <div class="ag-floating-top-full-width-container ag-hidden" ref="eTopFullWidthContainer" role="presentation" unselectable="on"></div>
        </div>
        <div class="ag-body-viewport ag-layout-normal ag-row-animation" ref="eBodyViewport" role="presentation">
            <div class="ag-pinned-left-cols-container ag-hidden" ref="eLeftContainer" role="presentation" unselectable="on" style="height: 28px;"><div role="row" row-index="0" aria-rowindex="3" row-id="0" comp-id="182" class="ag-row ag-row-no-focus ag-row-even ag-row-level-0 ag-row-position-absolute ag-row-first ag-row-last" style="height: 28px; transform: translateY(0px); "></div></div>
            <div class="ag-center-cols-clipper" ref="eCenterColsClipper" role="presentation" unselectable="on" style="height: 28px;">
                <div class="ag-center-cols-viewport" ref="eCenterViewport" role="presentation" style="height: calc(100% + 0px);">
                    <div class="ag-center-cols-container" ref="eCenterContainer" role="rowgroup" unselectable="on" style="width: 1058px; height: 28px;"><div role="row" row-index="0" aria-rowindex="3" row-id="0" comp-id="182" class="ag-row ag-row-no-focus ag-row-even ag-row-level-0 ag-row-position-absolute ag-row-first ag-row-last" style="height: 28px; transform: translateY(0px); "><div tabindex="-1" unselectable="on" role="gridcell" aria-colindex="1" comp-id="183" col-id="monthYear" class="ag-cell ag-cell-not-inline-editing ag-cell-auto-height greenBackground ag-cell-value" style="width: 100px; left: 0px; border:  0px white; ">08/24</div><div tabindex="-1" unselectable="on" role="gridcell" aria-colindex="2" comp-id="184" col-id="rodate" class="ag-cell ag-cell-not-inline-editing ag-cell-auto-height dateUS greenBackground ag-cell-value" style="width: 120px; left: 100px; border:  0px white; ">08/01/24</div><div tabindex="-1" unselectable="on" role="gridcell" aria-colindex="3" comp-id="185" col-id="totalVinsInLastSixMonths" class="ag-cell ag-cell-not-inline-editing ag-cell-auto-height greenBackground ag-cell-value" style="width: 135px; left: 220px; text-align: center; border:  0px white; white-space: normal !important; ">4706</div><div tabindex="-1" unselectable="on" role="gridcell" aria-colindex="4" comp-id="186" col-id="totalVinsInLastTwelveMonths" class="ag-cell ag-cell-not-inline-editing ag-cell-auto-height greenBackground ag-cell-value" style="width: 135px; left: 355px; text-align: center; border:  0px white; white-space: normal !important; ">6955</div><div tabindex="-1" unselectable="on" role="gridcell" aria-colindex="5" comp-id="187" col-id="lastSixMonVinsRevInCurrMon" class="ag-cell ag-cell-not-inline-editing ag-cell-auto-height greenBackground ag-cell-value" style="width: 150px; left: 490px; text-align: center; border:  0px white; white-space: normal !important; ">423</div><div tabindex="-1" unselectable="on" role="gridcell" aria-colindex="6" comp-id="188" col-id="lastTwelveMonVinsRevInCurrMon" class="ag-cell ag-cell-not-inline-editing ag-cell-auto-height greenBackground ag-cell-value" style="width: 150px; left: 640px; text-align: center; border:  0px white; white-space: normal !important; ">577</div><div tabindex="-1" unselectable="on" role="gridcell" aria-colindex="7" comp-id="189" col-id="sixMonthReturnrate" class="ag-cell ag-cell-not-inline-editing ag-cell-auto-height greenBackground ag-cell-value" style="width: 135px; left: 790px; text-align: center; border:  0px white; white-space: normal !important; ">8.99</div><div tabindex="-1" unselectable="on" role="gridcell" aria-colindex="8" comp-id="190" col-id="twelveMonthReturnrate" class="ag-cell ag-cell-not-inline-editing ag-cell-auto-height greenBackground ag-cell-value" style="width: 133px; left: 925px; text-align: center; border:  0px white; white-space: normal !important; ">8.30</div></div></div>
                </div>
            </div>
            <div class="ag-pinned-right-cols-container ag-hidden" ref="eRightContainer" role="presentation" unselectable="on" style="height: 28px;"><div role="row" row-index="0" aria-rowindex="3" row-id="0" comp-id="182" class="ag-row ag-row-no-focus ag-row-even ag-row-level-0 ag-row-position-absolute ag-row-first ag-row-last" style="height: 28px; transform: translateY(0px); "></div></div>
            <div class="ag-full-width-container" ref="eFullWidthContainer" role="presentation" unselectable="on" style="height: 28px;"></div>
        </div>
        <div class="ag-floating-bottom" ref="eBottom" role="presentation" unselectable="on" style="min-height: 0px; height: 0px; display: none; overflow-y: hidden;">
            <div class="ag-pinned-left-floating-bottom ag-hidden" ref="eLeftBottom" role="presentation" unselectable="on"></div>
            <div class="ag-floating-bottom-viewport" ref="eBottomViewport" role="presentation" unselectable="on">
                <div class="ag-floating-bottom-container" ref="eBottomContainer" role="presentation" unselectable="on" style="width: 1058px; transform: translateX(0px);"></div>
            </div>
            <div class="ag-pinned-right-floating-bottom ag-hidden" ref="eRightBottom" role="presentation" unselectable="on"></div>
            <div class="ag-floating-bottom-full-width-container ag-hidden" ref="eBottomFullWidthContainer" role="presentation" unselectable="on"></div>
        </div>
        <div class="ag-body-horizontal-scroll" ref="eHorizontalScrollBody" aria-hidden="true" style="height: 0px; max-height: 0px; min-height: 0px;">
            <div class="ag-horizontal-left-spacer ag-scroller-corner" ref="eHorizontalLeftSpacer" style="width: 0px; max-width: 0px; min-width: 0px;"></div>
            <div class="ag-body-horizontal-scroll-viewport" ref="eBodyHorizontalScrollViewport" style="height: 0px; max-height: 0px; min-height: 0px;">
                <div class="ag-body-horizontal-scroll-container" ref="eBodyHorizontalScrollContainer" style="width: 1058px; height: 0px; max-height: 0px; min-height: 0px;"></div>
            </div>
            <div class="ag-horizontal-right-spacer ag-scroller-corner" ref="eHorizontalRightSpacer" style="width: 0px; max-width: 0px; min-width: 0px;"></div>
        </div>
        <!--AG-OVERLAY-WRAPPER--><div class="ag-overlay ag-hidden" aria-hidden="true" ref="overlayWrapper">
            <div class="ag-overlay-panel">
                <div class="ag-overlay-wrapper ag-layout-normal ag-overlay-no-rows-wrapper" ref="eOverlayWrapper"></div>
            </div>
        </div>
    </div>
                    <!--AG-SIDE-BAR--><div class="ag-side-bar ag-unselectable ag-hidden" ref="sideBar">
            <!--AG-SIDE-BAR-BUTTONS--><div class="ag-side-buttons" ref="sideBarButtons"></div>
        </div>
                <div class="ag-tab-guard ag-tab-guard-bottom" tabindex="0"></div></div>
                <!--AG-STATUS-BAR--><div class="ag-status-bar ag-hidden" ref="statusBar">
            <div ref="eStatusBarLeft" class="ag-status-bar-left"></div>
            <div ref="eStatusBarCenter" class="ag-status-bar-center"></div>
            <div ref="eStatusBarRight" class="ag-status-bar-right"></div>
        </div>
                <!--AG-PAGINATION--><div class="ag-paging-panel ag-unselectable ag-hidden">
                <span ref="eSummaryPanel" class="ag-paging-row-summary-panel">
                    <span ref="lbFirstRowOnPage" class="ag-paging-row-summary-panel-number"></span>
                    to
                    <span ref="lbLastRowOnPage" class="ag-paging-row-summary-panel-number"></span>
                    of
                    <span ref="lbRecordCount" class="ag-paging-row-summary-panel-number"></span>
                </span>
                <span class="ag-paging-page-summary-panel">
                    <div ref="btFirst" class="ag-paging-button-wrapper"><span class="ag-icon ag-icon-first" unselectable="on"></span>
                        <button type="button" class="ag-paging-button">First</button>
                    </div>
                    <div ref="btPrevious" class="ag-paging-button-wrapper"><span class="ag-icon ag-icon-previous" unselectable="on"></span>
                        <button type="button" class="ag-paging-button">Previous</button>
                    </div>
                    <span class="ag-paging-description">
                        Page
                        <span ref="lbCurrent" class="ag-paging-number"></span>
                        of
                        <span ref="lbTotal" class="ag-paging-number"></span>
                    </span>
                    <span ref="lbTotal" class="ag-paging-number"></span>
                    <div ref="btNext" class="ag-paging-button-wrapper"><span class="ag-icon ag-icon-next" unselectable="on"></span>
                        <button type="button" class="ag-paging-button">Next</button>
                    </div>
                    <div ref="btLast" class="ag-paging-button-wrapper"><span class="ag-icon ag-icon-last" unselectable="on"></span>
                        <button type="button" class="ag-paging-button">Last</button>
                    </div>
                </span>
            </div>
                <!--AG-WATERMARK--><div class="ag-watermark ag-hidden">
                    <div ref="eLicenseTextRef" class="ag-watermark-text"></div>
               </div>
            </div></div></div>