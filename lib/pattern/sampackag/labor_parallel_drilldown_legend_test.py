import asyncio
import json
import os
import time
import glob
import traceback
from datetime import datetime
from math import isfinite, isnan
from playwright.async_api import async_playwright
from concurrent.futures import ThreadPoolExecutor
import threading
from datetime import datetime
from collections import defaultdict   
import re
from decimal import Decimal, ROUND_HALF_UP
import openpyxl
import openpyxl.cell
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Alignment, Font
from openpyxl.utils import get_column_letter
import csv
import logging
from lib.std.universal.logger import logger, log_info, log_warn, log_error

# # Configure logging
# def setup_logging():
#     """Setup logging configuration to save logs to file"""
#     # Create logs directory if it doesn't exist
#     logs_dir = "logs"
#     os.makedirs(logs_dir, exist_ok=True)

#     # Create log filename with timestamp
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#     log_filename = os.path.join(logs_dir, f"cp_overview_test_{timestamp}.log")

#     # Configure logging
#     logging.basicConfig(
#         level=logging.INFO,
#         format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
#         handlers=[
#             logging.FileHandler(log_filename, encoding='utf-8'),
#             logging.StreamHandler()  # Also log to console
#         ]
#     )

#     # Create logger
#     logger = logging.getLogger(__name__)
#     log_info(f"Logging initialized. Log file: {log_filename}")
#     return logger

# # Initialize logger
# logger = setup_logging()

# Target months-years for drilling down (modify as needed)
TARGET_MONTHS_YEARS = ["2023-11-01", "Nov 2023", "November 2023", "nov", "Nov"]  # Multiple formats to increase matching chances
TARGET_MONTHS_YEAR = "2023-11-01"

# Configuration constants
MAX_CONCURRENT_BROWSERS = 4
BROWSER_TIMEOUT = 30000
AUTH_STATE_FILE = "auth_state.json"

namespace = {
    'ns': 'http://www.dmotorworks.com/service-repair-order-history'
}



class AuthManager:
    """Handles authentication for the application"""
    
    def __init__(self):
        self.auth_state = None
        self.auth_file = AUTH_STATE_FILE
    
    def get_auth_state(self):
        """Get current auth state"""
        if not self.auth_state:
            self.load_auth_state()
        return self.auth_state
    
    def load_auth_state(self):
        """Load auth state from file"""
        try:
            if os.path.exists(self.auth_file):
                with open(self.auth_file, 'r') as f:
                    self.auth_state = json.load(f)
                log_info("✅ Auth state loaded from file")
                return True
        except Exception as e:
            log_error(f"Could not load auth state: {e}")
            log_error(f"❌ Could not load auth state: {e}")
        return False
    
    def save_auth_state(self, auth_state):
        """Save auth state to file"""
        try:
            with open(self.auth_file, 'w') as f:
                json.dump(auth_state, f, indent=2)
            self.auth_state = auth_state
            log_info("✅ Auth state saved to file")
            return True
        except Exception as e:
            log_error(f"Could not save auth state: {e}", exc_info=True)            
            return False
    
    async def setup_authentication(self, playwright):
        """Setup authentication with login process"""
        log_info("🔐 Setting up authentication...")
        
        browser = await playwright.chromium.launch(
            headless=False,  # Set to True for headless mode
            args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        page = await context.new_page()
        page.set_default_timeout(BROWSER_TIMEOUT)
        
        try:
            login_success = await self.perform_login(page)
            
            if login_success:
                # Save authentication state
                auth_state = await context.storage_state()
                self.save_auth_state(auth_state)
                log_info("✅ Authentication setup completed successfully")
                return True
            else:
                log_error("❌ Authentication setup failed")
                return False
                
        finally:
            await context.close()
            await browser.close()
    
    async def perform_login(self, page, max_retries=3):
        """Perform login with retry mechanism"""
        
        for attempt in range(max_retries):
            try:
                log_info(f"🔄 Login attempt {attempt + 1}/{max_retries}")
                
                # Navigate to login page
                log_error("📍 Navigating to login page...")
                await page.goto("https://sampackag.fixedops.cc/auth/login?provenance=fopc", timeout=30000)
                await page.wait_for_load_state("networkidle")

                # Click login button
                log_error("🖱️ Clicking login button...")
                await page.wait_for_selector("button#login", timeout=10000)
                await page.click("button#login", force=True)

                # Fill credentials
                log_error("   Filling username and password...")
                await page.wait_for_selector("input[name='username']", timeout=10000)
                await page.fill("input[name='username']", "<EMAIL>")
                await page.fill("input[name='password']", "123")
                await page.click("input#kc-login")
                await page.wait_for_load_state("networkidle")

                # Select Store
                log_error("  Selecting store...")
                await page.wait_for_selector("#store-select", timeout=30000)
                await page.click("#store-select")
                await page.wait_for_selector("role=option[name='Five Star Ford of North Richland Hills']", timeout=10000)
                await page.get_by_role("option", name='Five Star Ford of North Richland Hills').click()
                await page.wait_for_load_state("networkidle")

                # Click "View Dashboard"
                log_error("   Accessing dashboard...")
                await page.wait_for_selector("button#login", timeout=30000)
                await page.click("button#login")
                await page.wait_for_load_state("networkidle")
                
                # Verify by navigating to target page
                await page.goto("https://sampackag.fixedops.cc/CPLaborOverview", timeout=30000)
                await page.wait_for_load_state("networkidle")
                
                log_error("✅ Login completed successfully")
                return True
                
            except Exception as e:
                log_error(f"Login attempt {attempt + 1} failed: {e}", exc_info=True)                
                if attempt < max_retries - 1:
                    log_info("⏳ Retrying in 5 seconds...")
                    await asyncio.sleep(5)
        
        return False
    
class MultiChartParallelProcessor:
    """Process multiple charts in parallel with separate browsers"""
    
    def __init__(self, max_browsers=4, auth_manager=None):
        self.max_browsers = max_browsers
        self.auth_manager = auth_manager or AuthManager()
        log_info(f"Initialized MultiChartParallelProcessor with max_browsers={max_browsers}")
    
    async def create_authenticated_browser_context(self, playwright, headless=False):
        """Create an authenticated browser context"""
        log_info(f"Creating authenticated browser context (headless={headless})")
        browser = await playwright.chromium.launch(
            headless=headless,
            args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
        )

        # Load auth state
        auth_state = self.auth_manager.get_auth_state()
        log_info(f"Auth state loaded: {'Available' if auth_state else 'Not available'}")
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            storage_state=auth_state
        )
        
        page = await context.new_page()
        page.set_default_timeout(BROWSER_TIMEOUT)
        
        return browser, context, page

    async def click_mui_chart_buttons(self, page):
        """Ensure all chart containers are visible and accessible"""
        try:
            log_info("Ensuring chart containers are visible...")            

            # Wait for page to be fully loaded
            await page.wait_for_load_state("networkidle", timeout=10000)
            await asyncio.sleep(2)

            # Find all chart containers
            chart_containers = await page.query_selector_all('[id*="chartContainterId-"]')

            log_info(f"📊 Found {len(chart_containers)} chart containers")

            processed_containers = []
            for i, container in enumerate(chart_containers):
                try:
                    # Get container ID
                    container_id = await container.get_attribute('id')
                    chart_id = container_id.replace('chartContainterId-', '') if container_id else 'unknown'

                    # Get chart title from card header
                    card_header = await container.query_selector(f'#card-header-{chart_id}')
                    chart_title = 'Unknown Chart'
                    if card_header:
                        title_element = await card_header.query_selector('.MuiCardHeader-title')
                        if title_element:
                            chart_title = await title_element.inner_text()

                    print(f"📈 Processing container {i+1}: ID='{chart_id}', Title='{chart_title.strip()}'")

                    # Scroll container into view to ensure it's visible
                    await container.scroll_into_view_if_needed()
                    await asyncio.sleep(0.5)

                    # Check if canvas is present and visible
                    canvas = await container.query_selector('canvas')
                    if canvas:
                        canvas_rect = await canvas.bounding_box()
                        if canvas_rect and canvas_rect['width'] > 0 and canvas_rect['height'] > 0:
                            print(f"✅ Chart {chart_id} canvas is visible ({canvas_rect['width']}x{canvas_rect['height']})")
                        else:
                            print(f"⚠️ Chart {chart_id} canvas is not visible")
                    else:
                        print(f"⚠️ Chart {chart_id} has no canvas element")

                    processed_containers.append({
                        'index': i,
                        'container_id': container_id,
                        'chart_id': chart_id,
                        'title': chart_title.strip()
                    })

                except Exception as e:
                    print(f"⚠️ Error processing container {i+1}: {e}")
                    continue

            # print(f"✅ Successfully processed {len(processed_containers)} chart containers")

            # Wait for all charts to be fully rendered
            await asyncio.sleep(2)

            return processed_containers

        except Exception as e:
            log_error(f"Error processing chart containers: {e}", exc_info=True)
            print(f"❌ Error processing chart containers: {e}")
            return []

    async def click_chart_point_by_mui_structure(self, page, chart_id, point_data):
        """Click on a chart data point using actual MUI structure knowledge"""
        try:
            print(f"🎯 Attempting to click chart point for chart ID: {chart_id}")

            # Find the chart container using the actual structure
            chart_container = await page.query_selector(f'#chartContainterId-{chart_id}')
            if not chart_container:
                print(f"❌ Could not find chart container for ID: {chart_id}")
                return {'success': False, 'error': 'Chart container not found'}

            # Ensure the chart container is visible
            await chart_container.scroll_into_view_if_needed()
            await asyncio.sleep(1)

            # Find the canvas within the container
            canvas = await chart_container.query_selector('canvas')
            if not canvas:
                print(f"❌ Could not find canvas for chart ID: {chart_id}")
                return {'success': False, 'error': 'Canvas not found'}

            # Get canvas position and dimensions
            canvas_rect = await canvas.bounding_box()
            if not canvas_rect:
                print(f"❌ Could not get canvas bounding box for chart ID: {chart_id}")
                return {'success': False, 'error': 'Canvas not visible'}

            print(f"📊 Canvas found for chart {chart_id}: {canvas_rect['width']}x{canvas_rect['height']} at ({canvas_rect['x']}, {canvas_rect['y']})")

            # Calculate click coordinates
            screen_x = point_data.get('screenX', 0)
            screen_y = point_data.get('screenY', 0)

            # Use absolute coordinates if they're within canvas bounds
            if (screen_x >= canvas_rect['x'] and screen_x <= canvas_rect['x'] + canvas_rect['width'] and
                screen_y >= canvas_rect['y'] and screen_y <= canvas_rect['y'] + canvas_rect['height']):
                click_x = screen_x
                click_y = screen_y
            else:
                # Convert to canvas-relative coordinates
                click_x = canvas_rect['x'] + (screen_x if screen_x < canvas_rect['width'] else screen_x % canvas_rect['width'])
                click_y = canvas_rect['y'] + (screen_y if screen_y < canvas_rect['height'] else screen_y % canvas_rect['height'])

            print(f"🖱️ Clicking at coordinates: ({click_x}, {click_y}) on canvas for chart {chart_id}")

            # Click on the chart point
            await page.mouse.click(click_x, click_y)
            await asyncio.sleep(2)  # Wait for drilldown to load

            return {'success': True, 'click_x': click_x, 'click_y': click_y, 'canvas_rect': canvas_rect}

        except Exception as e:
            print(f"❌ Error clicking chart point for chart {chart_id}: {e}")
            return {'success': False, 'error': str(e)}

    async def click_chart_point_with_legend_control(self, page, chart_id, point_data, target_year="2023"):
        """Click on a chart data point after applying legend control for target year"""
        try:
            print(f"🎯 Clicking chart point with legend control for chart ID: {chart_id}")
            print(f"   Target Year: {target_year}")
            print(f"   Point Data: {point_data.get('xLabel', 'Unknown')} in {point_data.get('datasetLabel', 'Unknown')}")

            # Find the chart container using the exact ID format from the HTML
            chart_container_selector = f'#chartContainterId-{chart_id}'
            chart_container = await page.query_selector(chart_container_selector)
            if not chart_container:
                print(f"❌ Could not find chart container with selector: {chart_container_selector}")
                # Try alternative selectors
                alt_selector = f'div[id="chartContainterId-{chart_id}"]'
                chart_container = await page.query_selector(alt_selector)
                if not chart_container:
                    print(f"❌ Could not find chart container with alternative selector: {alt_selector}")
                    return {'success': False, 'error': f'Chart container not found for ID: {chart_id}'}

            # Ensure the chart container is visible
            await chart_container.scroll_into_view_if_needed()
            await asyncio.sleep(1)

            # Apply legend control first
            legend_control_result = await page.evaluate(f"""
                () => {{
                    console.log('Starting legend control for chart ID: {chart_id}, target year: {target_year}');

                    // Try multiple selectors to find the chart container
                    let chartContainer = document.querySelector('#chartContainterId-{chart_id}');
                    if (!chartContainer) {{
                        chartContainer = document.querySelector('div[id="chartContainterId-{chart_id}"]');
                    }}
                    if (!chartContainer) {{
                        console.error('Chart container not found for ID: {chart_id}');
                        return {{ success: false, error: 'Container not found' }};
                    }}

                    console.log('Found chart container:', chartContainer);

                    const canvas = chartContainer.querySelector('canvas');
                    if (!canvas) {{
                        console.error('Canvas not found in chart container');
                        return {{ success: false, error: 'Canvas not found' }};
                    }}

                    console.log('Found canvas:', canvas);

                    // Find Chart.js instance
                    let chart = null;
                    if (typeof Chart !== 'undefined' && Chart.getChart) {{
                        chart = Chart.getChart(canvas);
                    }}

                    if (!chart) {{
                        console.error('Chart.js instance not found');
                        return {{ success: false, error: 'Chart instance not found' }};
                    }}

                    console.log('Found Chart.js instance:', chart);
                    console.log('Chart datasets:', chart.data.datasets.map(d => d.label));

                    // Control legends - enable target year, disable others
                    const controlResults = [];
                    chart.data.datasets.forEach((dataset, index) => {{
                        const label = dataset.label || '';
                        // More precise year matching
                        const isTargetYear = label === '{target_year}' ||
                                           label.trim() === '{target_year}' ||
                                           label.includes('{target_year}');

                        console.log(`Dataset ${{index}}: "${{label}}" - Is target year ({target_year}): ${{isTargetYear}}`);

                        if (chart.isDatasetVisible && typeof chart.isDatasetVisible === 'function') {{
                            const currentlyVisible = chart.isDatasetVisible(index);
                            console.log(`Dataset ${{index}} currently visible: ${{currentlyVisible}}`);

                            if (isTargetYear && !currentlyVisible) {{
                                chart.show(index);
                                console.log(`✅ Enabled dataset ${{index}}: "${{label}}"`);
                                controlResults.push({{ index, label, action: 'enabled' }});
                            }} else if (!isTargetYear && currentlyVisible) {{
                                chart.hide(index);
                                console.log(`❌ Disabled dataset ${{index}}: "${{label}}"`);
                                controlResults.push({{ index, label, action: 'disabled' }});
                            }} else {{
                                console.log(`No action needed for dataset ${{index}}: "${{label}}" (visible: ${{currentlyVisible}}, target: ${{isTargetYear}})`);
                            }}
                        }} else {{
                            // Fallback: use dataset.hidden property
                            const wasHidden = dataset.hidden;
                            dataset.hidden = !isTargetYear;
                            if (wasHidden !== dataset.hidden) {{
                                console.log(`Set dataset ${{index}} "${{label}}" hidden: ${{dataset.hidden}}`);
                                controlResults.push({{ index, label, action: dataset.hidden ? 'disabled' : 'enabled' }});
                            }}
                        }}
                    }});

                    // Update chart
                    if (chart.update && typeof chart.update === 'function') {{
                        chart.update('none');
                        console.log('Chart updated after legend control');
                    }}

                    console.log('Legend control completed. Results:', controlResults);
                    return {{ success: true, controlResults, chartInfo: {{ datasets: chart.data.datasets.length }} }};
                }}
            """)

            if not legend_control_result.get('success'):
                print(f"⚠️ Legend control failed: {legend_control_result.get('error')}")
            else:
                print(f"✅ Legend control applied: {len(legend_control_result.get('controlResults', []))} datasets controlled")

            # Wait for chart update
            await asyncio.sleep(1)

            # Now click on the data point
            canvas = await chart_container.query_selector('canvas')
            if not canvas:
                print(f"❌ Could not find canvas for chart ID: {chart_id}")
                return {'success': False, 'error': 'Canvas not found'}

            # Get canvas position and dimensions
            canvas_rect = await canvas.bounding_box()
            if not canvas_rect:
                print(f"❌ Could not get canvas bounding box for chart ID: {chart_id}")
                return {'success': False, 'error': 'Canvas not visible'}

            # Calculate click coordinates
            screen_x = point_data.get('screenX', 0)
            screen_y = point_data.get('screenY', 0)
            canvas_x = point_data.get('canvasX', 0)
            canvas_y = point_data.get('canvasY', 0)

            # Use canvas coordinates if available, otherwise screen coordinates
            if canvas_x and canvas_y:
                click_x = canvas_rect['x'] + canvas_x
                click_y = canvas_rect['y'] + canvas_y
            else:
                click_x = screen_x
                click_y = screen_y

            print(f"🖱️ Clicking at coordinates: ({click_x}, {click_y}) on canvas for chart {chart_id}")
            print(f"   Canvas rect: {canvas_rect}")
            print(f"   Point coordinates: canvas({canvas_x}, {canvas_y}), screen({screen_x}, {screen_y})")

            # Click on the chart point
            await page.mouse.click(click_x, click_y)
            await asyncio.sleep(2)  # Wait for drilldown to load

            return {
                'success': True,
                'click_x': click_x,
                'click_y': click_y,
                'canvas_rect': canvas_rect,
                'legend_control': legend_control_result,
                'target_year': target_year,
                'point_info': {
                    'label': point_data.get('xLabel'),
                    'dataset': point_data.get('datasetLabel'),
                    'value': point_data.get('value')
                }
            }

        except Exception as e:
            print(f"❌ Error clicking chart point with legend control for chart {chart_id}: {e}")
            return {'success': False, 'error': str(e)}

    async def discover_charts(self):
        """Discover all charts on the CPLaborOverview page"""
        print("🔍 Discovering charts on CPLaborOverview page...")
        
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                await page.goto("https://sampackag.fixedops.cc/CPLaborOverview", timeout=30000)
                await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)
                
                charts_info = await page.evaluate("""
                    () => {
                        const canvases = document.querySelectorAll('canvas');
                        const chartsInfo = [];

                        for (let i = 0; i < canvases.length; i++) {
                            const canvas = canvases[i];
                            const rect = canvas.getBoundingClientRect();

                            // Try to find chart title and ID using actual HTML structure
                            let chartTitle = `Chart ${i + 1}`;
                            let chartId = null;

                            // Method 1: Look for parent container with chart ID (chartContainterId-XXX)
                            let parentContainer = canvas.closest('[id*="chartContainterId"]');
                            if (parentContainer) {
                                // Extract chart ID from container ID
                                const containerId = parentContainer.id;
                                chartId = containerId.replace('chartContainterId-', '');

                                // Look for the card header with matching ID
                                const cardHeader = parentContainer.querySelector(`#card-header-${chartId}`);
                                if (cardHeader) {
                                    const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }
                            }

                            // Method 2: If no container found, look for nearest MuiCard with header
                            if (!chartId) {
                                const cardRoot = canvas.closest('.MuiCard-root');
                                if (cardRoot) {
                                    const cardHeader = cardRoot.querySelector('.MuiCardHeader-root[id*="card-header-"]');
                                    if (cardHeader) {
                                        // Extract chart ID from card header ID
                                        const headerId = cardHeader.id;
                                        chartId = headerId.replace('card-header-', '');

                                        const titleElement = cardHeader.querySelector('.MuiCardHeader-title');
                                        if (titleElement && titleElement.textContent.trim()) {
                                            chartTitle = titleElement.textContent.trim();
                                        }
                                    }
                                }
                            }
                            
                            // If no specific container found, try general approach
                            if (chartTitle === `Chart ${i + 1}`) {
                                let container = canvas.closest('.MuiCard-root, [class*="card"], [class*="chart"], [class*="widget"]');
                                if (container) {
                                    const titleElement = container.querySelector('h1, h2, h3, h4, h5, h6, .title, [class*="title"], .MuiCardHeader-title');
                                    if (titleElement && titleElement.textContent.trim()) {
                                        chartTitle = titleElement.textContent.trim();
                                    }
                                }
                            }
                            
                            // Check if canvas is a valid chart
                            let hasChart = false;
                            
                            // Check for Chart.js specific indicators
                            if (canvas.classList.contains('chartjs-render-monitor') || 
                                canvas.classList.contains('chartjs-render') ||
                                canvas.width > 0 && canvas.height > 0) {
                                hasChart = true;
                            }
                            
                            // Try to detect Chart.js instance
                            try {
                                if (typeof Chart !== 'undefined' && Chart.getChart) {
                                    const chart = Chart.getChart(canvas);
                                    if (chart) {
                                        hasChart = true;
                                    }
                                }
                            } catch (e) {
                                // Continue with other checks
                            }
                            
                            // Check for canvas with actual content (non-zero dimensions)
                            if (!hasChart && rect.width > 0 && rect.height > 0) {
                                // Additional check for canvas context
                                try {
                                    const ctx = canvas.getContext('2d');
                                    if (ctx) {
                                        hasChart = true;
                                    }
                                } catch (e) {
                                    // Fallback to dimension check
                                    hasChart = true;
                                }
                            }
                            
                            // Only include canvases that appear to be charts and are visible
                            if (hasChart && rect.width > 0 && rect.height > 0) {
                                chartsInfo.push({
                                    canvasIndex: i,
                                    chartTitle: chartTitle,
                                    chartId: chartId || `chart-${i}`,  // Include chart ID from container
                                    canvasId: canvas.id || `canvas-${i}`,
                                    canvasClass: canvas.className,
                                    containerId: parentContainer ? parentContainer.id : null,
                                    position: {
                                        x: Math.round(rect.left),
                                        y: Math.round(rect.top),
                                        width: Math.round(rect.width),
                                        height: Math.round(rect.height)
                                    },
                                    visible: rect.width > 0 && rect.height > 0,
                                    isChartJs: canvas.classList.contains('chartjs-render-monitor'),
                                    hasCardHeader: chartId !== null  // Indicate if card header was found
                                });
                            }
                        }
                        
                        console.log(`Found ${chartsInfo.length} charts with data`);
                        return chartsInfo;
                    }
                """)
                
                print(f"   Found {len(charts_info)} charts on CPLaborOverviewpage")
                for chart in charts_info:
                    print(f"  - Chart {chart['canvasIndex']}: {chart['chartTitle']} (Container: {chart['containerId']})")
                
                return charts_info
                
            finally:
                await context.close()
                await browser.close()

    async def find_matching_points_in_chart(self, page, chart_index, target_month_year, chart_id):
        """Find matching data points for a specific chart and target month-year"""
        try:
            # Remove hardcoded override - use the actual parameter
            # target_month_year='Nov 2023'  # This was overriding the input!
            
            log_info(f"Finding points in chart {chart_index} (ID: {chart_id}) for target: {target_month_year}")
            print(f"🔍 Finding points in chart {chart_index} (ID: {chart_id}) for target: {target_month_year}")
            
            matching_points = await page.evaluate("""
                (args) => {
                    const { chartIndex, targetMonthYear, chartId } = args;
                    console.log(`=== CHART POINT EXTRACTION DEBUG ===`);
                    console.log(`Starting point extraction for chart ${chartIndex} (ID: ${chartId})`);
                    console.log(`Target: ${targetMonthYear}`);

                    try {
                        // Helper function to get all possible chart instances
                        const getAllChartInstances = () => {
                            const instances = [];
                            
                            // Method 1: Chart.js registry (Chart.js v3+)
                            if (typeof Chart !== 'undefined' && Chart.instances) {
                                Object.values(Chart.instances).forEach(chart => {
                                    if (chart && chart.canvas) {
                                        instances.push(chart);
                                    }
                                });
                            }
                            
                            // Method 2: Chart.getChart for all canvases
                            if (typeof Chart !== 'undefined' && Chart.getChart) {
                                document.querySelectorAll('canvas').forEach(canvas => {
                                    const chart = Chart.getChart(canvas);
                                    if (chart && !instances.includes(chart)) {
                                        instances.push(chart);
                                    }
                                });
                            }
                            
                            // Method 3: Check canvas._chart property (older Chart.js)
                            document.querySelectorAll('canvas').forEach(canvas => {
                                if (canvas._chart && !instances.some(c => c.canvas === canvas)) {
                                    instances.push(canvas._chart);
                                }
                            });
                            
                            return instances;
                        };

                        // Helper function to normalize strings for comparison
                        const normalize = (str) => {
                            if (!str) return '';
                            return String(str).toLowerCase().trim().replace(/[^a-z0-9]/g, '');
                        };

                        // IMPROVED month matching with more flexible patterns
                        const isMonthMatch = (label, targetMonth) => {
                            if (!label || !targetMonth) return false;
                            
                            const labelNorm = normalize(label);
                            const targetNorm = normalize(targetMonth);
                            
                            console.log(`  Comparing: "${label}" (norm: "${labelNorm}") vs target "${targetMonth}" (norm: "${targetNorm}")`);
                            
                            // Exact match
                            if (labelNorm === targetNorm) {
                                console.log(`    ✅ Exact match found`);
                                return true;
                            }
                            
                            // Define month patterns with more variations
                            const monthMappings = {
                                'jan': ['jan', 'january', '01', '1', 'enero'],
                                'feb': ['feb', 'february', '02', '2', 'febrero'],
                                'mar': ['mar', 'march', '03', '3', 'marzo'],
                                'apr': ['apr', 'april', '04', '4', 'abril'],
                                'may': ['may', '05', '5', 'mayo'],
                                'jun': ['jun', 'june', '06', '6', 'junio'],
                                'jul': ['jul', 'july', '07', '7', 'julio'],
                                'aug': ['aug', 'august', '08', '8', 'agosto'],
                                'sep': ['sep', 'september', 'sept', '09', '9', 'septiembre'],
                                'oct': ['oct', 'october', '10', 'octubre'],
                                'nov': ['nov', 'november', '11', 'noviembre'],
                                'dec': ['dec', 'december', '12', 'diciembre']
                            };
                            
                            // Find matches using flexible pattern matching
                            for (const [monthKey, patterns] of Object.entries(monthMappings)) {
                                // Check if target matches this month group
                                const targetMatchesMonth = patterns.some(p => {
                                    const pNorm = normalize(p);
                                    return targetNorm === pNorm || 
                                        targetNorm.includes(pNorm) || 
                                        pNorm.includes(targetNorm);
                                });
                                
                                if (targetMatchesMonth) {
                                    // Check if label also matches this month group
                                    const labelMatchesMonth = patterns.some(p => {
                                        const pNorm = normalize(p);
                                        return labelNorm === pNorm || 
                                            labelNorm.includes(pNorm) || 
                                            pNorm.includes(labelNorm);
                                    });
                                    
                                    if (labelMatchesMonth) {
                                        console.log(`    ✅ Pattern match found via ${monthKey}`);
                                        return true;
                                    }
                                }
                            }
                            
                            console.log(`    ❌ No match`);
                            return false;
                        };

                        // IMPROVED year matching with more flexible patterns
                        const isYearMatch = (datasetLabel, targetYear) => {
                            if (!datasetLabel || !targetYear) return false;
                            const labelStr = String(datasetLabel).toLowerCase();
                            const yearStr = String(targetYear);
                            
                            console.log(`  Checking year: dataset "${labelStr}" vs target year "${yearStr}"`);
                            
                            // Multiple year matching strategies
                            const yearMatches = [
                                // Exact year match
                                labelStr.includes(yearStr),
                                // Year with common separators
                                new RegExp(`\\b${yearStr}\\b`).test(labelStr),
                                // Year in date formats (2023-11, 11/2023, etc.)
                                new RegExp(`${yearStr}[-/\\s]`).test(labelStr),
                                new RegExp(`[-/\\s]${yearStr}`).test(labelStr),
                                // Just the year by itself
                                labelStr === yearStr
                            ];
                            
                            const matches = yearMatches.some(match => match);
                            console.log(`    ${matches ? '✅' : '❌'} Year match: ${matches}`);
                            return matches;
                        };

                        // Find chart containers and get target chart
                        let targetChart = null;
                        const allCharts = getAllChartInstances();
                        console.log(`Found ${allCharts.length} chart instances`);

                        // Multiple strategies to find the target chart
                        if (chartId) {
                            // Method 1: Find by chart ID with multiple selector variations
                            const selectors = [
                                `#chartContainterId-${chartId}`,
                                `#chartContainer-${chartId}`,
                                `#chart-${chartId}`,
                                `[data-chart-id="${chartId}"]`,
                                `[id*="${chartId}"]`
                            ];
                            
                            for (const selector of selectors) {
                                const chartElement = document.querySelector(selector);
                                if (chartElement) {
                                    const canvas = chartElement.querySelector('canvas');
                                    if (canvas) {
                                        targetChart = allCharts.find(chart => chart.canvas === canvas);
                                        if (targetChart) {
                                            console.log(`Found chart via selector: ${selector}`);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        
                        if (!targetChart && typeof chartIndex === 'number') {
                            // Method 2: Find by index using various container selectors
                            const containerSelectors = [
                                '.react-grid-item.diagram-section',
                                '.react-grid-item',
                                '.diagram-section',
                                '.chart-container',
                                '[class*="chart"]',
                                'canvas'
                            ];
                            
                            for (const selector of containerSelectors) {
                                const containers = document.querySelectorAll(selector);
                                if (chartIndex >= 0 && chartIndex < containers.length) {
                                    const canvas = containers[chartIndex].tagName === 'CANVAS' ? 
                                                containers[chartIndex] : 
                                                containers[chartIndex].querySelector('canvas');
                                    if (canvas) {
                                        targetChart = allCharts.find(chart => chart.canvas === canvas);
                                        if (targetChart) {
                                            console.log(`Found chart via index ${chartIndex} with selector: ${selector}`);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        
                        if (!targetChart && allCharts.length > 0) {
                            // Method 3: Use first available chart as fallback
                            targetChart = allCharts[Math.min(chartIndex || 0, allCharts.length - 1)];
                            console.log(`Using fallback chart at index ${Math.min(chartIndex || 0, allCharts.length - 1)}`);
                        }

                        if (!targetChart) {
                            console.error('❌ No chart instance found');
                            console.log('Available charts:', allCharts.length);
                            console.log('Chart selectors tried:', [
                                `#chartContainterId-${chartId}`,
                                '.react-grid-item.diagram-section',
                                'canvas'
                            ]);
                            return [];
                        }

                        console.log(`✅ Using chart:`, {
                            type: targetChart.config?.type,
                            hasData: !!targetChart.data,
                            datasets: targetChart.data?.datasets?.length || 0,
                            labels: targetChart.data?.labels?.length || 0
                        });

                        // Validate chart data
                        if (!targetChart.data || !targetChart.data.datasets || !targetChart.data.datasets.length) {
                            console.error('❌ Chart has no data or datasets');
                            return [];
                        }

                        const datasets = targetChart.data.datasets;
                        const xLabels = targetChart.data.labels || [];
                        
                        console.log(`=== CHART DATA ANALYSIS ===`);
                        console.log(`📊 Chart data:`, {
                            datasets: datasets.length,
                            xLabels: xLabels.length,
                            chartType: targetChart.config?.type
                        });
                        console.log(`📋 X-axis labels:`, xLabels);
                        console.log(`📈 Dataset labels:`, datasets.map((d, i) => `[${i}] "${d.label}"`));

                        // IMPROVED TARGET PARSING with more flexibility
                        const targetStr = String(targetMonthYear || '').trim();
                        console.log(`🎯 Original target string: "${targetStr}"`);
                        
                        if (!targetStr) {
                            console.error('❌ No target month/year provided');
                            return [];
                        }
                        
                        let target = {
                            year: null,
                            month: null,
                            originalTarget: targetStr
                        };
                        
                        // Multiple parsing strategies
                        const targetLower = targetStr.toLowerCase();
                        
                        // Strategy 1: Common formats like "Nov 2023", "November 2023", "2023-11"
                        const formatPatterns = [
                            // "Nov 2023" or "November 2023"
                            /(\w+)\s+(\d{4})/,
                            // "2023-11" or "2023/11"
                            /(\d{4})[-/](\d{1,2})/,
                            // "11/2023" or "11-2023"
                            /(\d{1,2})[-/](\d{4})/,
                            // "2023 Nov" or "2023 November"
                            /(\d{4})\s+(\w+)/
                        ];
                        
                        let parsed = false;
                        for (const pattern of formatPatterns) {
                            const match = targetLower.match(pattern);
                            if (match) {
                                if (pattern.source.startsWith('(\\d{4})')) {
                                    // Year first formats
                                    target.year = match[1];
                                    if (match[2].match(/^\d+$/)) {
                                        // Month number
                                        const monthNum = parseInt(match[2]);
                                        const monthNames = ['', 'jan', 'feb', 'mar', 'apr', 'may', 'jun',
                                                        'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
                                        target.month = monthNames[monthNum] || match[2];
                                    } else {
                                        target.month = match[2];
                                    }
                                } else if (pattern.source.startsWith('(\\d{1,2})')) {
                                    // Month first, year second
                                    const monthNum = parseInt(match[1]);
                                    const monthNames = ['', 'jan', 'feb', 'mar', 'apr', 'may', 'jun',
                                                    'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
                                    target.month = monthNames[monthNum] || match[1];
                                    target.year = match[2];
                                } else {
                                    // Month name first
                                    target.month = match[1];
                                    target.year = match[2];
                                }
                                parsed = true;
                                break;
                            }
                        }
                        
                        // Fallback parsing
                        if (!parsed) {
                            // Extract year (4-digit number)
                            const yearMatch = targetStr.match(/\b(19|20)\d{2}\b/);
                            target.year = yearMatch ? yearMatch[0] : null;
                            
                            // Extract month (look for month names/abbreviations)
                            const monthKeywords = ['jan', 'feb', 'mar', 'apr', 'may', 'jun',
                                                'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
                            
                            for (const month of monthKeywords) {
                                if (targetLower.includes(month)) {
                                    target.month = month;
                                    break;
                                }
                            }
                            
                            // Try month numbers
                            if (!target.month) {
                                const monthNumMatch = targetStr.match(/\b(0?[1-9]|1[0-2])\b/);
                                if (monthNumMatch) {
                                    const monthNum = parseInt(monthNumMatch[1]);
                                    const monthNames = ['', 'jan', 'feb', 'mar', 'apr', 'may', 'jun',
                                                    'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
                                    target.month = monthNames[monthNum];
                                }
                            }
                        }
                        
                        // Set defaults if still missing
                        if (!target.year) target.year = '2023';
                        if (!target.month) target.month = 'nov';
                        
                        console.log(`🎯 Parsed target: month="${target.month}", year="${target.year}"`);

                        // STEP 1: Find the target month index in x-axis labels with more flexibility
                        console.log(`=== STEP 1: FINDING MONTH IN X-AXIS ===`);
                        let targetMonthIndex = -1;
                        
                        // Try exact matches first, then fuzzy matches
                        for (let i = 0; i < xLabels.length; i++) {
                            const label = xLabels[i];
                            console.log(`Checking x-axis[${i}]: "${label}"`);
                            
                            if (isMonthMatch(label, target.month)) {
                                targetMonthIndex = i;
                                console.log(`✅ Found target month "${target.month}" at x-axis index ${i}: "${label}"`);
                                break;
                            }
                        }

                        // If no exact match, try partial matching
                        if (targetMonthIndex === -1) {
                            console.log(`Trying partial/fuzzy matching...`);
                            for (let i = 0; i < xLabels.length; i++) {
                                const label = String(xLabels[i]).toLowerCase();
                                const targetLower = target.month.toLowerCase();
                                
                                if (label.includes(targetLower) || targetLower.includes(label)) {
                                    targetMonthIndex = i;
                                    console.log(`✅ Found partial match for "${target.month}" at x-axis index ${i}: "${xLabels[i]}"`);
                                    break;
                                }
                            }
                        }

                        if (targetMonthIndex === -1) {
                            console.warn(`❌ Target month "${target.month}" not found in x-axis labels`);
                            console.log(`Available labels:`, xLabels);
                            console.log(`Trying to match any label containing year ${target.year}...`);
                            
                            // Last resort: find any label containing the year
                            for (let i = 0; i < xLabels.length; i++) {
                                if (String(xLabels[i]).includes(target.year)) {
                                    targetMonthIndex = i;
                                    console.log(`🔄 Using year-based fallback at index ${i}: "${xLabels[i]}"`);
                                    break;
                                }
                            }
                            
                            if (targetMonthIndex === -1) {
                                return [];
                            }
                        }

                        // STEP 2: Find datasets with matching year and extract points
                        console.log(`=== STEP 2: FINDING DATASETS WITH YEAR ${target.year} ===`);
                        const matchingPoints = [];
                        
                        for (let datasetIndex = 0; datasetIndex < datasets.length; datasetIndex++) {
                            const dataset = datasets[datasetIndex];
                            const datasetLabel = dataset.label || `Dataset ${datasetIndex}`;
                            
                            console.log(`📈 Analyzing dataset ${datasetIndex}: "${datasetLabel}"`);
                            
                            // More flexible year matching
                            const isTargetYearDataset = isYearMatch(datasetLabel, target.year) || 
                                                    !target.year || // If no year specified, match all
                                                    datasets.length === 1; // If only one dataset, use it
                            
                            if (!isTargetYearDataset) {
                                console.log(`   ❌ Skipping - no year match`);
                                continue;
                            }
                            
                            console.log(`   ✅ Year matches! Looking for data at month index ${targetMonthIndex}`);
                            
                            // Get the data point at the target month index
                            if (targetMonthIndex >= 0 && targetMonthIndex < dataset.data.length) {
                                const value = dataset.data[targetMonthIndex];
                                
                                console.log(`   📊 Data at index ${targetMonthIndex}: ${value}`);
                                
                                // Skip null/undefined values but be more lenient with 0
                                if (value === null || value === undefined) {
                                    console.warn(`   ⚠️ No data for ${target.month} ${target.year} in dataset "${datasetLabel}"`);
                                    continue;
                                }
                                
                                // EXTRACT COORDINATES for clicking with better error handling
                                let canvasX = null;
                                let canvasY = null;
                                let screenX = null;
                                let screenY = null;

                                try {
                                    const meta = targetChart.getDatasetMeta(datasetIndex);
                                    console.log(`   🎯 Getting coordinates from meta:`, {
                                        hasMeta: !!meta,
                                        hasData: !!(meta && meta.data),
                                        dataLength: meta && meta.data ? meta.data.length : 0
                                    });
                                    
                                    if (meta && meta.data && meta.data[targetMonthIndex]) {
                                        const element = meta.data[targetMonthIndex];
                                        console.log(`   📍 Element properties:`, {
                                            hasX: 'x' in element,
                                            hasY: 'y' in element,
                                            x: element.x,
                                            y: element.y,
                                            type: typeof element,
                                            keys: Object.keys(element)
                                        });
                                        
                                        // Multiple ways to get coordinates
                                        if (element && typeof element.x === 'number' && typeof element.y === 'number') {
                                            canvasX = element.x;
                                            canvasY = element.y;
                                        } else if (element && typeof element.getCenterPoint === 'function') {
                                            const center = element.getCenterPoint();
                                            if (center && typeof center.x === 'number' && typeof center.y === 'number') {
                                                canvasX = center.x;
                                                canvasY = center.y;
                                            }
                                        } else if (element && element.options && element.options.elements) {
                                            // Try alternate property paths
                                            const props = element.options.elements.point || element.options.elements;
                                            if (props && typeof props.x === 'number' && typeof props.y === 'number') {
                                                canvasX = props.x;
                                                canvasY = props.y;
                                            }
                                        }

                                        // Calculate screen coordinates if canvas coordinates found
                                        if (canvasX !== null && canvasY !== null) {
                                            const canvas = targetChart.canvas;
                                            const canvasRect = canvas.getBoundingClientRect();
                                            
                                            const displayWidth = canvasRect.width;
                                            const displayHeight = canvasRect.height;
                                            const actualWidth = canvas.width;
                                            const actualHeight = canvas.height;
                                            
                                            const scaleX = displayWidth / actualWidth;
                                            const scaleY = displayHeight / actualHeight;
                                            
                                            screenX = canvasRect.left + (canvasX * scaleX);
                                            screenY = canvasRect.top + (canvasY * scaleY);

                                            console.log(`   ✅ Found coordinates: canvas(${canvasX}, ${canvasY}) -> screen(${screenX}, ${screenY})`);
                                        }
                                    } else {
                                        console.warn(`   ⚠️ No meta data element found for dataset ${datasetIndex}, index ${targetMonthIndex}`);
                                    }
                                } catch (coordError) {
                                    console.warn(`   ❌ Error getting coordinates:`, coordError.message);
                                }

                                // Create point data with more comprehensive information
                                const pointData = {
                                    canvasIndex: chartIndex,
                                    datasetIndex: datasetIndex,
                                    pointIndex: targetMonthIndex,
                                    value: value,
                                    xLabel: xLabels[targetMonthIndex],
                                    screenX: screenX,
                                    screenY: screenY,
                                    canvasX: canvasX,
                                    canvasY: canvasY,
                                    datasetLabel: datasetLabel,
                                    coordinatesValid: (screenX !== null && screenY !== null),
                                    targetYear: target.year,
                                    targetMonth: target.month,
                                    isTargetYearDataset: true,
                                    monthMatch: xLabels[targetMonthIndex],
                                    monthIndex: targetMonthIndex,
                                    clickable: (screenX !== null && screenY !== null),
                                    debugInfo: {
                                        originalTarget: target.originalTarget,
                                        parsedMonth: target.month,
                                        parsedYear: target.year,
                                        foundAtIndex: targetMonthIndex,
                                        xLabelAtIndex: xLabels[targetMonthIndex],
                                        datasetMatched: datasetLabel,
                                        chartType: targetChart.config?.type,
                                        totalDatasets: datasets.length,
                                        totalXLabels: xLabels.length
                                    }
                                };

                                matchingPoints.push(pointData);
                                console.log(`   ✅ Added matching point:`, pointData);
                            } else {
                                console.warn(`   ❌ Target month index ${targetMonthIndex} out of range for dataset (length: ${dataset.data ? dataset.data.length : 0})`);
                            }
                        }

                        console.log(`=== FINAL RESULTS ===`);
                        console.log(`🎉 Found ${matchingPoints.length} matching points for ${target.month} ${target.year}`);
                        
                        if (matchingPoints.length === 0) {
                            console.log(`🔍 Debug summary:`);
                            console.log(`   - Original target: "${targetStr}"`);
                            console.log(`   - Target parsed as: ${target.month} ${target.year}`);
                            console.log(`   - Month found at x-axis index: ${targetMonthIndex}`);
                            console.log(`   - X-axis labels:`, xLabels);
                            console.log(`   - Dataset labels:`, datasets.map(d => d.label));
                            console.log(`   - Datasets with data at target index:`, 
                                    datasets.map((d, i) => ({
                                        index: i, 
                                        label: d.label, 
                                        hasData: targetMonthIndex < d.data.length,
                                        dataAtIndex: targetMonthIndex < d.data.length ? d.data[targetMonthIndex] : 'N/A'
                                    })));
                        }
                        
                        return matchingPoints;

                    } catch (error) {
                        console.error('❌ Error in chart point extraction:', error);
                        console.error('Stack:', error.stack);
                        return [];
                    }
                }
            """, {'chartIndex': chart_index, 'targetMonthYear': target_month_year, 'chartId': chart_id})
            
            print(f"✅ Found {len(matching_points)} matching points in chart {chart_index} for {target_month_year}")
            return matching_points

        except Exception as e:
            log_error(f"Error finding matching points in chart {chart_index}: {str(e)}", exc_info=True)
            print(f"❌ Error finding matching points in chart {chart_index}: {str(e)}")
            return []
    async def create_chart_point_combinations(self, target_months_years):
        """Create combinations of charts and their matching points"""
        print("🔍 Creating chart-point combinations...")

        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                # Navigate to CPLaborOverview
                await page.goto("https://sampackag.fixedops.cc/CPLaborOverview", timeout=30000)
                await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)

                # Click MUI chart buttons to reveal charts
                clicked_buttons = await self.click_mui_chart_buttons(page)

                # Discover all charts
                charts_info = await self.discover_charts()
                if not charts_info:
                    print("❌ No charts found")
                    # Delete auth_state.json file when no charts are found
                    auth_state_path = "auth_state.json"
                    auth_manager = AuthManager()
                    try:
                        if os.path.exists(auth_state_path):
                            os.remove(auth_state_path)
                            print(f"Deleted {auth_state_path} due to no charts found")
                            async with async_playwright() as playwright:
                                success = await auth_manager.setup_authentication(playwright)
                                if not success:
                                    print("❌ Authentication setup failed. Exiting.")
                                    return
                                else:
                                    # Navigate to CPLaborOverview
                                    await page.goto("https://sampackag.fixedops.cc/CPLaborOverview", timeout=30000)
                                    await page.wait_for_load_state("networkidle", timeout=15000)
                                    await asyncio.sleep(2)
                        else:
                            print(f"⚠️ {auth_state_path} not found to delete")
                    except Exception as e:
                        print(f"❌ Error deleting {auth_state_path}: {e}")
                    return []
                
                chart_point_combinations = []
                charts_with_points = []
                
                # For each chart, find matching points for each target month-year
                for chart_info in charts_info:
                    chart_index = chart_info['canvasIndex']
                    container_id = chart_info.get('containerId', '')
                    chart_id = container_id.split('-')[-1] if container_id else None
                    chart_title = chart_info.get('chartTitle', f'Chart {chart_id}')
                    print(f"🔍 Processing Chart {chart_id}: {chart_title}")
                    
                    chart_total_points = 0
                    chart_combinations = []
                    
                    # Process each target month-year for this chart
                    for target_month_year in target_months_years:
                        print(f"  📅 Looking for data points matching: {target_month_year}")
                        
                        # Find matching points for this chart and target month-year
                        matching_points = await self.find_matching_points_in_chart(
                            page, chart_index, target_month_year,chart_id
                        )
                        
                        print(f"     Found {len(matching_points) if matching_points else 0} matching points for {target_month_year}")
                        
                        if matching_points:
                            # Create combination for this chart and target month-year
                            combination = {
                                'chart_index': f"chart_{chart_index}",
                                'chart_id': chart_id,
                                'chart_info': chart_info,
                                'target_month_year': target_month_year,
                                'matching_points': matching_points,
                                'processing_status': 'pending',
                                'points_count': len(matching_points)
                            }
                            chart_combinations.append(combination)
                            chart_total_points += len(matching_points)
                            
                            print(f"  ✅ Added combination: Chart {chart_index} - {target_month_year} ({len(matching_points)} points)")
                        else:
                            print(f"  ⚠️  No matching points found for Chart {chart_index} - {target_month_year}")
                    
                    # Track charts with their point counts
                    if chart_combinations:
                        charts_with_points.append({
                            'chart_index': chart_index,
                            'chart_id': chart_id,
                            'chart_title': chart_title,
                            'total_points': chart_total_points,
                            'combinations': chart_combinations
                        })
                
                # Sort charts by total points (descending) to get charts with most points first
                charts_with_points.sort(key=lambda x: x['total_points'], reverse=True)
                
                # Take all charts and their combinations (all 12 combinations)
                for chart_data in charts_with_points:
                    chart_point_combinations.extend(chart_data['combinations'])
                    print(f"     Added Chart {chart_data['chart_index']}: {chart_data['chart_title']} ({chart_data['total_points']} points)")
                
                # print(chart_point_combinations, "chart_point_combinations***************")
                print(f"\n📈 Summary: Created {len(chart_point_combinations)} chart-point combinations from {len(charts_with_points)} charts")
                
                # Print summary by chart
                chart_summary = {}
                for combo in chart_point_combinations:
                    chart_id = combo['chart_id']
                    if chart_id not in chart_summary:
                        chart_summary[chart_id] = 0
                    chart_summary[chart_id] += 1
                
                for chart_id, count in chart_summary.items():
                    print(f"     {chart_id}: {count} combinations")
                # print(chart_point_combinations, "chart_point_combinations***************")
                return chart_point_combinations
                
            except Exception as e:
                log_error(f"Error creating chart-point combinations: {str(e)}", exc_info=True)
                print(f"❌ Error creating chart-point combinations: {str(e)}")
                return []
            
            finally:
                await context.close()
                await browser.close()
    
    async def click_chart_point(self, page, point):
        """Click on a chart data point"""
        try:
            # Check if we have valid coordinates
            if not point.get('coordinatesValid', False):
                print(f"⚠️ Invalid coordinates for point {point['x_label']}")
                return False
            
            screen_x = point['screenX']
            screen_y = point['screenY']
            
            if not (screen_x and screen_y and not (isnan(float(screen_x)) or isnan(float(screen_y)))):
                print(f"⚠️ Invalid screen coordinates: ({screen_x}, {screen_y})")
                return False
            
            # Click at the calculated coordinates
            await page.mouse.click(screen_x, screen_y)
            print(f"🖱️ Clicked at coordinates ({screen_x}, {screen_y})")
            
            # Wait for any UI changes
            await asyncio.sleep(1)
            
            return True
            
        except Exception as e:
            log_error(f"Error clicking chart point: {e}", exc_info=True)
            print(f"❌ Error clicking chart point: {e}")
            return False

    async def click_and_extract_data(self, page, point_data,target_month, target_month_year):
        """Click on a chart point and extract drilldown data"""
        result = {
            "target_month_year": target_month_year,
            "point_data": point_data,
            "click_success": False,
            "navigation_success": False,
            "extraction_data": None,
            "error": None,
            "processing_time": None,
            "screenshot_path": None
        }
        
        start_time = time.time()
        
        try:
            x_coord = point_data['screenX']
            y_coord = point_data['screenY']            
            # Validate coordinates
            if not (isinstance(x_coord, (int, float)) and isinstance(y_coord, (int, float))):
                result["error"] = "Invalid coordinates"
                return result            
            if not (0 <= x_coord <= 3000 and 0 <= y_coord <= 2000):
                result["error"] = f"Coordinates out of bounds: ({x_coord}, {y_coord})"
                return result            
            # Wait for page to be ready
            await page.wait_for_load_state("networkidle", timeout=10000)
            await asyncio.sleep(1)            
            # Click on the point
            print(f"🖱️ Clicking on point at ({x_coord}, {y_coord})")
            await page.mouse.move(x_coord, y_coord)
            await asyncio.sleep(0.5)
            await page.mouse.click(x_coord, y_coord)
            result["click_success"] = True            
            # Wait for navigation/modal
            await asyncio.sleep(3)            
            try:
                await page.wait_for_load_state("networkidle", timeout=15000)
                result["navigation_success"] = True                
                # Extract data from drilldown page
                extraction_data = await self.extract_drilldown_data(page)
                result["extraction_data"] = extraction_data           
                
            except Exception as nav_error:
                result["error"] = f"Navigation error: {str(nav_error)}"                
        except Exception as e:
            result["error"] = f"Click error: {str(e)}"
        
        result["processing_time"] = time.time() - start_time
        return result         

    async def implement_enhanced_single_legend_control(self, page):
        """Enhanced implementation for Chart.js single legend control with canvas detection and legend toggle"""

        enhanced_legend_control_script = """
        (function() {
            console.log('Implementing enhanced single legend control for Chart.js...');
            
            // Global variables
            let chartRotationIntervals = new Map();
            let currentActiveIndices = new Map();
            let chartInstances = new Map();
            let legendEnabled = new Map(); // Track legend state for each chart
            
            // Enhanced function to find Chart.js instances from canvas elements
            function findAllChartInstances() {
                const charts = [];
                
                // Method 1: Find canvases with Chart.js render monitor class
                const canvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                console.log(`Found ${canvases.length} Chart.js canvas elements`);
                
                canvases.forEach((canvas, index) => {
                    // Try multiple ways to access the Chart.js instance
                    let chartInstance = null;
                    
                    // Method A: Direct chart property
                    if (canvas.chart) {
                        chartInstance = canvas.chart;
                    }
                    // Method B: Check Chart.js global instances
                    else if (window.Chart && window.Chart.instances) {
                        const chartId = canvas.getAttribute('data-chartjs-id') || 
                                    canvas.getAttribute('id') || 
                                    Object.keys(window.Chart.instances)[index];
                        if (chartId && window.Chart.instances[chartId]) {
                            chartInstance = window.Chart.instances[chartId];
                        }
                    }
                    // Method C: Search through all Chart instances
                    else if (window.Chart && window.Chart.instances) {
                        Object.values(window.Chart.instances).forEach(instance => {
                            if (instance.canvas === canvas) {
                                chartInstance = instance;
                            }
                        });
                    }
                    
                    if (chartInstance && chartInstance.data && chartInstance.data.datasets) {
                        const chartId = `chart-${index}-${Date.now()}`;
                        charts.push({
                            id: chartId,
                            canvas: canvas,
                            chart: chartInstance,
                            datasetsCount: chartInstance.data.datasets.length
                        });
                        
                        console.log(`Chart ${chartId}: Found with ${chartInstance.data.datasets.length} datasets`);
                    } else {
                        console.log(`Canvas ${index}: No Chart.js instance found`);
                    }
                });
                
                // Method 2: Fallback - check all Chart.js instances globally
                if (charts.length === 0 && window.Chart && window.Chart.instances) {
                    console.log('🔄 Fallback: Searching through global Chart instances...');
                    Object.entries(window.Chart.instances).forEach(([key, instance], index) => {
                        if (instance && instance.canvas && instance.data && instance.data.datasets) {
                            const chartId = `global-chart-${index}`;
                            charts.push({
                                id: chartId,
                                canvas: instance.canvas,
                                chart: instance,
                                datasetsCount: instance.data.datasets.length
                            });
                            console.log(`✅ Global Chart ${chartId}: Found with ${instance.data.datasets.length} datasets`);
                        }
                    });
                }
                
                return charts;
            }
            
            // Function to create enhanced status indicator
            function createEnhancedStatusIndicator(chartData) {
                const { id, canvas, chart, datasetsCount } = chartData;
                
                if (datasetsCount <= 1) {
                    console.log(`ℹ️ Chart ${id}: Only ${datasetsCount} dataset(s), skipping indicator`);
                    return null;
                }
                
                // Find the chart container (look for parent with specific classes or create one)
                let chartContainer = canvas.closest('.MuiCard-root') || 
                                canvas.closest('.chart-container') || 
                                canvas.closest('div[class*="chart"]') ||
                                canvas.parentElement;
                
                if (!chartContainer) {
                    // Create a wrapper if none exists
                    chartContainer = document.createElement('div');
                    chartContainer.style.position = 'relative';
                    canvas.parentNode.insertBefore(chartContainer, canvas);
                    chartContainer.appendChild(canvas);
                }
                
                // Ensure container has relative positioning
                const computedStyle = window.getComputedStyle(chartContainer);
                if (computedStyle.position === 'static') {
                    chartContainer.style.position = 'relative';
                }
                
                // Create status indicator
                const statusContainer = document.createElement('div');
                statusContainer.id = `status-${id}`;
                statusContainer.className = 'chart-legend-status';
                statusContainer.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    background: linear-gradient(135deg, #1a237e, #3949ab);
                    color: white;
                    padding: 10px 14px;
                    border-radius: 20px;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 12px;
                    font-weight: 600;
                    z-index: 1000;
                    min-width: 160px;
                    text-align: center;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.1);
                    transition: all 0.3s ease;
                `;
                
                // Create dataset info display
                const datasetInfo = document.createElement('div');
                datasetInfo.id = `dataset-info-${id}`;
                datasetInfo.style.cssText = `
                    font-size: 11px;
                    margin-bottom: 8px;
                    opacity: 0.9;
                `;
                
                // Create progress bar container
                const progressContainer = document.createElement('div');
                progressContainer.style.cssText = `
                    width: 100%;
                    height: 4px;
                    background: rgba(255,255,255,0.2);
                    border-radius: 2px;
                    margin: 8px 0;
                    overflow: hidden;
                `;
                
                const progressBar = document.createElement('div');
                progressBar.id = `progress-${id}`;
                progressBar.style.cssText = `
                    width: 0%;
                    height: 100%;
                    background: linear-gradient(90deg, #4caf50, #8bc34a);
                    border-radius: 2px;
                    transition: width 0.2s ease;
                `;
                
                progressContainer.appendChild(progressBar);
                
                // Create control buttons
                const controlsContainer = document.createElement('div');
                controlsContainer.style.cssText = `
                    display: flex;
                    gap: 6px;
                    justify-content: center;
                    margin-top: 8px;
                `;
                
                // Legend toggle button
                const legendToggleBtn = document.createElement('button');
                legendToggleBtn.id = `legend-toggle-${id}`;
                legendToggleBtn.innerHTML = '👁️';
                legendToggleBtn.title = 'Toggle legend visibility';
                legendToggleBtn.style.cssText = `
                    background: rgba(255,255,255,0.2);
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 6px;
                    padding: 4px 8px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s ease;
                    color: white;
                `;
                
                // Play/Pause button
                const playPauseBtn = document.createElement('button');
                playPauseBtn.id = `playpause-${id}`;
                playPauseBtn.innerHTML = '⏸️';
                playPauseBtn.title = 'Toggle auto-rotation';
                playPauseBtn.style.cssText = `
                    background: rgba(255,255,255,0.2);
                    border: 1px solid rgba(255,255,255,0.3);
                    border-radius: 6px;
                    padding: 4px 8px;
                    cursor: pointer;
                    font-size: 12px;
                    transition: all 0.2s ease;
                    color: white;
                `;
                
                // Previous button
                const prevBtn = document.createElement('button');
                prevBtn.innerHTML = '⏮️';
                prevBtn.title = 'Previous dataset';
                prevBtn.style.cssText = playPauseBtn.style.cssText;
                
                // Next button
                const nextBtn = document.createElement('button');
                nextBtn.innerHTML = '⏭️';
                nextBtn.title = 'Next dataset';
                nextBtn.style.cssText = playPauseBtn.style.cssText;
                
                // Add hover effects
                [legendToggleBtn, playPauseBtn, prevBtn, nextBtn].forEach(btn => {
                    btn.addEventListener('mouseenter', () => {
                        btn.style.background = 'rgba(255,255,255,0.3)';
                        btn.style.transform = 'scale(1.05)';
                    });
                    btn.addEventListener('mouseleave', () => {
                        btn.style.background = 'rgba(255,255,255,0.2)';
                        btn.style.transform = 'scale(1)';
                    });
                });
                
                // Assemble the status indicator
                statusContainer.appendChild(datasetInfo);
                statusContainer.appendChild(progressContainer);
                controlsContainer.appendChild(legendToggleBtn);
                controlsContainer.appendChild(prevBtn);
                controlsContainer.appendChild(playPauseBtn);
                controlsContainer.appendChild(nextBtn);
                statusContainer.appendChild(controlsContainer);
                
                // Add to chart container
                chartContainer.appendChild(statusContainer);
                
                // Add event listeners
                legendToggleBtn.addEventListener('click', () => toggleLegend(id));
                playPauseBtn.addEventListener('click', () => toggleAutoRotation(id));
                nextBtn.addEventListener('click', () => switchToNextDataset(id));
                prevBtn.addEventListener('click', () => switchToPreviousDataset(id));
                
                console.log(`✅ Status indicator created for chart ${id}`);
                return statusContainer;
            }
            
            // Function to toggle legend visibility
            function toggleLegend(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const chart = chartData.chart;
                const currentState = legendEnabled.get(chartId) ?? true;
                const newState = !currentState;
                
                // Update legend display
                if (chart.options.plugins && chart.options.plugins.legend) {
                    chart.options.plugins.legend.display = newState;
                } else {
                    // Initialize legend options if they don't exist
                    if (!chart.options.plugins) chart.options.plugins = {};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                    chart.options.plugins.legend.display = newState;
                }
                
                // Update legend state
                legendEnabled.set(chartId, newState);
                
                // Update button appearance
                const legendToggleBtn = document.getElementById(`legend-toggle-${chartId}`);
                if (legendToggleBtn) {
                    legendToggleBtn.innerHTML = newState ? '👁️' : '🙈';
                    legendToggleBtn.title = newState ? 'Hide legend' : 'Show legend';
                    legendToggleBtn.style.opacity = newState ? '1' : '0.6';
                }
                
                // Update chart
                chart.update('none');
                
                console.log(`   Chart ${chartId}: Legend ${newState ? 'enabled' : 'disabled'}`);
            }
            
            // Enhanced function to toggle dataset visibility
            function setActiveDataset(chartData, activeIndex) {
                const { chart, id } = chartData;
                const datasets = chart.data.datasets;
                
                // Validate index
                if (activeIndex < 0 || activeIndex >= datasets.length) {
                    console.warn(`Invalid dataset index: ${activeIndex}`);
                    return;
                }
                
                // Hide all datasets except the active one
                datasets.forEach((dataset, index) => {
                    const meta = chart.getDatasetMeta(index);
                    if (meta) {
                        meta.hidden = (index !== activeIndex);
                    }
                });
                
                // Update legend to show only active dataset (if legend is enabled)
                const isLegendEnabled = legendEnabled.get(id) ?? true;
                if (isLegendEnabled && chart.options.plugins && chart.options.plugins.legend) {
                    if (!chart.options.plugins.legend.labels) {
                        chart.options.plugins.legend.labels = {};
                    }
                    chart.options.plugins.legend.labels.filter = function(legendItem) {
                        return legendItem.datasetIndex === activeIndex;
                    };
                }
                
                // Update chart without animation for better performance
                chart.update('none');
                
                console.log(`   Chart ${id}: Activated dataset ${activeIndex} (${datasets[activeIndex].label || 'Unnamed'})`);
            }
            
            // Function to update status display
            function updateStatusDisplay(chartId, activeIndex, progress = 0) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const datasets = chartData.chart.data.datasets;
                const activeDataset = datasets[activeIndex];
                const datasetName = activeDataset.label || `Dataset ${activeIndex + 1}`;
                
                // Update dataset info
                const datasetInfo = document.getElementById(`dataset-info-${chartId}`);
                if (datasetInfo) {
                    datasetInfo.textContent = `${datasetName} (${activeIndex + 1}/${datasets.length})`;
                }
                
                // Update progress bar
                const progressBar = document.getElementById(`progress-${chartId}`);
                if (progressBar) {
                    progressBar.style.width = `${progress}%`;
                }
            }
            
            // Function to start automatic rotation
            function startAutoRotation(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData || chartData.datasetsCount <= 1) return;
                
                // Clear existing interval
                if (chartRotationIntervals.has(chartId)) {
                    clearInterval(chartRotationIntervals.get(chartId));
                }
                
                let currentIndex = currentActiveIndices.get(chartId) || 0;
                let progressCounter = 0;
                const rotationDuration = 10000; // 10 seconds
                const updateInterval = 100; // Update every 100ms
                const totalUpdates = rotationDuration / updateInterval;
                
                // Set initial state
                setActiveDataset(chartData, currentIndex);
                updateStatusDisplay(chartId, currentIndex, 0);
                
                const intervalId = setInterval(() => {
                    progressCounter++;
                    const progress = (progressCounter % totalUpdates) / totalUpdates * 100;
                    
                    // Update progress display
                    updateStatusDisplay(chartId, currentIndex, progress);
                    
                    // Switch dataset when progress completes
                    if (progressCounter % totalUpdates === 0 && progressCounter > 0) {
                        currentIndex = (currentIndex + 1) % chartData.datasetsCount;
                        currentActiveIndices.set(chartId, currentIndex);
                        setActiveDataset(chartData, currentIndex);
                    }
                }, updateInterval);
                
                chartRotationIntervals.set(chartId, intervalId);
                console.log(`🔄 Auto-rotation started for chart ${chartId}`);
            }
            
            // Function to stop automatic rotation
            function stopAutoRotation(chartId) {
                if (chartRotationIntervals.has(chartId)) {
                    clearInterval(chartRotationIntervals.get(chartId));
                    chartRotationIntervals.delete(chartId);
                    console.log(`⏹️ Auto-rotation stopped for chart ${chartId}`);
                }
            }
            
            // Function to toggle auto-rotation
            function toggleAutoRotation(chartId) {
                const playPauseBtn = document.getElementById(`playpause-${chartId}`);
                
                if (chartRotationIntervals.has(chartId)) {
                    stopAutoRotation(chartId);
                    if (playPauseBtn) playPauseBtn.innerHTML = '▶️';
                } else {
                    startAutoRotation(chartId);
                    if (playPauseBtn) playPauseBtn.innerHTML = '⏸️';
                }
            }
            
            // Function to switch to next dataset
            function switchToNextDataset(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const currentIndex = currentActiveIndices.get(chartId) || 0;
                const nextIndex = (currentIndex + 1) % chartData.datasetsCount;
                
                currentActiveIndices.set(chartId, nextIndex);
                setActiveDataset(chartData, nextIndex);
                updateStatusDisplay(chartId, nextIndex, 0);
                
                console.log(`⏭️ Chart ${chartId}: Manually switched to dataset ${nextIndex}`);
            }
            
            // Function to switch to previous dataset
            function switchToPreviousDataset(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const currentIndex = currentActiveIndices.get(chartId) || 0;
                const prevIndex = (currentIndex - 1 + chartData.datasetsCount) % chartData.datasetsCount;
                
                currentActiveIndices.set(chartId, prevIndex);
                setActiveDataset(chartData, prevIndex);
                updateStatusDisplay(chartId, prevIndex, 0);
                
                console.log(`⏮️ Chart ${chartId}: Manually switched to dataset ${prevIndex}`);
            }
            
            // Function to enable legend for a specific chart
            function enableLegend(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const chart = chartData.chart;
                
                // Enable legend
                if (!chart.options.plugins) chart.options.plugins = {};
                if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                chart.options.plugins.legend.display = true;
                
                legendEnabled.set(chartId, true);
                
                // Update button appearance
                const legendToggleBtn = document.getElementById(`legend-toggle-${chartId}`);
                if (legendToggleBtn) {
                    legendToggleBtn.innerHTML = '👁️';
                    legendToggleBtn.title = 'Hide legend';
                    legendToggleBtn.style.opacity = '1';
                }
                
                // Re-apply current dataset filter
                const currentIndex = currentActiveIndices.get(chartId) || 0;
                setActiveDataset(chartData, currentIndex);
                
                console.log(`✅ Chart ${chartId}: Legend enabled`);
            }
            
            // Function to disable legend for a specific chart
            function disableLegend(chartId) {
                const chartData = chartInstances.get(chartId);
                if (!chartData) return;
                
                const chart = chartData.chart;
                
                // Disable legend
                if (!chart.options.plugins) chart.options.plugins = {};
                if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                chart.options.plugins.legend.display = false;
                
                legendEnabled.set(chartId, false);
                
                // Update button appearance
                const legendToggleBtn = document.getElementById(`legend-toggle-${chartId}`);
                if (legendToggleBtn) {
                    legendToggleBtn.innerHTML = '🙈';
                    legendToggleBtn.title = 'Show legend';
                    legendToggleBtn.style.opacity = '0.6';
                }
                
                chart.update('none');
                
                console.log(`❌ Chart ${chartId}: Legend disabled`);
            }
            
            // Main setup function
            function setupEnhancedLegendControl() {
                console.log('🔍 Searching for Chart.js instances...');
                
                const foundCharts = findAllChartInstances();
                console.log(`   Found ${foundCharts.length} Chart.js instances`);
                
                if (foundCharts.length === 0) {
                    console.log('⚠️ No Chart.js instances found. Will retry...');
                    return false;
                }
                
                foundCharts.forEach(chartData => {
                    chartInstances.set(chartData.id, chartData);
                    currentActiveIndices.set(chartData.id, 0);
                    legendEnabled.set(chartData.id, true); // Initialize legend as enabled
                    
                    // Create status indicator
                    createEnhancedStatusIndicator(chartData);
                    
                    // Start auto-rotation
                    startAutoRotation(chartData.id);
                });
                
                console.log(`✅ Enhanced legend control setup completed for ${foundCharts.length} charts`);
                return true;
            }
            
            // Retry mechanism for chart detection
            function initializeWithRetry() {
                let attempts = 0;
                const maxAttempts = 15;
                const retryInterval = 1000;
                
                function trySetup() {
                    attempts++;
                    console.log(`🔄 Setup attempt ${attempts}/${maxAttempts}`);
                    
                    if (setupEnhancedLegendControl() || attempts >= maxAttempts) {
                        if (attempts >= maxAttempts && chartInstances.size === 0) {
                            console.log('❌ Failed to find Chart.js instances after maximum attempts');
                            console.log('💡 Charts might be loaded dynamically. Try refreshing the page.');
                        }
                        return;
                    }
                    
                    setTimeout(trySetup, retryInterval);
                }
                
                trySetup();
            }
            
            // Cleanup function
            window.addEventListener('beforeunload', function() {
                chartRotationIntervals.forEach((intervalId) => {
                    clearInterval(intervalId);
                });
                chartRotationIntervals.clear();
                currentActiveIndices.clear();
                chartInstances.clear();
                legendEnabled.clear();
            });
            
            // Handle dynamic content changes
            const observer = new MutationObserver(function(mutations) {
                let shouldResetup = false;
                
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                if (node.tagName === 'CANVAS' || node.querySelector('canvas')) {
                                    shouldResetup = true;
                                }
                            }
                        });
                    }
                });
                
                if (shouldResetup) {
                    console.log('🔄 DOM changes detected, re-initializing...');
                    setTimeout(() => setupEnhancedLegendControl(), 2000);
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Start initialization
            initializeWithRetry();
            
            // Expose control functions globally
            window.chartLegendControl = {
                toggleLegend,
                enableLegend,
                disableLegend,
                toggleAutoRotation,
                switchToNextDataset,
                switchToPreviousDataset,
                stopAutoRotation,
                getChartInstances: () => Array.from(chartInstances.values()),
                getCurrentActiveIndex: (chartId) => currentActiveIndices.get(chartId),
                isLegendEnabled: (chartId) => legendEnabled.get(chartId) ?? true,
                manualSetup: setupEnhancedLegendControl
            };
            
            console.log('   Enhanced Chart.js Legend Control initialized!');
            console.log('   Available commands:');
            console.log('  - window.chartLegendControl.toggleLegend(chartId)');
            console.log('  - window.chartLegendControl.enableLegend(chartId)');
            console.log('  - window.chartLegendControl.disableLegend(chartId)');
            console.log('  - window.chartLegendControl.toggleAutoRotation(chartId)');
            
        })();
        """
        
        # Execute the enhanced JavaScript
        await page.evaluate(enhanced_legend_control_script)
    async def apply_enhanced_legend_control(self, page):
        """Apply the enhanced legend control script to the page with robust chart detection"""
        try:
            # Wait for charts to be loaded with multiple attempts
            print("🔍 Waiting for charts to load...")

            # Try multiple selectors to find charts
            chart_found = False
            selectors_to_try = [
                'canvas.chartjs-render-monitor',
                'canvas[class*="chartjs"]',
                'canvas',
                '[id*="chart"]'
            ]

            for selector in selectors_to_try:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    chart_found = True
                    print(f"✅ Found charts using selector: {selector}")
                    break
                except:
                    continue

            if not chart_found:
                print("⚠️ No chart elements found with any selector")
                return False

            await asyncio.sleep(3)  # Give charts time to fully initialize

            # Execute the comprehensive chart detection script
            result = await page.evaluate("""
                (function() {
                    console.log('=== COMPREHENSIVE CHART DETECTION ===');

                    // Check Chart.js availability
                    const chartJsAvailable = typeof Chart !== 'undefined';
                    console.log('Chart.js available:', chartJsAvailable);

                    if (!chartJsAvailable) {
                        // Try to find Chart.js in window object
                        const chartKeys = Object.keys(window).filter(key => key.toLowerCase().includes('chart'));
                        console.log('Potential chart objects:', chartKeys);
                        return false;
                    }

                    // Initialize chart instances map
                    window.legendControlInitialized = true;
                    window.chartInstances = new Map();

                    // Find all possible canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    const anyChartCanvas = document.querySelectorAll('canvas[class*="chart"]');

                    console.log(`Canvas elements found:`);
                    console.log(`  - Total canvas: ${allCanvases.length}`);
                    console.log(`  - chartjs-render-monitor: ${chartCanvases.length}`);
                    console.log(`  - chart-related: ${anyChartCanvas.length}`);

                    // Use the most specific selector that found canvases
                    let canvasesToProcess = chartCanvases.length > 0 ? chartCanvases :
                                          anyChartCanvas.length > 0 ? anyChartCanvas : allCanvases;

                    console.log(`Processing ${canvasesToProcess.length} canvas elements`);

                    let successfullyRegistered = 0;

                    canvasesToProcess.forEach((canvas, index) => {
                        console.log(`\\n--- Processing Canvas ${index} ---`);

                        let chartInstance = null;
                        let detectionMethod = 'none';

                        // Method 1: Chart.getChart (Chart.js v3+)
                        try {
                            if (Chart.getChart) {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    detectionMethod = 'Chart.getChart';
                                    console.log(`✓ Found via Chart.getChart`);
                                }
                            }
                        } catch (e) {
                            console.log(`✗ Chart.getChart failed:`, e.message);
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance) {
                            try {
                                if (canvas.chart) {
                                    chartInstance = canvas.chart;
                                    detectionMethod = 'canvas.chart';
                                    console.log(`✓ Found via canvas.chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas.chart failed:`, e.message);
                            }
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance) {
                            try {
                                if (canvas._chart) {
                                    chartInstance = canvas._chart;
                                    detectionMethod = 'canvas._chart';
                                    console.log(`✓ Found via canvas._chart`);
                                }
                            } catch (e) {
                                console.log(`✗ canvas._chart failed:`, e.message);
                            }
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance) {
                            try {
                                if (Chart.instances) {
                                    Object.values(Chart.instances).forEach(instance => {
                                        if (instance && instance.canvas === canvas) {
                                            chartInstance = instance;
                                            detectionMethod = 'Chart.instances';
                                            console.log(`✓ Found via Chart.instances`);
                                        }
                                    });
                                }
                            } catch (e) {
                                console.log(`✗ Chart.instances search failed:`, e.message);
                            }
                        }

                        // Method 5: Check canvas data attributes or properties
                        if (!chartInstance) {
                            try {
                                // Look for any property that might contain a chart instance
                                const props = Object.getOwnPropertyNames(canvas);
                                for (const prop of props) {
                                    if (prop.includes('chart') || prop.includes('Chart')) {
                                        const value = canvas[prop];
                                        if (value && typeof value === 'object' && value.data && value.data.datasets) {
                                            chartInstance = value;
                                            detectionMethod = `canvas.${prop}`;
                                            console.log(`✓ Found via canvas.${prop}`);
                                            break;
                                        }
                                    }
                                }
                            } catch (e) {
                                console.log(`✗ Property search failed:`, e.message);
                            }
                        }

                        // Validate chart instance
                        if (chartInstance) {
                            console.log(`Chart instance found via: ${detectionMethod}`);

                            if (chartInstance.data && chartInstance.data.datasets && chartInstance.data.datasets.length > 0) {
                                // Use canvas ID or create consistent ID mapping
                                let chartId = canvas.id || `canvas-${index}`;

                                // Also register with alternative IDs for compatibility
                                const alternativeIds = [`chart_${index}`, `chart-${index}`, `canvas_${index}`];

                                window.chartInstances.set(chartId, {
                                    instance: chartInstance,
                                    canvas: canvas,
                                    detectionMethod: detectionMethod,
                                    originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                });

                                // Register with alternative IDs
                                alternativeIds.forEach(altId => {
                                    window.chartInstances.set(altId, {
                                        instance: chartInstance,
                                        canvas: canvas,
                                        detectionMethod: detectionMethod,
                                        originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                                    });
                                });

                                console.log(`✅ Registered ${chartId} (and alternatives: ${alternativeIds.join(', ')}):`);
                                console.log(`   - Datasets: ${chartInstance.data.datasets.length}`);
                                console.log(`   - Detection: ${detectionMethod}`);

                                chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                    console.log(`   - Dataset ${dsIndex}: "${dataset.label || 'Unnamed'}"`);
                                });

                                successfullyRegistered++;
                            } else {
                                console.log(`✗ Chart instance invalid (no datasets)`);
                            }
                        } else {
                            console.log(`✗ No chart instance found for canvas ${index}`);
                        }
                    });

                    console.log(`\\n=== DETECTION COMPLETE ===`);
                    console.log(`Successfully registered: ${successfullyRegistered} charts`);
                    console.log(`Chart IDs: [${Array.from(window.chartInstances.keys()).join(', ')}]`);

                    return successfullyRegistered > 0;
                })()
            """)

            if result:
                print("✅ Enhanced legend control applied successfully with comprehensive detection")
                return True
            else:
                print("❌ No chart instances found even with comprehensive detection")
                return False

        except Exception as e:
            print(f"❌ Failed to apply enhanced legend control: {str(e)}")
            return False
    async def disable_all_legends(self, page):
        """Disable legends for all charts"""
        try:
            await page.evaluate("""
                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        
                        // Disable legend
                        if (!chart.options.plugins) chart.options.plugins = {};
                        if (!chart.options.plugins.legend) chart.options.plugins.legend = {};
                        chart.options.plugins.legend.display = false;
                        
                        // Update chart
                        chart.update('none');
                        
                        console.log(`Legend disabled for ${chartId}`);
                    });
                }
            """)
            
            print("✅ All legends disabled")
            return True
            
        except Exception as e:
            print(f"❌ Failed to disable legends: {str(e)}")
            return False
     
    async def enable_only_target_legend(self, page, chart_id, target_dataset_label):
        """Enable only the target dataset legend and disable all others"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to enable legend for chart: {chart_id}, dataset: {target_dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found, attempting to reinitialize...');
                    return false;
                }}

                // Try multiple chart ID variations including the actual chart ID
                const chartIdVariations = ['{chart_id}', 'chart_{chart_id}', '{chart_id}_chart', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;
                let actualChartId = null;

                // First try to find in chartInstances
                for (const id of chartIdVariations) {{
                    if (window.chartInstances && window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        actualChartId = id;
                        console.log(`Found chart with ID: ${{id}}`);
                        break;
                    }}
                }}

                // If not found in chartInstances, try to find canvas and get Chart.js instance
                if (!chartData) {{
                    console.log('Chart not found in chartInstances, searching for canvas...');
                    
                    // Look for canvas in the specific card
                    const cardElement = document.getElementById('card-header-{chart_id}');
                    let canvas = null;
                    
                    if (cardElement) {{
                        const cardContainer = cardElement.closest('.MuiCard-root');
                        if (cardContainer) {{
                            canvas = cardContainer.querySelector('canvas');
                        }}
                    }}
                    
                    // Fallback: search all canvases
                    if (!canvas) {{
                        const canvases = document.querySelectorAll('canvas');
                        canvas = canvases[0]; // Use first canvas as fallback
                    }}
                    
                    if (canvas && canvas.chart) {{
                        chartData = {{ instance: canvas.chart }};
                        actualChartId = 'canvas-found';
                        console.log('Found chart via canvas element');
                    }} else {{
                        console.log('No chart found via canvas either');
                        if (window.chartInstances) {{
                            console.log('Available charts:', Array.from(window.chartInstances.keys()));
                        }} else {{
                            console.log('chartInstances not available');
                        }}
                        return false;
                    }}
                }}

                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure for', actualChartId);
                    return false;
                }}

                // Find target dataset index with fuzzy matching
                let targetDatasetIndex = -1;
                chart.data.datasets.forEach((dataset, index) => {{
                    const datasetLabel = dataset.label || '';
                    const targetLabel = '{target_dataset_label}' || '';
                    
                    // Ensure both are strings before comparison
                    const normalizedDatasetLabel = String(datasetLabel);
                    const normalizedTargetLabel = String(targetLabel);

                    // Exact match first
                    if (normalizedDatasetLabel === normalizedTargetLabel) {{
                        targetDatasetIndex = index;
                        return;
                    }}

                    // Fuzzy match (contains or similar) - only if both strings are non-empty
                    if (normalizedDatasetLabel && normalizedTargetLabel) {{
                        if (normalizedDatasetLabel.includes(normalizedTargetLabel) || 
                            normalizedTargetLabel.includes(normalizedDatasetLabel)) {{
                            targetDatasetIndex = index;
                            console.log(`Fuzzy match found: "${{normalizedDatasetLabel}}" matches "${{normalizedTargetLabel}}"`);
                        }}
                    }}
                }});

                if (targetDatasetIndex === -1) {{
                    console.log('Target dataset not found: {target_dataset_label}');
                    console.log('Available datasets:', chart.data.datasets.map(d => d.label || 'unlabeled'));

                    // Try to find any dataset and use the first one as fallback
                    if (chart.data.datasets.length > 0) {{
                        targetDatasetIndex = 0;
                        console.log('Using first dataset as fallback:', chart.data.datasets[0].label || 'unlabeled');
                    }} else {{
                        return false;
                    }}
                }}

                try {{
                    // Show only the target dataset
                    chart.data.datasets.forEach((dataset, index) => {{
                        const meta = chart.getDatasetMeta(index);
                        if (meta) {{
                            meta.hidden = (index !== targetDatasetIndex);
                        }}
                    }});

                    // Enable legend but filter to show only target dataset
                    if (!chart.options.plugins) chart.options.plugins = {{}};
                    if (!chart.options.plugins.legend) chart.options.plugins.legend = {{}};

                    chart.options.plugins.legend.display = true;
                    chart.options.plugins.legend.labels = {{
                        filter: function(legendItem) {{
                            return legendItem.datasetIndex === targetDatasetIndex;
                        }}
                    }};

                    // Update chart
                    chart.update('none');

                    const actualDatasetLabel = chart.data.datasets[targetDatasetIndex].label || 'unlabeled';
                    console.log('Successfully enabled legend for:', actualDatasetLabel, '(index:', targetDatasetIndex, ')');
                    return true;
                }} catch (error) {{
                    console.error('Error updating chart:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                print(f"✅ Enabled only legend for {target_dataset_label} in chart {chart_id}")
                return True
            else:
                print(f"⚠️ Failed to enable legend for {target_dataset_label} in chart {chart_id}")
                return False

        except Exception as e:
            print(f"❌ Error enabling legend for {target_dataset_label}: {str(e)}")
            return False
   
    async def debug_legend_control(self, page):
        """Debug function to check legend control setup"""
        try:
            debug_info = await page.evaluate("""
            (function() {
                const info = {
                    chartInstancesExists: !!window.chartInstances,
                    chartInstancesSize: window.chartInstances ? window.chartInstances.size : 0,
                    chartIds: window.chartInstances ? Array.from(window.chartInstances.keys()) : [],
                    chartDetails: []
                };

                if (window.chartInstances) {
                    window.chartInstances.forEach((chartData, chartId) => {
                        const chart = chartData.instance;
                        info.chartDetails.push({
                            id: chartId,
                            hasInstance: !!chart,
                            hasData: !!(chart && chart.data),
                            datasetCount: chart && chart.data && chart.data.datasets ? chart.data.datasets.length : 0,
                            datasetLabels: chart && chart.data && chart.data.datasets ? chart.data.datasets.map(d => d.label) : []
                        });
                    });
                }

                return info;
            })()
            """)
            
            for chart_detail in debug_info.get('chartDetails', []):
                chart_id = chart_detail.get('id', 'Unknown')
                dataset_count = chart_detail.get('datasetCount', 0)
                dataset_labels = chart_detail.get('datasetLabels', [])                
            return debug_info

        except Exception as e:
            print(f"❌ Error debugging legend control: {str(e)}")
            return None

    async def click_data_point_for_drilldown(self, page, chart_id, point_data):
        """Click on a specific data point to trigger drilldown navigation"""
        try:
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            # First try using the pre-calculated screen coordinates
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    print(f"🖱️ Clicking at pre-calculated coordinates: ({screen_x}, {screen_y})")
                    await page.mouse.click(screen_x, screen_y)
                    await asyncio.sleep(1)
                    return {
                        'success': True,
                        'method': 'pre_calculated_coordinates',
                        'position': {'x': screen_x, 'y': screen_y},
                        'dataset_label': dataset_label,
                        'x_label': x_label
                    }
                except Exception as coord_error:
                    print(f"⚠️ Pre-calculated coordinates failed: {coord_error}")

            # Fallback to JavaScript-based clicking
            click_result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting to click data point for chart: {chart_id}, dataset: {dataset_label}');

                if (!window.chartInstances) {{
                    console.log('chartInstances not found');
                    return {{ success: false, error: 'Chart instances not initialized' }};
                }}

                if (!window.chartInstances.has('{chart_id}')) {{
                    console.log('Chart {chart_id} not found');
                    console.log('Available charts:', Array.from(window.chartInstances.keys()));
                    return {{ success: false, error: 'Chart instance not found' }};
                }}

                const chartData = window.chartInstances.get('{chart_id}');
                const chart = chartData.instance;

                if (!chart || !chart.data || !chart.data.datasets) {{
                    console.log('Invalid chart data structure');
                    return {{ success: false, error: 'Invalid chart data' }};
                }}

                try {{
                    // Find target dataset index by label
                    let targetDatasetIndex = {dataset_index};
                    chart.data.datasets.forEach((dataset, index) => {{
                        if (dataset.label === '{dataset_label}') {{
                            targetDatasetIndex = index;
                        }}
                    }});

                    // Get dataset meta
                    const meta = chart.getDatasetMeta(targetDatasetIndex);
                    if (!meta || !meta.data || !meta.data[{point_index}]) {{
                        console.log('Data point not found at index: {point_index}');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    // Get point element and position
                    const pointElement = meta.data[{point_index}];
                    const pointPosition = pointElement.getCenterPoint();
                    const canvas = chart.canvas;

                    // Create click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true
                    }});

                    // Dispatch click event
                    canvas.dispatchEvent(clickEvent);

                    console.log('Successfully clicked data point: {x_label} from {dataset_label}');
                    return {{
                        success: true,
                        method: 'javascript_click',
                        position: pointPosition,
                        dataset_label: '{dataset_label}',
                        x_label: '{x_label}'
                    }};

                }} catch (error) {{
                    console.error('Error clicking data point:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)

            if click_result.get('success', False):
                print(f"✅ Data point clicked for drilldown: {x_label} from {dataset_label}")
                return click_result
            else:
                print(f"⚠️ Failed to click data point: {click_result.get('error', 'Unknown error')}")
                return click_result

        except Exception as e:
            print(f"❌ Error clicking data point for drilldown: {str(e)}")
            return {'success': False, 'error': str(e)}

    
    async def process_single_point_task_with_selective_legend(self, page, task, target_month_year):
        """Process a single point task with selective legend control and drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            month_year=target_month_year
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']
        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')         
            # Step 1: Enable only the target legend, disable all others
            legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
            if not legend_enabled:
                print(f"⚠️ {task_id}: Legend control failed, continuing anyway")
            # Wait for legend update
            await asyncio.sleep(1)
            # Debug: Check if the chart area is visible and clickable
            await self.debug_chart_clickability(page, chart_id, point_data)
            # Step 2: Click data point and wait for drilldown navigation
            click_result = None
            navigation_result = None
            # Get current URL before clicking
            initial_url = page.url
            print(f"📍 {task_id}: Current URL before click: {initial_url}")
            # Method 1: Use pre-calculated coordinates (most reliable)
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')
            if screen_x and screen_y and isinstance(screen_x, (int, float)) and isinstance(screen_y, (int, float)):
                try:
                    await page.mouse.click(screen_x, screen_y)                   
                    await asyncio.sleep(3)  # Give time for navigation to start
                    # Check if URL changed
                    current_url = page.url
                    if current_url != initial_url:
                        print(f"✅ {task_id}: Navigation detected to: {current_url}")
                        # Wait for page to fully load
                        try:
                            await page.wait_for_load_state("networkidle", timeout=10000)
                            await asyncio.sleep(2)
                        except:
                            pass  # Continue even if load state fails

                        click_result = {
                            'success': True,
                            'method': 'pre_calculated_coordinates',
                            'coordinates': {'x': screen_x, 'y': screen_y}
                        }

                        navigation_result = {
                            'success': True,
                            'url': current_url,
                            'navigation_completed': True,
                            'initial_url': initial_url
                        }

                    else:
                        print(f"⚠️ {task_id}: No navigation detected after coordinate click")
                        # Wait a bit more and check again
                        await asyncio.sleep(2)
                        current_url = page.url

                        if current_url != initial_url:
                            print(f"✅ {task_id}: Delayed navigation detected to: {current_url}")
                            click_result = {'success': True, 'method': 'pre_calculated_coordinates'}
                            navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                        else:
                            print(f"❌ {task_id}: No navigation after coordinate click")
                            click_result = {'success': False, 'error': 'No navigation detected after coordinate click'}
                            navigation_result = {'success': False, 'error': 'No URL change detected'}

                except Exception as coord_error:
                    print(f"⚠️ {task_id}: Coordinate clicking failed: {coord_error}")
                    click_result = {'success': False, 'error': str(coord_error)}

            # Method 2: Fallback to JavaScript-based clicking
            if not click_result or not click_result.get('success', False):
                print(f"🔄 {task_id}: Trying JavaScript-based clicking...")

                try:
                    # Try JavaScript clicking
                    js_click_result = await self.click_data_point_for_drilldown(page, chart_id, point_data)

                    if js_click_result.get('success', False):
                        print(f"🖱️ {task_id}: JavaScript click executed, waiting for navigation...")

                        # Wait for potential navigation
                        await asyncio.sleep(3)
                        # Check if URL changed
                        current_url = page.url
                        if current_url != initial_url:                           
                            # Wait for page to fully load
                            try:
                                await page.wait_for_load_state("networkidle", timeout=10000)
                                await asyncio.sleep(2)
                            except:
                                pass
                            click_result = js_click_result
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                        else:                           
                            # Wait a bit more and check again
                            await asyncio.sleep(2)
                            current_url = page.url
                            if current_url != initial_url:
                                print(f"✅ {task_id}: Delayed JavaScript navigation detected to: {current_url}")
                                click_result = js_click_result
                                navigation_result = {'success': True, 'url': current_url, 'navigation_completed': True}
                            else:
                                click_result = {'success': False, 'error': 'JavaScript click no navigation'}
                                navigation_result = {'success': False, 'error': 'No URL change after JavaScript click'}
                    else:
                        click_result = js_click_result
                        navigation_result = {'success': False, 'error': 'JavaScript click failed'}

                except Exception as js_error:
                    print(f"❌ {task_id}: JavaScript clicking failed: {js_error}")
                    click_result = {'success': False, 'error': str(js_error)}
                    navigation_result = {'success': False, 'error': str(js_error)}

            # Method 3: Fallback to simple click with longer wait
            if not click_result or not click_result.get('success', False):
                print(f"🔄 {task_id}: Trying simple click with extended wait...")
                try:
                    # Simple click without complex navigation detection
                    await page.mouse.click(screen_x, screen_y)
                    print(f"🖱️ {task_id}: Simple click executed, waiting longer for navigation...")
                    # Wait longer for navigation
                    await asyncio.sleep(5)
                    # Check URL multiple times
                    for attempt in range(3):
                        current_url = page.url
                        if current_url != initial_url:
                            click_result = {
                                'success': True,
                                'method': 'simple_click_extended_wait',
                                'coordinates': {'x': screen_x, 'y': screen_y}
                            }
                            navigation_result = {
                                'success': True,
                                'url': current_url,
                                'navigation_completed': True,
                                'initial_url': initial_url
                            }
                            break

                        if attempt < 2:  # Don't wait after last attempt
                            await asyncio.sleep(2)

                    if not click_result or not click_result.get('success', False):
                        print(f"❌ {task_id}: Simple click also failed to trigger navigation")
                        click_result = {'success': False, 'error': 'Simple click no navigation'}
                        navigation_result = {'success': False, 'error': 'No URL change after simple click'}

                except Exception as simple_error:
                    print(f"❌ {task_id}: Simple clicking failed: {simple_error}")
                    click_result = {'success': False, 'error': str(simple_error)}
                    navigation_result = {'success': False, 'error': str(simple_error)}

            # Check if any clicking method succeeded
            if not click_result or not click_result.get('success', False):
                error_msg = click_result.get('error', 'All clicking methods failed') if click_result else 'No click result'
                print(f"❌ {task_id}: Failed to click data point: {error_msg}")
                return {
                    'task_id': task_id,
                    'chart_id': chart_id,
                    'error': f"Failed to click data point: {error_msg}",
                    'success': False,
                    'legend_controlled': legend_enabled
                }
            # Check navigation result
            if not navigation_result or not navigation_result.get('success', False):
                print(f"⚠️ {task_id}: Navigation failed, but click was successful - attempting data extraction anyway")
                # Try to extract data from current page
                current_url = page.url
                navigation_result = {
                    'success': False,
                    'url': current_url,
                    'navigation_completed': False,
                    'error': 'Navigation failed but click succeeded'
                }

            # Step 3: Extract data from the drilldown page
            extracted_data = None
            extraction_success = False
            # Only attempt extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # Check if we're on the expected drilldown page
                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    print(f"✅ {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year, chart_id)

                    # Check extraction success from the nested structure
                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)
                    if extraction_success:
                        print(f"✅ {task_id}: Data extraction successful")
                    else:
                        print(f"⚠️ {task_id}: Data extraction failed or incomplete")
                else:
                    print(f"⚠️ {task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                print(f"❌ {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': legend_enabled,
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            # print(f"✅ {task_id}: Successfully processed {point_label} from {dataset_label}")
            return result
        except Exception as e:
            log_error(f"{task_id}: Error processing point: {e}", exc_info=True)
            print(f"❌ {task_id}: Error processing point: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': False
            }
            return error_result

    async def extract_ag_grid_data_for_938(self, page, dataset_label):
        """Extract data from AG-Grid table specifically for chart 938 (Return Rate)"""
        try:
            log_info(f"🔍 Extracting AG-Grid data for chart 938, dataset: {dataset_label}")

            # Wait for AG-Grid to load
            await page.wait_for_selector('#returnRateDrilldown', timeout=10000)
            await asyncio.sleep(2)

            ag_grid_data = []

            # Find the AG-Grid table
            ag_grid_element = await page.query_selector('#returnRateDrilldown')
            if not ag_grid_element:
                log_info("⚠️ AG-Grid element not found")
                return []

            # Get all data rows from the AG-Grid
            data_rows = await ag_grid_element.query_selector_all('.ag-row')
            log_info(f"Found {len(data_rows)} data rows in AG-Grid")

            for row_index, row in enumerate(data_rows):
                try:
                    # Extract cell values from the row
                    cells = await row.query_selector_all('.ag-cell')
                    if len(cells) >= 8:  # Ensure we have all expected columns
                        # Extract the return rate values based on dataset_label
                        six_month_cell = cells[6]  # Column index 6 for 6 Month Return Rate
                        twelve_month_cell = cells[7]  # Column index 7 for 12 Month Return Rate

                        six_month_value = await six_month_cell.text_content()
                        twelve_month_value = await twelve_month_cell.text_content()

                        # Clean the values
                        six_month_clean = six_month_value.strip() if six_month_value else ""
                        twelve_month_clean = twelve_month_value.strip() if twelve_month_value else ""

                        # Create data structure based on which dataset was clicked
                        if "6 Month" in dataset_label or "6 Months" in dataset_label:
                            # User clicked on 6 Month Return Rate
                            container_data = {
                                "container_index": row_index,
                                "selector_used": "ag-grid-6-month",
                                "items": [{
                                    "item_index": 0,
                                    "title": "6 Months Return Rate",
                                    "value": six_month_clean,
                                    "html_structure": {
                                        "source": "ag-grid-cell",
                                        "column": "sixMonthReturnrate"
                                    }
                                }]
                            }
                        elif "12 Month" in dataset_label or "12 Months" in dataset_label:
                            # User clicked on 12 Month Return Rate
                            container_data = {
                                "container_index": row_index,
                                "selector_used": "ag-grid-12-month",
                                "items": [{
                                    "item_index": 0,
                                    "title": "12 Months Return Rate",
                                    "value": twelve_month_clean,
                                    "html_structure": {
                                        "source": "ag-grid-cell",
                                        "column": "twelveMonthReturnrate"
                                    }
                                }]
                            }
                        else:
                            # Default: extract both values
                            container_data = {
                                "container_index": row_index,
                                "selector_used": "ag-grid-both",
                                "items": [
                                    {
                                        "item_index": 0,
                                        "title": "6 Months Return Rate",
                                        "value": six_month_clean,
                                        "html_structure": {
                                            "source": "ag-grid-cell",
                                            "column": "sixMonthReturnrate"
                                        }
                                    },
                                    {
                                        "item_index": 1,
                                        "title": "12 Months Return Rate",
                                        "value": twelve_month_clean,
                                        "html_structure": {
                                            "source": "ag-grid-cell",
                                            "column": "twelveMonthReturnrate"
                                        }
                                    }
                                ]
                            }

                        ag_grid_data.append(container_data)
                        log_info(f"✅ Extracted row {row_index}: 6M={six_month_clean}, 12M={twelve_month_clean}")

                except Exception as row_error:
                    log_error(f"Error extracting row {row_index}: {row_error}")
                    continue

            log_info(f"✅ Successfully extracted {len(ag_grid_data)} containers from AG-Grid")
            return ag_grid_data

        except Exception as e:
            log_error(f"❌ Error extracting AG-Grid data for chart 938: {e}")
            return []

    async def extract_data_from_drilldown_page(self, page, point_data, target_month_year, chart_id=None):
        """Extract MUI Grid data from drilldown page focusing on h5 and h6 tags only"""
        max_retries = 3
        retry_delay = 2  # seconds between retries
        
        for attempt in range(max_retries):
            try:
                print(f"🔍 Extracting drill-down page data... (Attempt {attempt + 1}/{max_retries})")

                # Wait for page to load completely
                await page.wait_for_load_state("networkidle", timeout=10000)
                await asyncio.sleep(3)
                extraction_data = {
                    "extraction_timestamp": datetime.now().isoformat(),
                    "page_url": page.url,
                    "mui_grid_data": [],
                    "all_text_content": [],
                    "raw_html_sections": [],
                    "monetary_data": [],
                    "success": False,
                    "error": None,
                    "attempt": attempt + 1
                }

                # Special handling for chart 938 - AG-Grid table extraction
                if chart_id == '938':
                    dataset_label = point_data.get('datasetLabel', '') if point_data else ''
                    ag_grid_data = await self.extract_ag_grid_data_for_938(page, dataset_label)
                    if ag_grid_data:
                        extraction_data["mui_grid_data"].extend(ag_grid_data)
                        print(f"✅ Extracted AG-Grid data for chart 938: {len(ag_grid_data)} containers")

                # Method 1: Look for MUI Grid containers with the specific structure
                mui_grid_selectors = [
                    '.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3',
                    '.MuiGrid-root.MuiGrid-container',
                    '[class*="MuiGrid-container"]'
                ]
                for selector in mui_grid_selectors:
                    try:
                        grid_containers = await page.query_selector_all(selector)
                        print(f"Found {len(grid_containers)} grid containers with selector: {selector}")
                        for container_index, container in enumerate(grid_containers):
                            if await container.is_visible():
                                # Extract the specific structure we're looking for
                                grid_items = await container.query_selector_all('.MuiGrid-item')
                                print(f"Container {container_index} has {len(grid_items)} grid items")
                                container_data = {
                                    "container_index": container_index,
                                    "selector_used": selector,
                                    "items": []
                                }
                                for item_index, item in enumerate(grid_items):
                                    # Look for h5 (title) and h6 (value) elements
                                    h5_element = await item.query_selector('h5.MuiTypography-root.MuiTypography-h5')
                                    h6_element = await item.query_selector('h6.MuiTypography-root.MuiTypography-subtitle1')
                                    if h5_element and h6_element:
                                        title = (await h5_element.text_content()).strip()
                                        value = (await h6_element.text_content()).strip()
                                        item_data = {
                                            "item_index": item_index,
                                            "title": title,
                                            "value": value,
                                            "html_structure": {
                                                "h5_html": await h5_element.inner_html(),
                                                "h6_html": await h6_element.inner_html()
                                            }
                                        }
                                        container_data["items"].append(item_data)
                                        # print(f"Extracted: {title} - {value}")
                                if container_data["items"]:
                                    extraction_data["mui_grid_data"].append(container_data)
                                    # print(f"Added container {container_index} with {len(container_data['items'])} items")

                    except Exception as selector_error:
                        print(f"Error with selector {selector}: {selector_error}")
                        continue
                # Determine success
                extraction_data["success"] = len(extraction_data["mui_grid_data"]) > 0
                print(f"Extraction success: {extraction_data['success']}")
                print(f"MUI Grid data items: {len(extraction_data['mui_grid_data'])}")
                # If successful, return the result immediately
                if extraction_data["success"]:
                    print(f"✅ Data extraction successful on attempt {attempt + 1}")
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': None,
                        'processing_time': 0,
                        'screenshot_path': None
                    }
                    return result

                # If not successful and not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    print(f"⚠️ Attempt {attempt + 1} failed, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)                   
                else:
                    print(f"❌ All {max_retries} attempts failed")
                    # Return failure result after all attempts exhausted
                    result = {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': extraction_data,
                        'error': f"Data extraction failed after {max_retries} attempts",
                        'processing_time': 0,
                        'screenshot_path': None
                    }
                    return result

            except Exception as e:
                print(f"❌ Error extracting drill-down page data on attempt {attempt + 1}: {e}")
              
                # If not the last attempt, wait and retry
                if attempt < max_retries - 1:
                    print(f"⚠️ Attempt {attempt + 1} failed with error, retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                else:
                    print(f"❌ All {max_retries} attempts failed with errors")
                    # Return error result after all attempts exhausted
                    return {
                        'target_month_year': target_month_year,
                        'point_data': point_data,
                        'click_success': True,
                        'navigation_success': True,
                        'extraction_data': {
                            "extraction_timestamp": datetime.now().isoformat(),
                            "success": False,
                            "error": str(e),
                            "attempt": attempt + 1
                        },
                        'error': str(e),
                        'processing_time': 0,
                        'screenshot_path': None
                    }
        return {
            'target_month_year': target_month_year,
            'point_data': point_data,
            'click_success': True,
            'navigation_success': True,
            'extraction_data': {
                "extraction_timestamp": datetime.now().isoformat(),
                "success": False,
                "error": "Maximum retries exceeded",
                "attempt": max_retries
            },
            'error': "Maximum retries exceeded",
            'processing_time': 0,
            'screenshot_path': None
        }
   
   

    async def debug_and_setup_charts(self, page):
        """Debug and manually setup chart instances"""
        try:
            result = await page.evaluate("""
                (function() {
                    console.log('=== CHART DEBUG AND SETUP ===');

                    // Check if Chart.js is available
                    console.log('Chart.js available:', typeof Chart !== 'undefined');

                    // Find all canvas elements
                    const allCanvases = document.querySelectorAll('canvas');
                    console.log(`Total canvas elements found: ${allCanvases.length}`);

                    const chartCanvases = document.querySelectorAll('canvas.chartjs-render-monitor');
                    console.log(`Chart.js canvas elements found: ${chartCanvases.length}`);

                    // Initialize chart instances map
                    if (!window.chartInstances) {
                        window.chartInstances = new Map();
                    }

                    let foundCharts = 0;

                    // Try multiple methods to find charts
                    chartCanvases.forEach((canvas, index) => {
                        let chartInstance = null;

                        console.log(`Processing canvas ${index}:`);

                        // Method 1: Chart.getChart
                        if (typeof Chart !== 'undefined' && Chart.getChart) {
                            try {
                                chartInstance = Chart.getChart(canvas);
                                if (chartInstance) {
                                    console.log(`  - Found via Chart.getChart`);
                                }
                            } catch (e) {
                                console.log(`  - Chart.getChart failed:`, e.message);
                            }
                        }

                        // Method 2: Canvas chart property
                        if (!chartInstance && canvas.chart) {
                            chartInstance = canvas.chart;
                            console.log(`  - Found via canvas.chart`);
                        }

                        // Method 3: Canvas _chart property
                        if (!chartInstance && canvas._chart) {
                            chartInstance = canvas._chart;
                            console.log(`  - Found via canvas._chart`);
                        }

                        // Method 4: Search Chart.instances
                        if (!chartInstance && typeof Chart !== 'undefined' && Chart.instances) {
                            Object.values(Chart.instances).forEach(instance => {
                                if (instance.canvas === canvas) {
                                    chartInstance = instance;
                                    console.log(`  - Found via Chart.instances`);
                                }
                            });
                        }

                        if (chartInstance && chartInstance.data && chartInstance.data.datasets) {
                            const chartId = `chart_${index}`;
                            window.chartInstances.set(chartId, {
                                instance: chartInstance,
                                canvas: canvas,
                                originalLegendDisplay: chartInstance.options?.plugins?.legend?.display ?? true
                            });

                            console.log(`  - Registered as ${chartId} with ${chartInstance.data.datasets.length} datasets`);
                            chartInstance.data.datasets.forEach((dataset, dsIndex) => {
                                console.log(`    Dataset ${dsIndex}: ${dataset.label || 'Unnamed'}`);
                            });

                            foundCharts++;
                        } else {
                            console.log(`  - No valid chart instance found`);
                        }
                    });

                    console.log(`=== SETUP COMPLETE: ${foundCharts} charts registered ===`);
                    return foundCharts;
                })()
            """)

            log_info(f"🔍 Debug setup completed: {result} charts found and registered")
            return result > 0

        except Exception as e:
            log_error(f"❌ Debug setup failed: {str(e)}")
            return False


    async def click_chart_data_point(self, page, chart_id, point_data):
        """Click on a specific chart data point directly"""
        try:
            # Extract point information
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            x_label = point_data.get('xLabel', 'Unknown')
            dataset_index = point_data.get('datasetIndex', 0)
            point_index = point_data.get('pointIndex', 0)
            
            # Click on the specific data point
            point_clicked = await page.evaluate(f"""
            (function() {{
                if (window.chartInstances && window.chartInstances.has('{chart_id}')) {{
                    const chartData = window.chartInstances.get('{chart_id}');
                    const chart = chartData.instance;
                    
                    try {{
                        // Find the dataset by label if datasetIndex is not reliable
                        let targetDatasetIndex = {dataset_index};
                        
                        // Verify dataset index by label
                        chart.data.datasets.forEach((dataset, index) => {{
                            if (dataset.label === '{dataset_label}') {{
                                targetDatasetIndex = index;
                            }}
                        }});
                        
                        // Get the dataset meta
                        const meta = chart.getDatasetMeta(targetDatasetIndex);
                        if (!meta || !meta.data || !meta.data[{point_index}]) {{
                            console.log('Data point not found at index: ' + {point_index});
                            return false;
                        }}
                        
                        // Get the data point element
                        const pointElement = meta.data[{point_index}];
                        if (!pointElement) {{
                            console.log('Point element not found');
                            return false;
                        }}
                        
                        // Get the chart canvas
                        const canvas = chart.canvas;
                        if (!canvas) {{
                            console.log('Canvas not found');
                            return false;
                        }}
                        
                        // Get point position
                        const pointPosition = pointElement.getCenterPoint();
                        
                        // Create and dispatch click event
                        const rect = canvas.getBoundingClientRect();
                        const clickEvent = new MouseEvent('click', {{
                            clientX: rect.left + pointPosition.x,
                            clientY: rect.top + pointPosition.y,
                            bubbles: true,
                            cancelable: true
                        }});
                        
                        // Dispatch the click event
                        canvas.dispatchEvent(clickEvent);
                        
                        console.log('Clicked data point: ' + '{x_label}' + ' from dataset: ' + '{dataset_label}');
                        console.log('Point position:', pointPosition);
                        
                        return true;
                        
                    }} catch (error) {{
                        console.error('Error clicking data point:', error);
                        return false;
                    }}
                }}
                console.log('Chart instance not found: {chart_id}');
                return false;
            }})()
            """)
            
            if point_clicked:
                log_info(f"✅ Data point clicked for {chart_id} - {x_label} from {dataset_label}")
                return True
            else:
                log_info(f"⚠️ Could not click data point for {chart_id} - {x_label} from {dataset_label}")
                return False
                
        except Exception as e:
            log_error(f"❌ Failed to click data point for {chart_id} - {x_label}: {str(e)}")
            return False

    async def process_all_combinations_parallel(self, combinations):
        """Process charts in parallel with 3 browsers, each handling different charts"""

        log_info(f"🚀 Starting parallel processing of {len(combinations)} charts with 3 browsers")       
        # Organize combinations by chart for easier processing
        chart_combinations = {}
        for combo_idx, combination in enumerate(combinations):
            chart_id = combination.get('chart_id', f'chart_{combo_idx}')
            target_month = combination.get('target_month_year', 'unknown')
            log_info(target_month,"target_month....")
            matching_points = combination.get('matching_points', [])

            chart_combinations[chart_id] = {
                'combination_index': combo_idx,
                'chart_info': combination['chart_info'],
                'target_month_year': target_month,
                'matching_points': matching_points,
                'current_point_index': 0
            }

            log_info(f"**Chart {chart_id}: {target_month} ({(len(matching_points))} points)")

        all_results = []
        target_month_year = TARGET_MONTHS_YEAR
        max_browsers = 3

        # Group charts for parallel processing (3 charts at a time)
        chart_items = list(chart_combinations.items())
        chart_batches = []

        # Create batches of 3 charts each
        for i in range(0, len(chart_items), max_browsers):
            batch = chart_items[i:i + max_browsers]
            chart_batches.append(batch)

        log_info(f"   Processing {len(chart_batches)} batches with up to {max_browsers} browsers per batch")

        # Process each batch of charts in parallel
        for batch_index, chart_batch in enumerate(chart_batches, 1):
            log_info(f"\n🚀 Starting Batch {batch_index}/{len(chart_batches)} with {len(chart_batch)} charts")

            # Create parallel tasks for this batch
            batch_tasks = []
            for browser_index, (chart_id, chart_data) in enumerate(chart_batch):
                browser_id = f"Browser_{browser_index + 1}"
                log_info(f"   📋 {browser_id}: Will process Chart {chart_id} - {chart_data['chart_info'].get('chartTitle', 'Unknown')}")                
                task = asyncio.create_task(
                    self.process_single_chart_parallel(chart_data, target_month_year, browser_id,chart_id)
                )
                batch_tasks.append((browser_id, chart_id, task))

            # Wait for all browsers in this batch to complete
            log_info(f"⏳ Waiting for all {len(batch_tasks)} browsers in batch {batch_index} to complete...")

            for browser_id, chart_id, task in batch_tasks:
                try:
                    result = await task
                    if isinstance(result, list):
                        all_results.extend(result)
                        log_info(f"✅ {browser_id} completed Chart {chart_id}: {len(result)} results")
                    else:
                        log_info(f"⚠️ {browser_id} returned unexpected result type for Chart {chart_id}")
                except Exception as e:
                    log_error(f"❌ {browser_id} failed processing Chart {chart_id}: {str(e)}")
                    continue

            log_info(f"✅ Batch {batch_index} completed")

            # Add delay between batches to avoid overwhelming the system
            if batch_index < len(chart_batches):
                log_info(f"⏳ Waiting before next batch...")
                await asyncio.sleep(3)

        # Process final results
        successful_results = []
        failed_results = []

        for result in all_results:
            if isinstance(result, dict) and result.get('success', False):
                successful_results.append(result)
            else:
                failed_results.append(result)

        log_info(f"\n🎉 Parallel processing with 3 browsers completed!")
        log_info(f"📈 Summary:")
        log_info(f"   - Total charts processed: {len(chart_combinations)}")
        log_info(f"   - Total batches processed: {len(chart_batches)}")
        log_info(f"   - Total point tasks processed: {len(all_results)}")
        log_info(f"   - Successful: {len(successful_results)}")
        log_info(f"   - Failed: {len(failed_results)}")
        log_info(f"   - Success rate: {(len(successful_results) / len(all_results) * 100):.1f}%" if all_results else "0%")
        # print(all_results,"all_results+++++++++++")
        return {
            'successful': successful_results,
            'failed': failed_results,
            'all_results': all_results,
            'total_processed': len(all_results),
            'total_charts': len(chart_combinations),
            'batches_processed': len(chart_batches),
            'success_rate': (len(successful_results) / len(all_results) * 100) if all_results else 0
        }

    async def process_single_chart_parallel(self, chart_data, target_month_year, browser_id,chart_id):
        """Process all points in a single chart in parallel (for use with multiple browsers)"""
        # log_info(chart_data,"chart_data*******************")
        # chart_id = chart_data.get('chart_info', {}).get('chart_id', 'unknown_chart')
        # chart_id=chart_id
        # log_info(chart_id,"chart_id")
        chart_title = chart_data.get('chart_info', {}).get('chartTitle', 'Unknown Chart')
        matching_points = chart_data.get('matching_points', [])

        log_info(f"{browser_id}: Processing chart: {chart_title} :({chart_id}) with {len(matching_points)} points")

        chart_results = []

        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)

            try:
                # Navigate to CPLaborOverview
                log_info(f"📍 {browser_id}: Navigating to CPLaborOverview for {chart_id}")
                await page.goto("https://sampackag.fixedops.cc/CPLaborOverview", timeout=30000)
                await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)

                # Click MUI chart buttons to reveal charts
                await self.click_mui_chart_buttons(page)

                # Apply enhanced legend control
                legend_setup_success = await self.apply_enhanced_legend_control(page)
                await asyncio.sleep(2)

                if not legend_setup_success:
                    log_info(f"⚠️ {browser_id}: Legend control setup failed for {chart_id}, attempting manual setup...")
                    await self.debug_and_setup_charts(page)

                # Debug legend control setup
                await self.debug_legend_control(page)

                # log_info(f"✅ {browser_id}: Page setup completed for {chart_id}")

                # Process each point in this chart sequentially within this browser
                for point_idx, point_data in enumerate(matching_points):
                    point_label = point_data.get('xLabel', f'Point_{point_idx}')
                    dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')

                    # log_info(f"\n🔄 {browser_id}: Processing point {point_idx + 1}/{len(matching_points)}: {point_label} ({dataset_label})")

                    try:
                        # Step 1: Disable ALL legends first
                        # log_info(f"🔒 {browser_id}: Disabling all legends before processing {chart_id}")
                        await self.disable_all_legends(page)
                        await asyncio.sleep(1)

                        # Step 2: Enable ONLY the legend for current chart/dataset
                        log_info(f"🔓 {browser_id}: Enabling ONLY legend for {chart_id} - {dataset_label}")
                        legend_enabled = await self.enable_only_target_legend(page, chart_id, dataset_label)
                        await asyncio.sleep(2)  # Give more time for chart to update

                        if legend_enabled:
                            log_info(f"✅ {browser_id}: Legend control successful - ONLY {chart_id} legend is active")
                        else:
                            log_info(f"⚠️ {browser_id}: Legend control failed, but continuing with processing")

                        # Step 2.5: Ensure chart is interactive and data points are clickable
                        log_info(f"🔄 {browser_id}: Ensuring chart {chart_id} is interactive after legend control...")
                        await self.ensure_chart_interactivity(page, chart_id)
                        await asyncio.sleep(1)

                        # Step 3: Create task for this point
                        task = {
                            'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_info': chart_data['chart_info'],
                            'target_month_year': chart_data['target_month_year'],
                            'point_data': point_data,
                            'point_index': point_idx
                        }

                        # Step 4: Process this point with enhanced clicking
                        result = await self.process_single_point_task_with_enhanced_clicking(page, task, target_month_year)

                        if isinstance(result, dict):
                            result['chart_title'] = chart_title
                            result['point_sequence'] = point_idx + 1
                            result['method'] = 'parallel_processing'
                            result['browser_id'] = browser_id
                            result['chart_id'] = chart_id

                        chart_results.append(result)

                        # Log detailed result
                        if result.get('success', False):
                            click_success = result.get('click_success', False)
                            nav_success = result.get('navigation_success', False)
                            extract_success = result.get('extraction_success', False)
                            # print(f"✅ {browser_id}: Completed point {point_idx + 1}: {point_label}")
                            # print(f"      Click: {'✅' if click_success else '❌'} | Navigation: {'✅' if nav_success else '❌'} | Extraction: {'✅' if extract_success else '❌'}")
                            if nav_success:
                                drilldown_url = result.get('drilldown_url', 'Unknown')
                                log_info(f"   🔗 Drilldown URL: {drilldown_url}")
                        else:
                            error_msg = result.get('error', 'Unknown error')
                            log_error(f"❌ {browser_id}: Failed point {point_idx + 1}: {point_label} - {error_msg}")

                        # Step 5: Navigate back to CPLaborOverview for next point (if not last point)
                        if point_idx < len(matching_points) - 1:
                            log_info(f"🔄 {browser_id}: Navigating back to CPLaborOverview for next point")
                            try:
                                await page.goto("https://sampackag.fixedops.cc/CPLaborOverview", timeout=30000)
                                await page.wait_for_load_state("networkidle", timeout=15000)
                                await asyncio.sleep(2)

                                # Click MUI chart buttons to reveal charts
                                await self.click_mui_chart_buttons(page)

                                # Re-apply legend control
                                await self.apply_enhanced_legend_control(page)
                                await asyncio.sleep(1)
                                log_info(f"✅ {browser_id}: Successfully navigated back to CPLaborOverview")
                            except Exception as nav_back_error:
                                log_error(f"❌ {browser_id}: Failed to navigate back to CPLaborOverview: {nav_back_error}")
                                # Try to continue anyway
                                pass

                    except Exception as e:
                        log_error(f"❌ {browser_id}: Error processing point {point_idx + 1} - {point_label}: {str(e)}")
                        error_result = {
                            'task_id': f"{browser_id}_{chart_id}_point_{point_idx}",
                            'chart_id': chart_id,
                            'chart_title': chart_title,
                            'point_label': point_label,
                            'error': str(e),
                            'success': False,
                            'method': 'parallel_processing',
                            'browser_id': browser_id,
                            'point_sequence': point_idx + 1
                        }
                        chart_results.append(error_result)

                log_info(f"✅ {browser_id}: Completed all points for chart: {chart_title}")

            except Exception as e:
                log_error(f"❌ {browser_id}: Error setting up chart {chart_id}: {str(e)}")
                error_result = {
                    'chart_id': chart_id,
                    'chart_title': chart_title,
                    'error': f"Chart setup failed: {str(e)}",
                    'success': False,
                    'method': 'parallel_processing',
                    'browser_id': browser_id
                }
                chart_results.append(error_result)

            finally:
                try:
                    await context.close()
                    await browser.close()
                    log_info(f"🔒 {browser_id}: Browser closed for chart {chart_id}")
                except Exception as cleanup_error:
                    log_error(f"⚠️ {browser_id}: Cleanup error for {chart_id}: {cleanup_error}")

        return chart_results
    

    async def ensure_chart_interactivity(self, page, chart_id):
        """Ensure chart is interactive and data points are clickable after legend control"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Ensuring chart interactivity for: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return false;
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found for interactivity check');
                    return false;
                }}

                const chart = chartData.instance;

                try {{
                    // Ensure chart is responsive to events
                    if (chart.options) {{
                        if (!chart.options.interaction) chart.options.interaction = {{}};
                        chart.options.interaction.intersect = false;
                        chart.options.interaction.mode = 'point';

                        if (!chart.options.onHover) {{
                            chart.options.onHover = function(event, elements) {{
                                console.log('Chart hover detected:', elements.length, 'elements');
                            }};
                        }}

                        if (!chart.options.onClick) {{
                            chart.options.onClick = function(event, elements) {{
                                console.log('Chart click detected:', elements.length, 'elements');
                                if (elements.length > 0) {{
                                    const element = elements[0];
                                    console.log('Clicked element:', element);
                                }}
                            }};
                        }}
                    }}

                    // Force chart update to apply interaction settings
                    chart.update('none');

                    console.log('Chart interactivity ensured for: {chart_id}');
                    return true;
                }} catch (error) {{
                    console.error('Error ensuring chart interactivity:', error);
                    return false;
                }}
            }})()
            """)

            if result:
                log_info(f"✅ Chart {chart_id} interactivity ensured")
            else:
                log_info(f"⚠️ Failed to ensure chart {chart_id} interactivity")

            return result

        except Exception as e:
            log_error(f"❌ Error ensuring chart interactivity: {str(e)}")
            return False    
   
    async def try_chartjs_event_click(self, page, chart_id, point_data):
        """Try to trigger Chart.js click event programmatically"""
        try:
            result = await page.evaluate(f"""
            (function() {{
                console.log('Attempting Chart.js event click for chart: {chart_id}');

                if (!window.chartInstances) {{
                    console.log('No chart instances found');
                    return {{ success: false, error: 'No chart instances' }};
                }}

                // Try multiple chart ID variations
                const chartIdVariations = ['{chart_id}', 'chart_0', 'chart_1', 'chart_2', 'chart_3', 'chart_4', 'chart_5', 'canvas-0', 'canvas-1', 'canvas-2', 'canvas-3', 'canvas-4', 'canvas-5'];
                let chartData = null;

                for (const id of chartIdVariations) {{
                    if (window.chartInstances.has(id)) {{
                        chartData = window.chartInstances.get(id);
                        break;
                    }}
                }}

                if (!chartData) {{
                    console.log('Chart not found for event click');
                    return {{ success: false, error: 'Chart not found' }};
                }}

                const chart = chartData.instance;
                const canvas = chartData.canvas;

                try {{
                    // Get point data
                    const pointIndex = {point_data.get('pointIndex', 0)};
                    const datasetIndex = {point_data.get('datasetIndex', 0)};

                    // Get the data point element
                    const meta = chart.getDatasetMeta(datasetIndex);
                    if (!meta || !meta.data || !meta.data[pointIndex]) {{
                        console.log('Data point not found');
                        return {{ success: false, error: 'Data point not found' }};
                    }}

                    const pointElement = meta.data[pointIndex];
                    const pointPosition = pointElement.getCenterPoint();

                    // Create a synthetic click event
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {{
                        clientX: rect.left + pointPosition.x,
                        clientY: rect.top + pointPosition.y,
                        bubbles: true,
                        cancelable: true,
                        view: window
                    }});

                    // Trigger the click event
                    canvas.dispatchEvent(clickEvent);

                    // Also try Chart.js onClick if available
                    if (chart.options && chart.options.onClick) {{
                        const elements = chart.getElementsAtEventForMode(clickEvent, 'nearest', {{ intersect: true }}, false);
                        chart.options.onClick(clickEvent, elements, chart);
                    }}

                    console.log('Chart.js event click executed successfully');
                    return {{ success: true, method: 'chartjs_event', position: pointPosition }};

                }} catch (error) {{
                    console.error('Error in Chart.js event click:', error);
                    return {{ success: false, error: error.message }};
                }}
            }})()
            """)
            return result
        except Exception as e:
            log_error(f"❌ Error in Chart.js event click: {str(e)}")
            return {'success': False, 'error': str(e)}
    async def process_single_point_task_with_enhanced_clicking(self, page, task, target_month_year):
        """Process a single point task with enhanced clicking methods for drilldown"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]

        task_id = task['task_id']
        chart_id = task['chart_id']
        point_data = task['point_data']

        try:
            point_label = point_data.get('xLabel', 'Unknown')
            dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')
            # Get current URL before clicking
            initial_url = page.url          
            # Method 1: Try Chart.js event-based clicking first
            click_result = await self.try_chartjs_event_click(page, chart_id, point_data)
           
            navigation_result = None

            if click_result.get('success', False):
                # print(f"✅ {task_id}: Chart.js event click successful, checking for navigation...")

                # Wait for navigation
                await asyncio.sleep(3)
                current_url = page.url

                if current_url != initial_url:
                    # print(f"✅ {task_id}: Navigation successful to: {current_url}")
                    navigation_result = {'success': True, 'url': current_url, 'method': 'chartjs_event'}
                else:
                    # print(f"⚠️ {task_id}: Chart.js event click didn't trigger navigation")
                    click_result = {'success': False, 'error': 'No navigation after Chart.js event click'}

            # Method 2: Fallback to coordinate clicking if Chart.js event didn't work
            if not click_result or not click_result.get('success', False):
                # print(f"🔄 {task_id}: Trying coordinate-based clicking...")

                screen_x = point_data.get('screenX')
                screen_y = point_data.get('screenY')

                if screen_x and screen_y:
                    # Try multiple click methods
                    for click_method in ['single', 'double', 'with_delay']:
                        log_info(f"🖱️ {task_id}: Trying {click_method} click at ({screen_x}, {screen_y})")

                        if click_method == 'single':
                            await page.mouse.click(screen_x, screen_y)
                        elif click_method == 'double':
                            await page.mouse.click(screen_x, screen_y, click_count=2)
                        elif click_method == 'with_delay':
                            await page.mouse.move(screen_x, screen_y)
                            await asyncio.sleep(0.5)
                            await page.mouse.down()
                            await asyncio.sleep(0.1)
                            await page.mouse.up()

                        # Check for navigation after each method
                        await asyncio.sleep(3)
                        current_url = page.url

                        if current_url != initial_url:
                            # print(f"✅ {task_id}: {click_method} click triggered navigation to: {current_url}")
                            click_result = {'success': True, 'method': f'coordinate_{click_method}'}
                            navigation_result = {'success': True, 'url': current_url, 'method': f'coordinate_{click_method}'}
                            break
                        else:
                            log_info(f"⚠️ {task_id}: {click_method} click didn't trigger navigation")

                    if not navigation_result or not navigation_result.get('success', False):
                        click_result = {'success': False, 'error': 'All coordinate click methods failed'}
                        navigation_result = {'success': False, 'error': 'No navigation with any click method'}
                else:
                    click_result = {'success': False, 'error': 'No coordinates available'}
                    navigation_result = {'success': False, 'error': 'No coordinates for clicking'}

            # Continue with data extraction if navigation was successful
            if navigation_result and navigation_result.get('success', False):
                current_url = page.url
                # print(f"   {task_id}: Attempting data extraction from: {current_url}")

                if "AnalyzeData" in current_url and "chartId=drillDown" in current_url:
                    # print(f"✅ {task_id}: On correct drilldown page, extracting data...")
                    extracted_data = await self.extract_data_from_drilldown_page(page, point_data, target_month_year, chart_id)

                    extraction_success = False
                    if extracted_data and extracted_data.get('extraction_data'):
                        extraction_success = extracted_data['extraction_data'].get('success', False)

                    if extraction_success:
                        log_info(f"✅ {task_id}: Data extraction successful")
                    else:
                        log_info(f"⚠️ {task_id}: Data extraction failed or incomplete")
                else:
                    log_info(f"⚠️ {task_id}: Not on expected drilldown page. URL: {current_url}")
                    extracted_data = {
                        'extraction_data': {
                            'success': False,
                            'error': f'Unexpected page URL: {current_url}',
                            'expected_url_pattern': 'AnalyzeData?chartId=drillDown'
                        }
                    }
                    extraction_success = False
            else:
                # print(f"❌ {task_id}: Skipping data extraction due to navigation failure")
                extracted_data = {
                    'extraction_data': {
                        'success': False,
                        'error': 'Navigation failed, cannot extract data',
                        'navigation_error': navigation_result.get('error', 'Unknown navigation error') if navigation_result else 'No navigation result'
                    }
                }
                extraction_success = False
            # Determine overall success
            overall_success = (
                click_result.get('success', False) and
                navigation_result.get('success', False) and
                extraction_success
            )
            result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': TARGET_MONTHS_YEAR,
                'point_data': point_data,
                'dataset_label': dataset_label,
                'click_result': click_result,
                'navigation_result': navigation_result,
                'extracted_data': extracted_data,
                'timestamp': datetime.now().isoformat(),
                'success': overall_success,
                'legend_controlled': True,  # We know legend control was attempted
                'drilldown_url': navigation_result.get('url', '') if navigation_result else '',
                'click_success': click_result.get('success', False) if click_result else False,
                'navigation_success': navigation_result.get('success', False) if navigation_result else False,
                'extraction_success': extraction_success
            }
            # print(f"✅ {task_id}: Enhanced processing completed for {point_label} from {dataset_label}")
            return result

        except Exception as e:
            log_error(f"❌ {task_id}: Error in enhanced processing: {e}")
            error_result = {
                'task_id': task_id,
                'chart_id': chart_id,
                'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
                'target_month_year': task['target_month_year'],
                'point_data': point_data,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'success': False,
                'legend_controlled': True
            }
            return error_result

    async def debug_chart_clickability(self, page, chart_id, point_data):
        """Debug function to check if chart area is clickable"""
        try:
            screen_x = point_data.get('screenX')
            screen_y = point_data.get('screenY')

            if screen_x and screen_y:
                # Check what element is at the click coordinates
                element_info = await page.evaluate(f"""
                    () => {{
                        const element = document.elementFromPoint({screen_x}, {screen_y});
                        if (element) {{
                            return {{
                                tagName: element.tagName,
                                className: element.className,
                                id: element.id,
                                isCanvas: element.tagName === 'CANVAS',
                                boundingRect: element.getBoundingClientRect(),
                                visible: element.offsetParent !== null
                            }};
                        }}
                        return null;
                    }}
                """)

                if element_info:
                    log_info(f"🔍 Debug - Element at ({screen_x}, {screen_y}):")
                    log_info(f"   Tag: {element_info.get('tagName', 'Unknown')}")
                    log_info(f"   Class: {element_info.get('className', 'None')}")
                    log_info(f"   Canvas: {element_info.get('isCanvas', False)}")
                    log_info(f"   Visible: {element_info.get('visible', False)}")
                else:
                    log_info(f"⚠️ Debug - No element found at coordinates ({screen_x}, {screen_y})")

        except Exception as e:
            log_error(f"⚠️ Debug function failed: {e}")
                   
     
    # async def process_single_point_task(self, task, target_month_year):
    #     """Process a single point task - unchanged from your original"""
    #     if isinstance(target_month_year, list) and len(target_month_year) > 0:
    #         target_month = target_month_year[0]

    #     task_id = task['task_id']
    #     chart_id = task['chart_id']
    #     point_data = task['point_data']
    #     browser_id = task['browser_id']
        
    #     async with async_playwright() as playwright:
    #         browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
    #         try:
    #             # Navigate to CPLaborOverview
    #             await page.goto("https://sampackag.fixedops.cc/CPLaborOverview", timeout=30000)
    #             await page.wait_for_load_state("networkidle", timeout=15000)
    #             await asyncio.sleep(2)                
    #             # Click on the specific data point and extract data
    #             point_label = point_data.get('xLabel', 'Unknown')
    #             dataset_label = point_data.get('datasetLabel', 'Unknown Dataset')                
    #             log_info(f"   {task_id}: Clicking point {point_label} from {dataset_label}")                
    #             extracted_data = await self.click_and_extract_data(page, point_data,target_month, target_month_year)
                
    #             result = {
    #                 'task_id': task_id,
    #                 'chart_id': chart_id,
    #                 'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
    #                 'target_month_year': task['target_month_year'],
    #                 'point_data': point_data,
    #                 'dataset_label': dataset_label,
    #                 'extracted_data': extracted_data,
    #                 'timestamp': datetime.now().isoformat(),
    #                 'browser_id': browser_id,
    #                 'success': extracted_data.get('click_success', False) if extracted_data else False
    #             }                
    #             # log_info(f"✅ {task_id}: Successfully processed {point_label} from {dataset_label}")
    #             return result                
    #         except Exception as e:
    #             log_info(f"❌ {task_id}: Error processing point: {e}")                
    #             error_result = {
    #                 'task_id': task_id,
    #                 'chart_id': chart_id,
    #                 'chart_title': task['chart_info'].get('chartTitle', 'Unknown'),
    #                 'target_month_year': task['target_month_year'],
    #                 'point_data': point_data,
    #                 'error': str(e),
    #                 'timestamp': datetime.now().isoformat(),
    #                 'browser_id': browser_id,
    #                 'success': False
    #             }
    #             return error_result                
    #         finally:
    #             await context.close()
    #             await browser.close()
     
    async def process_chart_combination(self, combination, target_month_year, browser_id):
        """Process a single chart-point combination in its own browser"""
        if isinstance(target_month_year, list) and len(target_month_year) > 0:
            target_month_year = target_month_year[0]
        chart_id = combination.get('chart_id', 'unknown')
        target_month = combination.get('target_month_year', 'unknown')
        matching_points = combination.get('matching_points', [])        
        log_info(f"🔄 Browser {browser_id}: Processing {chart_id} - {target_month}")
        log_info(f"Points to process: {len(matching_points)}")
        
        async with async_playwright() as playwright:
            browser, context, page = await self.create_authenticated_browser_context(playwright, headless=False)
            
            try:
                # Navigate to CPLaborOverview
                await page.goto("https://sampackag.fixedops.cc/CPLaborOverview", timeout=30000)
                await page.wait_for_load_state("networkidle", timeout=15000)
                await asyncio.sleep(2)                
                results = []                
                # Process each matching point in this combination
                for point_idx, point in enumerate(matching_points):
                    try:
                        point_label = point.get('xLabel', f'Point_{point_idx}')
                        point_value = point.get('value', 'unknown')                        
                        log_info(f"   Browser {browser_id}: Processing point {point_idx + 1}/{len(matching_points)}")
                        log_info(f"   Point: {point_label} (Value: {point_value})")                        
                        # Click on the data point and extract data
                        extracted_data = await self.click_and_extract_data(page, point,target_month, target_month_year)
                        
                        result = {
                            'chart_id': chart_id,
                            'chart_title': combination['chart_info'].get('chartTitle', 'Unknown'),
                            'target_month_year': target_month,
                            'clicked_point': point,
                            'extracted_data': extracted_data,
                            'timestamp': datetime.now().isoformat(),
                            'browser_id': browser_id,
                            'point_index': point_idx,
                            'success': extracted_data.get('click_success', False) if extracted_data else False
                        }                        
                        results.append(result)
                        # log_info(f"✅ Browser {browser_id}: Successfully processed point {point_label}")
                        
                        # Wait before processing next point
                        await asyncio.sleep(3)
                    
                    except Exception as e:
                        log_error(f"❌ Browser {browser_id}: Error processing point {point.get('xLabel', 'unknown')}: {e}")
                        
                        error_result = {
                            'chart_id': chart_id,
                            'chart_title': combination['chart_info'].get('chartTitle', 'Unknown'),
                            'target_month_year': target_month,
                            'clicked_point': point,
                            'error': str(e),
                            'timestamp': datetime.now().isoformat(),
                            'browser_id': browser_id,
                            'point_index': point_idx,
                            'success': False
                        }
                        results.append(error_result)                
                combination['processing_status'] = 'completed'
                combination['results'] = results                
                log_info(f"✅ Browser {browser_id}: Completed {chart_id} - {len(results)} results")
                return combination                
            except Exception as e:
                log_error(f"❌ Browser {browser_id}: Error processing combination {chart_id}: {e}")
                combination['processing_status'] = 'failed'
                combination['error'] = str(e)
                return combination                
            finally:
                await context.close()
                await browser.close()
    async def run_complete_process(self):
        """Run the complete chart processing workflow with enhanced legend control"""
        log_info("🚀 Starting complete chart processing workflow with enhanced legend control...")

        try:
            # Step 1: Create chart-point combinations            
            combinations = await self.create_chart_point_combinations(TARGET_MONTHS_YEARS)            
            if not combinations:
                log_error("❌ No chart-point combinations found")
                return None
            log_info(f"✅ Created {len(combinations)} chart-point combinations")
            # Step 2: Process all combinations in parallel with 3 browsers
            log_info("🔄 Step 2: Processing combinations in parallel with 3 browsers...")
            results = await self.process_all_combinations_parallel(combinations)
            if not results:
                log_info("❌ No results from processing")
                return None
            # Step 3: Save results
            log_info("💾 Step 3: Saving results...")
            await self.save_results(results)
            log_info("✅ Complete chart processing workflow finished successfully")
            log_info(f"📈 Final Summary:")
            log_info(f"   - Total combinations processed: {len(combinations)}")
            log_info(f"   - Total tasks completed: {results.get('total_processed', 0)}")
            log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
            return results
        except Exception as e:            
            log_error(f"❌ Error in complete process: {e}")
            traceback.print_exc()
            return None
    async def save_results(self, results):
        """Save processing results to files with enhanced formatting"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # Create results directory if it doesn't exist
            results_dir = "chart_processing_results"
            os.makedirs(results_dir, exist_ok=True)
            
            # Helper function to modify values for specific chart conditions
            def modify_chart_data(data_list):
                """Modify point_data and extracted_data values for specific chart conditions"""
                modified_data = []
                modifications_count = 0
                
                for item in data_list:
                    # Create a deep copy to avoid modifying original data
                    modified_item = json.loads(json.dumps(item, default=str))
                    
                    # Check if this item matches our conditions
                    chart_title = modified_item.get('chart_title', '')
                    chart_id = str(modified_item.get('chart_id', ''))
                    
                    if chart_title == "CP Labor GP %" and chart_id == "1073":
                        # Modify point_data value
                        if 'point_data' in modified_item and 'value' in modified_item['point_data']:
                            try:
                                original_value = float(modified_item['point_data']['value'])
                                new_value = original_value * 100
                                modified_item['point_data']['value'] = str(new_value)
                                log_info(f"🔄 Modified point_data value for Chart {chart_id}: {original_value} -> {new_value}")
                                modifications_count += 1
                            except (ValueError, TypeError) as e:
                                log_error(f"⚠️ Could not convert point_data value to float for Chart {chart_id}: {e}")
                        
                        # Modify extracted_data value
                        if ('extracted_data' in modified_item and 
                            'point_data' in modified_item['extracted_data'] and 
                            'value' in modified_item['extracted_data']['point_data']):
                            try:
                                original_value = float(modified_item['extracted_data']['point_data']['value'])
                                new_value = original_value * 100
                                modified_item['extracted_data']['point_data']['value'] = str(new_value)
                                log_info(f"🔄 Modified extracted_data value for Chart {chart_id}: {original_value} -> {new_value}")
                                modifications_count += 1
                            except (ValueError, TypeError) as e:
                                log_error(f"⚠️ Could not convert extracted_data value to float for Chart {chart_id}: {e}")
                    
                    modified_data.append(modified_item)
                
                if modifications_count > 0:
                    log_info(f"✨ Total modifications made: {modifications_count}")
                
                return modified_data
            
            # Save successful results with modifications
            if results.get('successful'):
                log_info(f"📝 Processing successful results for modifications...")
                modified_successful = modify_chart_data(results['successful'])
                success_file = os.path.join(results_dir, f"chart_processing_success_{timestamp}.json")
                with open(success_file, 'w', encoding='utf-8') as f:
                    json.dump(modified_successful, f, indent=2, default=str, ensure_ascii=False)
                log_info(f"✅ Successful results saved to {success_file}")

            # Save failed results (no modification needed for failed results)
            if results.get('failed'):
                failed_file = os.path.join(results_dir, f"chart_processing_failed_{timestamp}.json")
                with open(failed_file, 'w', encoding='utf-8') as f:
                    json.dump(results['failed'], f, indent=2, default=str, ensure_ascii=False)
                log_info(f"⚠️ Failed results saved to {failed_file}")

            # Save all results (combined) with modifications
            if results.get('all_results'):
                log_info(f"📝 Processing all results for modifications...")
                modified_all_results = modify_chart_data(results['all_results'])
                all_results_file = os.path.join(results_dir, f"chart_processing_all.json")
                with open(all_results_file, 'w', encoding='utf-8') as f:
                    json.dump(modified_all_results, f, indent=2, default=str, ensure_ascii=False)
                log_info(f"📄 All results saved to {all_results_file}")

            # Save enhanced summary
            summary = {
                'timestamp': timestamp,
                'processing_method': 'enhanced_legend_control_parallel',
                'target_months_years': TARGET_MONTHS_YEARS,
                'max_concurrent_browsers': MAX_CONCURRENT_BROWSERS,
                'total_processed': results.get('total_processed', 0),
                'total_charts': results.get('total_charts', 0),
                'rounds_processed': results.get('rounds_processed', 0),
                'successful_count': len(results.get('successful', [])),
                'failed_count': len(results.get('failed', [])),
                'success_rate': results.get('success_rate', 0),
                'processing_statistics': {
                    'charts_with_data': results.get('total_charts', 0),
                    'average_points_per_chart': results.get('total_processed', 0) / results.get('total_charts', 1) if results.get('total_charts', 0) > 0 else 0,
                    'total_rounds': results.get('rounds_processed', 0)
                }
            }

            summary_file = os.path.join(results_dir, f"chart_processing_summary_{timestamp}.json")
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, default=str, ensure_ascii=False)
            log_info(f"   Enhanced summary saved to {summary_file}")

            # Save CSV summary for easy analysis
            csv_file = os.path.join(results_dir, f"chart_processing_summary_{timestamp}.csv")
            with open(csv_file, 'w', encoding='utf-8') as f:
                f.write("Metric,Value\n")
                f.write(f"Timestamp,{timestamp}\n")
                f.write(f"Processing Method,enhanced_legend_control_parallel\n")
                f.write(f"Total Processed,{results.get('total_processed', 0)}\n")
                f.write(f"Total Charts,{results.get('total_charts', 0)}\n")
                f.write(f"Successful,{len(results.get('successful', []))}\n")
                f.write(f"Failed,{len(results.get('failed', []))}\n")
                f.write(f"Success Rate %,{results.get('success_rate', 0):.1f}\n")
                f.write(f"Rounds Processed,{results.get('rounds_processed', 0)}\n")
            log_info(f"📈 CSV summary saved to {csv_file}")

            # Perform comparison with CP overview results
            log_info("\n🔍 Step 4: Performing UI vs DB comparison...")
            await self.compare_with_cp_overview_results(results, timestamp)

        except Exception as e:
            log_error(f"Error saving results: {e}", exc_info=True)            
            traceback.print_exc()    

    async def compare_with_cp_overview_results(self, results, timestamp):
        """Compare chart processing results with CP overview DB calculated values"""
        try:
            log_info("🔍 Starting comparison with CP overview results...")

            # Find the latest CP overview results file
            cp_overview_file = self.find_latest_cp_overview_file()
            if not cp_overview_file:
                log_info("⚠️ No CP overview results file found. Skipping comparison.")
                return

            log_info(f"📄 Found CP overview file: {cp_overview_file}")

            # Load CP overview data
            with open(cp_overview_file, 'r', encoding='utf-8') as f:
                cp_data = json.load(f)

            # Extract UI values and chart details from chart processing results
            ui_values, chart_details = self.extract_ui_values_from_results(results)

            # Extract DB values from CP overview data
            db_values = self.extract_db_values_from_cp_overview(cp_data)
            db_json_path='F:/Sinju_work/fopc-ui-validation/fopc-ui-validation/lib/pattern/sampackag/chart_processing_results/db_calculated_value_labor.json'
            ui_json_path='F:/Sinju_work/fopc-ui-validation/fopc-ui-validation/lib/pattern/sampackag/chart_processing_results/chart_processing_all.json'
            # Perform comparison - fix parameter order: ui_json_path first, then db_json_path
            # comparison_results = self.compare_ui_db_values(ui_values, db_values, chart_details)
            comparison_results = self.compare_ui_db_values(ui_json_path, db_json_path)
            # Save comparison results
            await self.save_comparison_results(comparison_results, timestamp)

            log_info("✅ UI vs DB comparison completed successfully")

        except Exception as e:
            log_error(f"Error in comparison: {e}")            
            traceback.print_exc()

    def find_latest_cp_overview_file(self):
        """Find the latest CP overview results file"""
        try:
            # Look for cp_overview files in current directory and results directory
            search_patterns = [
                "cp_overview_single_month_results.json",
                "cp_overview_12_months_results.json",
                "chart_processing_results/db_calculated_values.json"
            ]

            latest_file = None
            latest_time = 0

            for pattern in search_patterns:
                if '*' in pattern:
                    # Handle wildcard patterns                    
                    files = glob.glob(pattern)
                    for file in files:
                        if os.path.exists(file):
                            file_time = os.path.getmtime(file)
                            if file_time > latest_time:
                                latest_time = file_time
                                latest_file = file
                else:
                    # Handle exact file names
                    if os.path.exists(pattern):
                        file_time = os.path.getmtime(pattern)
                        if file_time > latest_time:
                            latest_time = file_time
                            latest_file = pattern

            return latest_file

        except Exception as e:
            log_error(f"❌ Error finding CP overview file: {e}")
            return None

    def extract_ui_values_from_results(self, results):
        """Extract UI values and chart details from chart processing results"""
        ui_values = {}
        chart_details = {}

        try:
            successful_results = results.get('successful', [])

            for result in successful_results:
                if 'extracted_data' in result and 'extraction_data' in result['extracted_data']:
                    extraction_data = result['extracted_data']['extraction_data']
                    target_month = result.get('target_month_year', 'Unknown')

                    # Extract chart information
                    chart_title = result.get('chart_title', 'Unknown Chart')
                    chart_id = result.get('chart_id', 'Unknown ID')
                    chart_name_with_id = f"{chart_title}({chart_id})"

                    # Also try to get chart info from chart_info if available
                    if 'chart_info' in result:
                        chart_info = result['chart_info']
                        if 'chartNameWithId' in chart_info:
                            chart_name_with_id = chart_info['chartNameWithId']
                        elif 'chartId' in chart_info and 'chartTitle' in chart_info:
                            chart_title = chart_info['chartTitle']
                            chart_id = chart_info['chartId']
                            chart_name_with_id = f"{chart_title}({chart_id})"

                    # Extract dataset label from clicked point data
                    # dataset_label = "Unknown Line"
                    # if 'clicked_point_data' in result:
                    #     point_data = result['clicked_point_data']
                    #     if 'dataset' in point_data and 'label' in point_data['dataset']:
                    #         dataset_label = point_data['dataset']['label']
                    dataset_label = "Unknown Line"
                    if 'point_data' in result:
                        point_data = result['point_data']
                        if 'datasetLabel' in point_data:
                            dataset_label = point_data['datasetLabel']

                    if 'mui_grid_data' in extraction_data:
                        for container in extraction_data['mui_grid_data']:
                            if 'items' in container:
                                for item in container['items']:
                                    title = item.get('title', '')
                                    value = item.get('value', '')

                                    # Clean the value (remove $ and commas)
                                    clean_value = value.replace('$', '').replace(',', '').replace('%', '').strip()

                                    # Store the value with month and title as key
                                    key = f"{title} ({target_month})"
                                    if key not in ui_values:
                                        ui_values[key] = clean_value

                                        # Store chart details for this key
                                        chart_details[key] = {
                                            'chart_title': chart_title,
                                            'chart_id': chart_id,
                                            'chart_name_with_id': chart_name_with_id,
                                            'dataset_label': dataset_label,
                                            'target_month': target_month,
                                            'drilldown_label': title
                                        }

            log_info(f"   Extracted {len(ui_values)} UI values from chart processing results")
            return ui_values, chart_details

        except Exception as e:
            log_error(f"❌ Error extracting UI values: {e}")
            return {}, {}

    def extract_db_values_from_cp_overview(self, cp_data):
        """Extract DB values from CP overview data"""
        db_values = {}

        try:
            # Handle both single month and 12 months data structures
            summary_data = None

            # Check for single month data
            if 'cp_summary_overview_single_month' in cp_data:
                summary_data = cp_data['cp_summary_overview_single_month'].get('summary_format', {})
            # Check for 12 months data
            elif 'cp_summary_overview_12_months' in cp_data:
                summary_data = cp_data['cp_summary_overview_12_months'].get('summary_format', {})

            if summary_data:
                for metric, values_list in summary_data.items():
                    for value_str in values_list:
                        # Extract value and month from format: "value (YYYY-MM-DD)"
                        if '(' in value_str and ')' in value_str:
                            db_value_part = value_str.split('(')[0].strip()
                            month_part = value_str.split('(')[1].replace(')', '').strip()

                            # Clean DB value
                            clean_value = db_value_part.replace('$', '').replace(',', '').replace('%', '').strip()

                            # Create key matching UI format
                            key = f"{metric} ({month_part})"
                            db_values[key] = clean_value

            log_info(f"   Extracted {len(db_values)} DB values from CP overview data")
            return db_values

        except Exception as e:
            log_error(f"❌ Error extracting DB values: {e}")
            return {}
    def compare_ui_db_values(self, ui_json_path, db_json_path):
        try:
            log_info(f"📄 Loading UI data from: {ui_json_path}")
            log_info(f"📄 Loading DB data from: {db_json_path}")

            with open(ui_json_path, 'r') as f:
                ui_data = json.load(f)

            with open(db_json_path, 'r') as f:
                db_data = json.load(f)

            log_info(f"   UI data type: {type(ui_data)}")
            log_info(f"   DB data type: {type(db_data)}")

            if isinstance(ui_data, list):
                log_info(f"   UI data is a list with {len(ui_data)} items")
                if ui_data:
                    log_info(f"   First UI item keys: {list(ui_data[0].keys()) if isinstance(ui_data[0], dict) else 'Not a dict'}")
            elif isinstance(ui_data, dict):
                log_info(f"   UI data keys: {list(ui_data.keys())}")

            if isinstance(db_data, dict):
                log_info(f"   DB data keys: {list(db_data.keys())}")
                # Print first few characters of the JSON for structure analysis
                log_info(f"   DB data sample: {str(db_data)[:500]}...")
            elif isinstance(db_data, list):
                log_info(f"   DB data is a list with {len(db_data)} items")
            else:
                log_info(f"   Unexpected DB data type: {type(db_data)}")
                log_info(f"   DB data content: {str(db_data)[:500]}...")
        except Exception as e:
            log_error(f"❌ Error loading JSON files: {e}")
            return []

        # Handle DB data - extract both monthly_data and summary_format structures
        db_flat_map = {}
        monthly_data = {}

        if isinstance(db_data, dict):
            log_info(f"   Processing DB data as dictionary with {len(db_data)} keys")

            # Extract monthly_data for first comparison (ui_line_value vs monthly_data)
            if 'cp_summary_overview_single_month' in db_data:
                monthly_data_section = db_data.get('cp_summary_overview_single_month', {}).get('monthly_data', {})
                if monthly_data_section:
                    log_info(f"   Found monthly_data with {len(monthly_data_section)} months")
                    monthly_data = monthly_data_section

                # Look for the summary_format structure for second comparison
                summary_format = db_data.get('cp_summary_overview_single_month', {}).get('summary_format', {})
                log_info("   Found cp_summary_overview_single_month structure")
            elif 'summary_format' in db_data:
                summary_format = db_data.get('summary_format', {})
                log_info("   Found direct summary_format structure")

            if summary_format:
                log_info(f"   Found summary_format with {len(summary_format)} metrics")
                # Extract values from summary_format structure
                for metric_name, value_list in summary_format.items():
                    if isinstance(value_list, list) and value_list:
                        for value_str in value_list:
                            # Parse format like "373977.76 (2023-11-01)" or "72.0% (2023-11-01)"
                            match = re.match(r"([\d\.\-]+)(%)?\s+\(([^)]+)\)", value_str.strip())
                            if match:
                                number, percent, date = match.groups()
                                db_key = f"{metric_name} ({date})"
                                db_value = number + (percent if percent else "")
                                db_flat_map[db_key] = db_value
                                log_info(f"   Mapped: {db_key} = {db_value}")
            else:
                log_info("⚠️ No summary_format found in DB data")
                # Fallback: use the data directly if it's already flat
                db_flat_map = db_data
        else:
            log_info(f"⚠️ Expected DB data to be a dictionary, got {type(db_data)}")
            # Try to extract from complex structure if needed
            if isinstance(db_data, list) and db_data and isinstance(db_data[0], dict):
                # Look for any dictionary that might contain the calculated values
                for item in db_data:
                    if isinstance(item, dict):
                        # Look for keys that look like metric names
                        for key, value in item.items():
                            if isinstance(value, (str, int, float)) and key not in ['task_id', 'chart_id', 'timestamp']:
                                db_flat_map[key] = str(value)

        log_info(f"   Created DB flat map with {len(db_flat_map)} entries")
        if db_flat_map:
            log_info(f"   DB flat map keys: {list(db_flat_map.keys())[:10]}...")  # Show first 10 keys

        if not db_flat_map:
            log_info("⚠️ No DB data found. Creating comparison with 'Not Found' values.")
            # We'll still create comparison results but mark DB values as "Not Found"

        comparison_results = []

        if not isinstance(ui_data, list):
            log_info(f"⚠️ Expected UI data to be a list, got {type(ui_data)}")
            return []

        log_info(f"   Processing {len(ui_data)} UI charts")

        for chart_idx, chart in enumerate(ui_data):
            try:
                if not isinstance(chart, dict):
                    log_info(f"⚠️ Chart {chart_idx} is not a dictionary, skipping")
                    continue

                chart_name_with_id = f"{chart.get('chart_title', 'Unknown')}({chart.get('chart_id', 'Unknown')})"
                chart_title = chart.get('chart_title', 'Unknown')  # Extract just the chart title
                line_name = str(chart.get("dataset_label", "Unknown Line"))  # Ensure it's a string
                formatted_date = str(chart.get("target_month_year", "Unknown Date"))  # Ensure it's a string

                # Line value for first comparison
                if "%" in line_name:
                    raw_value = chart.get("point_data", {}).get("value", "0")
                    ui_line_value = float(raw_value) * 100
                else:
                    raw_value = chart.get("point_data", {}).get("value", "0")
                    ui_line_value = float(raw_value) 
                    
                ui_line_value_clean = str(ui_line_value).replace("$", "").replace(",", "").replace("%", "").strip()

                # FIRST COMPARISON: Compare ui_line_value with monthly_data using CHART NAME instead of line name
                line_match = False
                monthly_db_value = "Not Found"
                # print(monthly_data,"monthly_data+++++++++")
                if formatted_date in monthly_data:
                    month_data = monthly_data[formatted_date]

                    # Map CHART NAMES to monthly_data keys instead of line names
                    chart_to_monthly_mapping = {
                        "CP Labor Revenue": "labor_revenue",
                        "Labor Hours Per RO": "labor_hours_per_ro", 
                        "CP Combined Revenue": "combined_revenue",
                        "Labor Gross Profit": "labor_gross_profit",
                        "Average Labor Sale Per RO": "Labor_Sales_Per_RO",
                        "RO Count": "ro_count",
                        "CP Labor GP %": "labor_gross_profit_percentage",
                        "ELR - Repair": "Repair_ELR", 
                        "Job Count": "job_count",
                        "Labor Sold Hours": "labor_sold_hours",
                        "ELR - Total Shop": "effective_labor_rate_cp",
                        "CP ELR - Maintenance and Competitive": "Effective_Labor_Rate_Main_and_Competitive"
                    }

                    # Use chart_title instead of line_name for mapping
                    monthly_key = chart_to_monthly_mapping.get(chart_title, None)
                    if monthly_key and monthly_key in month_data:
                        monthly_db_value = str(month_data[monthly_key])

                        # Compare values
                        try:
                            ui_floats = float(ui_line_value_clean) if ui_line_value_clean else 0
                            db_floats = float(str(monthly_db_value)) if monthly_db_value else 0
                            line_match = abs(ui_floats - db_floats) < 0.01
                        except ValueError:
                            line_match = ui_line_value_clean == str(monthly_db_value)

                        log_info(f"🔍 Line comparison: {chart_title} -> UI: {ui_line_value_clean}, DB: {monthly_db_value}, Match: {line_match}")
                    else:
                        log_info(f"⚠️ No monthly_data mapping found for chart: {chart_title}")
                        # Try to find a close match in monthly_data keys
                        available_keys = list(month_data.keys())
                        log_info(f"   Available monthly_data keys: {available_keys}")
                else:
                    log_info(f"⚠️ Date {formatted_date} not found in monthly_data")

                # Extract drilldown fields - only from container_index 1
                extracted_items = chart.get("extracted_data", {}).get("extraction_data", {}).get("mui_grid_data", [])

                if not extracted_items:
                    log_info(f"⚠️ No extracted items found for chart {chart_name_with_id}")
                    continue

                # Filter for only container_index 1
                container_1_items = []
                for grid in extracted_items:
                    if not isinstance(grid, dict):
                        continue

                    # Only process container_index 0
                    if grid.get("container_index") == 0:
                        container_1_items.extend(grid.get("items", []))
                        log_info(f"   Found {len(grid.get('items', []))} items in container_index 0 for chart {chart_name_with_id}")

                    if not container_1_items:
                        log_info(f"⚠️ No items found in container_index 0 for chart {chart_name_with_id}")
                        # Let's also check what container indexes are available
                        if grid.get("container_index") == 1:
                            container_1_items.extend(grid.get("items", []))
                            log_info(f"   Found {len(grid.get('items', []))} items in container_index 1 for chart {chart_name_with_id}")
                        available_indexes = []
                        for grid in extracted_items:
                            if isinstance(grid, dict) and "container_index" in grid:
                                available_indexes.append(grid["container_index"])
                        log_info(f"   Available container indexes: {available_indexes}")
                        continue

                for item in container_1_items:
                    if not isinstance(item, dict):
                        continue

                    title = str(item.get("title", ""))  # Ensure it's a string
                    value = str(item.get("value", ""))  # Ensure it's a string

                    if not title or not value:
                        continue

                    ui_extracted_value = str(value).replace("$", "").replace(",", "").replace("%", "").strip()

                    # Map UI field names to exact DB field names
                    title_mapping = {
                        "Labor Sale - Customer Pay": "Labor Sale - Customer Pay",
                        "Total Parts Sale": "Total Parts Sales",  # Note: DB has "Sales" not "Sale"
                        
                        "Labor Gross Profit": "Labor Gross Profit",
                    
                        "Labor Gross Profit %": "Labor Gross Profit %",                        
                        "Labor Sale - Repair": "Labor Sale - Repair",
                        "Labor Sold Hours - Repair": "Labor Sold Hours - Repair",
                        "Labor Sale - Maint and Comp": "Labor Sale - Maint and Comp",
                        "Labor Sold Hours - Maint and Comp": "Labor Sold Hours - Maint and Comp",  # DB uses "ELR" instead of full name
                        "Effective Labor Rate - Maint and Comp": "Effective Labor Rate - Maint and Comp",
                        "Repair Hours": "Repair Hours",
                        "Labor Sale - Repair": "Labor Sale - Repair",
                        "Labor Sold Hours - Repair": "Labor Sold Hours - Repair",
                        "Effective Labor Rate - Repair": "Effective Labor Rate - Repair",
                        "Average Labor Sale Per RO": "Labor Sale/RO",
                        "Job Count": "Job Count",
                        "RO Count": "RO Count"
                    }

                    # Get the correct DB field name
                    db_field_name = title_mapping.get(title, title)

                    # Build DB lookup key with correct field name
                    db_key = f"{db_field_name} ({formatted_date})"
                    db_value = db_flat_map.get(db_key, "Not Found")

                    # Debug: Show the lookup attempt
                    log_info(f"🔍 Looking up: '{db_key}' -> {db_value}")
                    if db_value == "Not Found":
                        log_info(f"⚠️ Available keys: {list(db_flat_map.keys())[:5]}...")

                    # SECOND COMPARISON: Match drilldown extracted values with summary_format
                    drilldown_match = False
                    try:
                        ui_float = float(ui_extracted_value) if ui_extracted_value else 0
                        # Ensure db_value is a string before calling replace()
                        db_value_str = str(db_value) if db_value != "Not Found" else "0"
                        db_float = float(db_value_str.replace("%", "")) if db_value != "Not Found" else 0
                        drilldown_match = abs(ui_float - db_float) < 0.01
                    except ValueError:
                        drilldown_match = ui_extracted_value == str(db_value)

                    # SINGLE MATCH: Both line comparison AND drilldown comparison must be TRUE
                    match = line_match and drilldown_match

                    result = {
                        "Chart_Namewith_id": chart_name_with_id,
                        "Line_Name_Legend": f"{line_name} ({formatted_date})",
                        "UI_Line_Data_Point_Value": ui_line_value,
                        "Drilldown_Extracted_Field": title,
                        "UI_Extracted_Value": ui_extracted_value,
                        "DB_Tooltip_Value": monthly_db_value,
                        "DB_Calculated_Field": db_field_name,  # Use the mapped DB field name
                        "DB_Calculated_Value": db_value,  # Second comparison DB value
                        "Match": "TRUE" if match else "FALSE"  # Single match result
                    }

                    comparison_results.append(result)

            except Exception as e:
                log_error(f"❌ Error processing chart {chart_idx}: {e}")
                continue

        log_info(f"   Generated {len(comparison_results)} comparison results")
        return comparison_results
    
    
    
    async def save_comparison_results(self, comparison_results, timestamp):
        """Save comparison results to CSV file"""
        try:
            if not comparison_results:
                log_info("⚠️ No comparison results to save")
                return

            # Create results directory if it doesn't exist
            results_dir = "chart_processing_results"
            os.makedirs(results_dir, exist_ok=True)

            # Save comparison CSV with Excel format
            comparison_csv_file = os.path.join(results_dir, f"ui_db_comparison_{timestamp}.csv")

            with open(comparison_csv_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    "Chart_Namewith_id",  # Note: no underscore between Name and with
                    "Line_Name_Legend",
                    "UI_Line_Data_Point_Value",
                    "Drilldown_Extracted_Field",
                    "UI_Extracted_Value",
                    "DB_Tooltip_Value",  # Added missing comma
                    "DB_Calculated_Field",
                    "DB_Calculated_Value",
                    "Match"
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                # writer.writerow(["Chart Name(ID)", "Line Name(Month/Year)", "Tooltip Value", "Extracted Field", "Match (True/False)"])
                writer.writerows(comparison_results)

            log_info(f"📄 Comparison results saved to: {comparison_csv_file}")

            # Print summary statistics
            total_comparisons = len(comparison_results)
            matches = sum(1 for result in comparison_results if result["Match"] == "TRUE")
            mismatches = total_comparisons - matches
            match_rate = (matches / total_comparisons * 100) if total_comparisons > 0 else 0

            log_info(f"\n   Comparison Summary:")
            log_info(f"   - Total comparisons: {total_comparisons}")
            log_info(f"   - Matches: {matches}")
            log_info(f"   - Mismatches: {mismatches}")
            log_info(f"   - Match rate: {match_rate:.1f}%")

            # Save detailed comparison JSON
            comparison_json_file = os.path.join(results_dir, f"ui_db_comparison_detailed_{timestamp}.json")
            with open(comparison_json_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "timestamp": timestamp,
                    "summary": {
                        "total_comparisons": total_comparisons,
                        "matches": matches,
                        "mismatches": mismatches,
                        "match_rate": match_rate
                    },
                    "detailed_results": comparison_results
                }, f, indent=2, ensure_ascii=False)

            log_info(f"📄 Detailed comparison saved to: {comparison_json_file}")

        except Exception as e:
            log_error(f"❌ Error saving comparison results: {e}")            
            traceback.print_exc()
def generate_ui_db_comparison_html(html_path, comparison_data, timestamp, tenant="Unknown", store="Unknown", role="Unknown"):
    """Generate HTML report for UI-DB comparison results"""   
    # Calculate statistics
    total = len(comparison_data)
    passed = sum(1 for entry in comparison_data if entry.get('Match', '').upper() == 'TRUE')
    failed = total - passed    
    # Group by chart name
    grouped_data = defaultdict(list)
    for entry in comparison_data:
        chart_name = entry.get('Chart_Namewith_id', 'Unknown Chart')
        grouped_data[chart_name].append(entry)

    html_template = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>UI vs DB Comparison Report</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
            .comparison-row {{ display: flex; justify-content: space-between; margin-bottom: 10px; }}
            .ui-value {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-right: 10px; }}
            .db-value {{ background-color: #e9ecef; padding: 10px; border-radius: 5px; margin-left: 10px; }}
            .match-indicator {{ font-weight: bold; padding: 5px 10px; border-radius: 3px; }}
            .match-true {{ background-color: #d4edda; color: #155724; }}
            .match-false {{ background-color: #f8d7da; color: #721c24; }}
            .pre-json {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="mb-4">UI vs DB Comparison Report</h1>
            <div class="mb-4">
                <strong>Tenant:</strong> {tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
                <strong>Report Timestamp:</strong> {timestamp}<br>
            </div>

            <div class="d-flex gap-3 mb-4">
                <span class="badge bg-success">Passed: {passed}</span>
                <span class="badge bg-danger">Failed: {failed}</span>
                <span class="badge bg-secondary">Total: {total}</span>
                <span class="badge bg-info">Match Rate: {(passed/total*100):.1f}%</span>
            </div>

            <div class="accordion" id="reportAccordion">
    """
    for chart_idx, (chart_name, entries) in enumerate(grouped_data.items()):
        # Check if all entries for this chart pass
        chart_pass = all(entry.get('Match', '').upper() == 'TRUE' for entry in entries)
        badge_class = "badge-pass" if chart_pass else "badge-fail"
        badge_text = "All Passed" if chart_pass else "Has Failures"
        chart_id = f"chart{chart_idx}"
        html_template += f"""
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading-{chart_id}">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#{chart_id}" aria-expanded="false" aria-controls="{chart_id}">
                    {chart_name} <span class="ms-3 badge {badge_class}">{badge_text}</span>
                    <small class="ms-2 text-muted">({len(entries)} comparisons)</small>
                </button>
            </h2>
            <div id="{chart_id}" class="accordion-collapse collapse" aria-labelledby="heading-{chart_id}" data-bs-parent="#reportAccordion">
                <div class="accordion-body">
        """
        for idx, entry in enumerate(entries):
            match = entry.get('Match', '').upper() == 'TRUE'
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{chart_id}-entry-{idx}"
            
            # Extract values for display
            line_name = entry.get('Line_Name_Legend', 'Unknown')
            tooltip_value = entry.get('UI_Line_Data_Point_Value', 'N/A')
            extracted_field = entry.get('Drilldown_Extracted_Field', 'Unknown Field')
            db_tooltip_value = entry.get('DB_Tooltip_Value', 'N/A')
            ui_value = entry.get('UI_Extracted_Value', 'N/A')
            db_field = entry.get('DB_Calculated_Field', 'Unknown Field')
            db_value = entry.get('DB_Calculated_Value', 'N/A')
            
            html_template += f"""
            <div class="card mb-2">
                <div class="card-header" data-bs-toggle="collapse" data-bs-target="#{sub_id}" aria-expanded="false" style="cursor:pointer;">
                    <strong>{extracted_field}</strong> ({line_name}) <span class="ms-2 badge {sub_badge}">{sub_text}</span>
                </div>
                <div id="{sub_id}" class="collapse">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h6>Chart Information:</h6>
                                <p><strong>Line Name:</strong> {line_name}</p>
                                <p><strong>UI Tooltip Value:</strong> {tooltip_value}</p>
                                <p><strong>DB Tooltip Value:</strong> {db_tooltip_value}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Match Status:</h6>
                                <span class="match-indicator {'match-true' if match else 'match-false'}">
                                    {'✓ MATCH' if match else '✗ MISMATCH'}
                                </span>
                            </div>
                        </div>
                        
                        <div class="comparison-row">
                            <div class="ui-value flex-fill">
                                <h6>UI Extracted Value:</h6>
                                <p><strong>Field:</strong> {extracted_field}</p>
                                <p><strong>Value:</strong> {ui_value}</p>
                            </div>
                            <div class="db-value flex-fill">
                                <h6>DB Calculated Value:</h6>
                                <p><strong>Field:</strong> {db_field}</p>
                                <p><strong>Value:</strong> {db_value}</p>
                            </div>
                        </div>
                        
                        
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)    
    log_info(f"📄 HTML report generated: {html_path}")
async def generate_final_comparison_report(timestamp):
    """Generate a final consolidated comparison report with enhanced formatting"""
    try:
        log_info("   Generating final consolidated comparison report...")
        results_dir = "chart_processing_results"
        # Find the comparison CSV file
        comparison_csv_file = os.path.join(results_dir, f"ui_db_comparison_{timestamp}.csv")
        if not os.path.exists(comparison_csv_file):
            log_info(f"⚠️ Comparison CSV file not found: {comparison_csv_file}")
            return
        comparison_data = []
        with open(comparison_csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            comparison_data = list(reader)
        if not comparison_data:
            log_info("⚠️ No comparison data found")
            return
        # Calculate summary statistics
        total_comparisons = len(comparison_data)
        matches = sum(1 for row in comparison_data if row.get('Match', '').lower() == 'true')
        mismatches = total_comparisons - matches
        match_rate = (matches / total_comparisons * 100) if total_comparisons > 0 else 0

        # Generate simplified final report
        final_report_file = os.path.join(results_dir, f"final_ui_db_comparison_report_{timestamp}.csv")

        with open(final_report_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # Write simplified header with only requested columns
            writer.writerow(["UI", "DB Calculated", "Matches"])

            # Write data rows with simplified format
            for row in comparison_data:
                ui_value = row.get('UI_Value', '')
                db_value = row.get('DB_Value', '')
                match_value = row.get('Match', '')
                # Format the UI and DB values with metric names for clarity
                ui_display = f"{row.get('UI_Title', '')} ({row.get('Month', '')}): {ui_value}"
                db_display = f"{row.get('DB_Metric', '')} ({row.get('Month', '')}): {db_value}"
                writer.writerow([ui_display, db_display, match_value])
        # Also create an Excel file with conditional formatting for highlighting
        try:
            # Create Excel workbook
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "UI vs DB Comparison"
            # Create styles
            gray_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
            blue_fill = PatternFill(start_color="305496", end_color="305496", fill_type="solid")
            yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
            center_alignment = Alignment(horizontal='center', vertical='center')
            bold_font = Font(bold=True)
            # Add "CP Overview" heading in first row (merged across all columns)
            ws.merge_cells('A1:I1')
            cp_overview_cell = ws['A1']
            cp_overview_cell.value = "CP Overview"
            cp_overview_cell.alignment = center_alignment
            cp_overview_cell.font = bold_font
            # Add "UI" heading in second row (columns C, D, E) with gray background
            ws.merge_cells('C2:E2')
            ui_cell = ws['C2']
            ui_cell.value = "UI"
            ui_cell.fill = gray_fill
            ui_cell.alignment = center_alignment
            ui_cell.font = bold_font

            # Add "Calculated" heading in second row (columns F, G,H) with blue background
            ws.merge_cells('F2:H2')
            calculated_cell = ws['F2']
            calculated_cell.value = "Calculated"
            calculated_cell.fill = blue_fill
            calculated_cell.alignment = center_alignment
            calculated_cell.font = bold_font

            # Define custom Excel column headers as requested
            custom_headers = [
                    "Chart Name(ID)",                # Custom heading
                    "Legend Name(Date)",         # Custom heading
                    "Tooltip Value",                 # Custom heading
                    "Extracted Field Name",               # Custom heading
                    "Extracted Value",
                    "Tooltip Value",
                    "Field Name",
                    "Value",
                    "Match (True/False)"
                ]
            # Original CSV headers mapping to custom headers
            original_headers = [
                "Chart_Namewith_id",
                "Line_Name_Legend",
                "UI_Line_Data_Point_Value",
                "Drilldown_Extracted_Field",
                "UI_Extracted_Value",
                "DB_Tooltip_Value",
                "DB_Calculated_Field",
                
                "DB_Calculated_Value",
                "Match"
            ]
            # Add custom headers to Excel (row 3)
            for col_idx, header in enumerate(custom_headers, start=1):
                # ws.cell(row=3, column=col_idx, value=header)
                header_cell = ws.cell(row=3, column=col_idx, value=header)
                header_cell.font = bold_font
            # Add data and apply conditional formatting (starting from row 4)
            for row_idx, row_data in enumerate(comparison_data, start=4):
                match_value = str(row_data.get('Match', '')).upper()
                for col_idx, original_header in enumerate(original_headers, start=1):
                    cell_value = row_data.get(original_header, '')
                    ws.cell(row=row_idx, column=col_idx, value=cell_value)
                    # Highlight row in yellow if match is FALSE
                    if match_value == 'FALSE':
                        ws.cell(row=row_idx, column=col_idx).fill = yellow_fill
            # Auto-adjust column widths (handle merged cells)
            for col_idx in range(1, len(custom_headers) + 1):
                max_length = 0
                column_letter = get_column_letter(col_idx)                
                # Check all cells in this column for maximum length
                for row in ws.iter_rows(min_col=col_idx, max_col=col_idx):
                    for cell in row:
                        if cell.value and not isinstance(cell, openpyxl.cell.MergedCell):
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass                
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            # Save Excel file
            excel_file = os.path.join(results_dir, f"final_ui_db_comparison_report_{timestamp}.xlsx")
            wb.save(excel_file)
            log_info(f"📄 Excel report with custom headers and highlighting saved to: {excel_file}")

        except ImportError:
            log_error("⚠️ openpyxl not available - Excel file with highlighting not created")
            log_info("💡 Install openpyxl to get Excel file with yellow highlighting: pip install openpyxl")

        log_info(f"📄 Final comparison report saved to: {final_report_file}")
        # Print final summary to console
        log_info(f"\n   FINAL COMPARISON SUMMARY:")
        log_info(f"   📈 Total Comparisons: {total_comparisons}")
        log_info(f"   ✅ Successful Matches: {matches}")
        log_info(f"   ❌ Mismatches: {mismatches}")
        log_info(f"      Match Rate: {match_rate:.1f}%")
        if mismatches > 0:
            log_info(f"\n⚠️  Found {mismatches} mismatches - check the final report for details")
        else:
            log_info(f"\n🎉 Perfect match! All UI and DB values are consistent!")
        html_report_file = os.path.join(results_dir, f"ui_db_comparison_report_{timestamp}.html")
        generate_ui_db_comparison_html(html_report_file, comparison_data, timestamp)
        return final_report_file
    except Exception as e:
        log_error(f"❌ Error generating final comparison report: {e}")        
        traceback.print_exc()
        return None
# Main execution
async def main():
    """Main function to run the enhanced chart processing with legend control"""
    log_info("Starting Parallel Chart Processing Application with 3 Browsers")    
    log_info("=" * 80)
    start_time = time.time()
    log_info(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Initialize components
    auth_manager = AuthManager()
    processor = MultiChartParallelProcessor(
        max_browsers=3,  # Parallel processing uses 3 browsers
        auth_manager=auth_manager
    )
    log_info(f"   - Processing mode: Parallel (3 browsers, different charts)")
    log_info(f"   - Max concurrent browsers: 3")
    log_info(f"   - Target months/years: {TARGET_MONTHS_YEARS}")
    log_info(f"   - Browser timeout: {BROWSER_TIMEOUT}ms")
    log_info("=" * 80)
    # Check if we have valid authentication
    if not auth_manager.load_auth_state():
        logger.warning("No valid authentication found. Setting up authentication...")
        

        async with async_playwright() as playwright:
            success = await auth_manager.setup_authentication(playwright)
            if not success:
                log_error("Authentication setup failed. Exiting.")                
                return
    else:
        log_info("Valid authentication found, proceeding with processing...")        
    # Run the complete processing workflow
    log_info("Starting parallel chart processing workflow with 3 browsers...")    
    results = await processor.run_complete_process()
    if results:
        log_info("Parallel processing with 3 browsers completed successfully!")
        log_info(f"Results: Total processed: {results.get('total_processed', 0)}, "
                   f"Charts: {results.get('total_charts', 0)}, "
                   f"Success rate: {results.get('success_rate', 0):.1f}%")
        log_info("\n" + "=" * 80)
        log_info(f"🎉 Parallel processing with 3 browsers completed successfully!")
        log_info(f"  Final Results:")
        log_info(f"   - Total tasks processed: {results.get('total_processed', 0)}")
        log_info(f"   - Charts processed: {results.get('total_charts', 0)}")
        log_info(f"   - Batches processed: {results.get('batches_processed', 0)}")
        log_info(f"   - Successful tasks: {len(results.get('successful', []))}")
        log_info(f"   - Failed tasks: {len(results.get('failed', []))}")
        log_info(f"   - Success rate: {results.get('success_rate', 0):.1f}%")
        log_info("=" * 80)
        # Additional statistics
        if results.get('successful'):
            log_info(f"✅ Parallel processing completed with {len(results['successful'])} successful extractions")
        if results.get('failed'):
            log_info(f"⚠️  {len(results['failed'])} tasks failed - check failed results file for details")

        # Generate final comparison report
        log_info("\n" + "=" * 80)
        log_info("🔍 GENERATING FINAL UI vs DB COMPARISON REPORT")
        log_info("=" * 80)
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            await processor.compare_with_cp_overview_results(results, timestamp)
            # Generate final consolidated CSV report
            await generate_final_comparison_report(timestamp)
            log_info("\n" + "=" * 80)
            log_info("✅ FINAL COMPARISON REPORT GENERATED SUCCESSFULLY!")
            log_info("📄 Check the 'chart_processing_results' directory for:")
            log_info(f"   - ui_db_comparison_{timestamp}.csv")
            log_info(f"   - ui_db_comparison_detailed_{timestamp}.json")
            log_info(f"   - final_ui_db_comparison_report_{timestamp}.csv")
            log_info("=" * 80)
            end_time = time.time()-start_time
            log_info(f"End Time: {end_time}")
        except Exception as comparison_error:
            log_error(f"Error generating comparison report: {comparison_error}", exc_info=True)            
            traceback.print_exc()
    else:
        log_error("❌ Parallel processing failed - check logs for details")

async def test_mui_structure():
    """Test function to verify MUI button structure detection"""
    log_info("🧪 Testing MUI button structure detection...")

    processor = MultiChartParallelProcessor()

    async with async_playwright() as playwright:
        browser, context, page = await processor.create_authenticated_browser_context(playwright, headless=False)

        try:
            # Navigate to CPLaborOverview
            await page.goto("https://sampackag.fixedops.cc/CPLaborOverview", timeout=30000)
            await page.wait_for_load_state("networkidle", timeout=15000)
            await asyncio.sleep(3)

            # Test MUI button detection
            log_info("🔍 Testing MUI button detection...")
            clicked_buttons = await processor.click_mui_chart_buttons(page)

            # Test chart discovery with MUI structure
            log_info("📊 Testing chart discovery...")
            charts_info = await processor.discover_charts()

            log_info(f"✅ Test completed:")
            log_info(f"   - Found {len(clicked_buttons)} MUI buttons")
            log_info(f"   - Discovered {len(charts_info)} charts")

            for i, chart in enumerate(charts_info):
                log_info(f"   Chart {i+1}: {chart.get('chartTitle', 'Unknown')} (ID: {chart.get('chartId', 'Unknown')})")

            # Test finding matching points for first chart if available
            if charts_info:
                first_chart = charts_info[0]
                chart_index = first_chart.get('canvasIndex', 0)
                chart_id = first_chart.get('chartId', 'unknown')

                log_info(f"🎯 Testing point finding for chart {chart_index} (ID: {chart_id})...")
                matching_points = await processor.find_matching_points_in_chart(page, chart_index, "11-2023", chart_id)
                log_info(f"   Found {len(matching_points)} matching points")

            return True

        except Exception as e:
            log_error(f"❌ Test failed: {e}")
            return False
        finally:
            await context.close()
            await browser.close()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        log_info(" Processing interrupted by user")
    except Exception as e:
        log_error(f"Unexpected error in main execution: {e}")        
        traceback.print_exc()