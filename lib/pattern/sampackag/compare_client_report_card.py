"""
Compares Client Report Card (1 month) values between UI and calculated results, and generates CSV, Excel, JSON, and HTML reports.
"""

import json
import csv
import os
import re
import logging
from openpyxl.styles import <PERSON>ont, Alignment, PatternFill
from openpyxl import Workbook
import openpyxl
from datetime import datetime
from dotenv import load_dotenv
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error

load_dotenv()

# Configure logging
#logging.basicConfig(level=log_info, format="%(levelname)s: %(message)s")

# Constants
Tenant = config.database_name
store = config.store_name
role = config.role


def clean_number(value):
    if value is None or value == "":
        return "Missing"
    if isinstance(value, str):
        value = re.sub(r"[$%,]", "", value)
        if re.match(r"\(.*\)", value):
            value = "-" + value.strip("()")
    try:
        return float(value)
    except (ValueError, TypeError):
        return value


def extract_main_values(data):
    keys_to_extract = ["Monthly FOPC", "Monthly DMS", "Total", "ROI", "Total Pts & Lbr GP Change", "Repair ELR Change"]
    return {key: {"Second Month": clean_number(data.get(key, "Missing"))} for key in keys_to_extract}

def extract_kpis_from_dict(data):
    kpis = {}
    for kpi, values in data.get("KPIs", {}).items():
        if isinstance(values, list) and len(values) == 3:
            kpis[kpi] = {
                "Second Month": clean_number(values[0]),
                "First Month": clean_number(values[1]),
                "Variance": clean_number(values[2]),
            }
        elif isinstance(values, dict):
            kpis[kpi] = {
                "Second Month": clean_number(values.get("Second Month", "Missing")),
                "First Month": clean_number(values.get("First Month", "Missing")),
                "Variance": clean_number(values.get("Variance", "Missing")),
            }
    return kpis

def extract_category_kpis(data, category):
    category_data = data.get(category, {})
    return {
        f"{category} - {kpi}": {
            "Second Month": clean_number(values.get("Second Month", "Missing")),
            "First Month": clean_number(values.get("First Month", "Missing")),
            "Variance": clean_number(values.get("Variance", "Missing")),
        }
        for kpi, values in category_data.items()
    }

def parse_value(val):
    if isinstance(val, str):
        try:
            return float(val.replace("$", "").replace("%", "").replace(",", ""))
        except ValueError:
            return val
    return val


# Helpers
def sanitize(name): return name.replace(" ", "-")
def get_output_folder(): return f"Playwright-report-{sanitize(Tenant)}_{sanitize(store)}_{sanitize(role)}"

def generate_playwright_style_html(html_path: str, json_report_data: list) -> None:
    """
    Generates a Bootstrap-styled HTML report from comparison results.
    """
    from collections import defaultdict

    passed = sum(1 for entry in json_report_data if entry['match'])
    failed = len(json_report_data) - passed
    total = len(json_report_data)

    # Group by section prefix
    sectioned_data = defaultdict(list)
    for entry in json_report_data:
        kpi = entry['kpi']
        if ' - ' in kpi:
            section, name = kpi.split(' - ', 1)
        else:
            section, name = 'KPI', kpi
        entry['clean_kpi'] = name
        sectioned_data[section].append(entry)

    html_template = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
    <head>
        <meta charset=\"UTF-8\">
        <title>Client Report Card One Month</title>
        <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
        <style>
            body {{ padding: 20px; font-family: Arial, sans-serif; }}
            .badge-pass {{ background-color: #28a745; }}
            .badge-fail {{ background-color: #dc3545; }}
            .card-header {{ cursor: pointer; }}
        </style>
    </head>
    <body>
        <div class=\"container\">
            <h1 class=\"mb-4\">Client Report Card One Month</h1>
            <div class=\"mb-4\">
                <strong>Tenant:</strong> {Tenant}<br>
                <strong>Store:</strong> {store}<br>
                <strong>Role:</strong> {role}<br>
                <strong>Generated At:</strong> {datetime.now().isoformat()}<br>
            </div>

            <div class=\"d-flex gap-3 mb-4\">
                <span class=\"badge bg-success\">Passed: {passed}</span>
                <span class=\"badge bg-danger\">Failed: {failed}</span>
                <span class=\"badge bg-secondary\">Total: {total}</span>
            </div>

            <div class=\"accordion\" id=\"reportAccordion\">
    """

    for s_idx, (section, entries) in enumerate(sectioned_data.items()):
        section_pass = all(entry['match'] for entry in entries)
        badge_class = "badge-pass" if section_pass else "badge-fail"
        badge_text = "Passed" if section_pass else "Failed"
        section_id = f"section{s_idx}"

        html_template += f"""
        <div class=\"accordion-item\">
            <h2 class=\"accordion-header\" id=\"heading-{section_id}\">
                <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#{section_id}\" aria-expanded=\"false\" aria-controls=\"{section_id}\">
                    {section} <span class=\"ms-3 badge {badge_class}\">{badge_text}</span>
                </button>
            </h2>
            <div id=\"{section_id}\" class=\"accordion-collapse collapse\" aria-labelledby=\"heading-{section_id}\" data-bs-parent=\"#reportAccordion\">
                <div class=\"accordion-body\">
        """

        for idx, entry in enumerate(entries):
            match = entry['match']
            sub_badge = "badge-pass" if match else "badge-fail"
            sub_text = "Passed" if match else "Failed"
            sub_id = f"{section_id}-entry-{idx}"
            html_template += f"""
            <div class=\"card mb-2\">
                <div class=\"card-header\" data-bs-toggle=\"collapse\" data-bs-target=\"#{sub_id}\" aria-expanded=\"false\" style=\"cursor:pointer;\">
                    {entry['clean_kpi']} <span class=\"ms-2 badge {sub_badge}\">{sub_text}</span>
                </div>
                <div id=\"{sub_id}\" class=\"collapse\">
                    <div class=\"card-body\">
                        <strong>UI:</strong>
                        <pre>{json.dumps(entry['ui'], indent=2)}</pre>
                        <strong>Calculated:</strong>
                        <pre>{json.dumps(entry['calculated'], indent=2)}</pre>
                    </div>
                </div>
            </div>
            """

        html_template += """
                </div>
            </div>
        </div>
        """

    html_template += """
            </div>
        </div>
        <script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js\"></script>
    </body>
    </html>
    """

    with open(html_path, "w", encoding="utf-8") as f:
        f.write(html_template)

# -------------------
# Main comparison function
# -------------------

def compare_client_report_card(file1_path, file2_path):
    """Compare two JSON files and save CSV/Excel/JSON/HTML reports."""

    # Use variables from config
    Tenant = getattr(config, "Tenant", "UnknownTenant")
    store = getattr(config, "store", "UnknownStore")
    role = getattr(config, "role", "UnknownRole")
    database_name = getattr(config, "database_name", "default_db")

    # --- Load JSON data ---
    with open(file1_path, "r") as file1, open(file2_path, "r") as file2:
        calc_data = json.load(file1)
        ui_data = json.load(file2)

    # --- Extract KPI/main values ---
    main_values_calc = extract_main_values(calc_data)
    main_values_ui = extract_main_values(ui_data)
    kpis_calc = extract_kpis_from_dict(calc_data)
    kpis_ui = extract_kpis_from_dict(ui_data)

    for section in ["Competitive", "Maintenance", "Repair"]:
        kpis_calc.update(extract_category_kpis(calc_data, section))
        kpis_ui.update(extract_category_kpis(ui_data, section))

    # --- Compare KPI values ---
    output_data = []

    all_kpis = set(kpis_calc.keys()).union(set(kpis_ui.keys()))
    for kpi in all_kpis:
        calc = kpis_calc.get(kpi, {"First Month": "Missing", "Second Month": "Missing", "Variance": "Missing"})
        ui = kpis_ui.get(kpi, {"First Month": "Missing", "Second Month": "Missing", "Variance": "Missing"})
        try:
            is_match = (
                parse_value(calc["First Month"]) == parse_value(ui["First Month"]) and
                parse_value(calc["Second Month"]) == parse_value(ui["Second Month"]) and
                parse_value(calc["Variance"]) == parse_value(ui["Variance"])
            )
        except:
            is_match = False
        output_data.append([
            kpi,
            ui["First Month"], ui["Second Month"], ui["Variance"],
            calc["First Month"], calc["Second Month"], calc["Variance"],
            is_match
        ])

    # --- Compare main values ---
    all_main = set(main_values_calc.keys()).union(set(main_values_ui.keys()))
    for kpi in all_main:
        ui_val = main_values_ui.get(kpi, {"Second Month": "Missing"})["Second Month"]
        calc_val = main_values_calc.get(kpi, {"Second Month": "Missing"})["Second Month"]
        try:
            is_match = parse_value(ui_val) == parse_value(calc_val)
        except:
            is_match = False
        output_data.append([kpi, ui_val, "N/A", "N/A", calc_val, "N/A", "N/A", is_match])

    # --- Save CSV ---
    output_folder, output_csv = create_folder_file_path("Individual_Reports", "comparison_results_client_report_1_month.csv", database_name)
    with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["KPI Name", "UI First Month", "UI Second Month", "UI Variance",
                         "Calc First Month", "Calc Second Month", "Calc Variance", "Match (True/False)"])
        writer.writerows(output_data)
    print(f"CSV report saved to {output_csv}")

    # --- Save Excel with highlights ---
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Comparison Results"

    ws.merge_cells(start_row=1, start_column=1, end_row=1, end_column=8)
    ws.cell(row=1, column=1, value="1 Month Client Report Card").font = Font(bold=True, size=14)
    ws.cell(row=1, column=1).alignment = Alignment(horizontal="center")

    headers = ["KPI Name", "UI First Month", "UI Second Month", "UI Variance",
               "Calc First Month", "Calc Second Month", "Calc Variance", "Match (True/False)"]
    for col_idx, header in enumerate(headers, start=1):
        ws.cell(row=2, column=col_idx, value=header).font = Font(bold=True)
        ws.cell(row=2, column=col_idx).alignment = Alignment(horizontal="center")

    for row_idx, row in enumerate(output_data, start=3):
        for col_idx, value in enumerate(row, start=1):
            ws.cell(row=row_idx, column=col_idx, value=value)

    yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    for row in ws.iter_rows(min_row=3, max_row=ws.max_row, min_col=8, max_col=8):
        for cell in row:
            if cell.value is False:
                for c in ws[cell.row]:
                    c.fill = yellow_fill

    folder, xlsx_file = create_folder_file_path("Individual_Reports", "comparison_client_report_1_month_highlighted.xlsx", database_name)
    wb.save(xlsx_file)

    # --- Save JSON report ---
    folder, json_path = create_folder_file_path("Individual_Reports", "client_report_card_one_month.json", database_name)
    with open(json_path, "w") as jf:
        json.dump({
            "tenant": Tenant,
            "store": store,
            "role": role,
            "generatedAt": datetime.now().isoformat(),
            "results": output_data
        }, jf, indent=2)
    print(f"JSON report saved to {json_path}")

    # --- Save HTML report ---
    folder, html_path = create_folder_file_path("Individual_Reports", "client_report_card_one_month.html", database_name)
    json_report_data = []
    for row in output_data:
        json_report_data.append({
            "kpi": row[0],
            "match": row[-1],
            "ui": {"First Month": row[1], "Second Month": row[2], "Variance": row[3]},
            "calculated": {"First Month": row[4], "Second Month": row[5], "Variance": row[6]}
        })
    generate_playwright_style_html(html_path, json_report_data)
    print(f"HTML report saved to {html_path}")