"""
Validation script for KPI Scorecard.
Automates UI, extracts data, compares with DB, and generates validation reports.
"""

import math
import json
import traceback
import numpy as np
from datetime import datetime
import pandas as pd
import os
import logging
import time
from typing import Set, Dict, Any
from decimal import Decimal, ROUND_HALF_UP
import asyncio
from traceback import print_exc

from dotenv import load_dotenv
from playwright.sync_api import sync_playwright
from lib.pattern.sampackag.compare_kpi_dashboard import compare_dashboard_kpis
from lib.std.universal.extract_image_data import extract_image_data
from lib.pattern.config import config
from lib.std.universal.utils import create_folder_file_path
from lib.std.universal.logger import logger, log_info, log_warn, log_error
from lib.std.universal.authmanager import AuthManager

load_dotenv()
# logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")



CUSTOM_SYSTEM_PROMPT = os.getenv(
    "CUSTOM_SYSTEM_PROMPT",
    "Analyze this document and provide the contents in  json format. The labels should be correctly mapped as in the image "
    "Sample Output format is given below"
    """{ 
    "KPI Scorecard":
    {
        "A) Financial - Customer Pay": null,
        "Labor Sales": value,
        "Labor Gross Profit": "value/ %value",
        "Labor Sales Per RO":value,
        "Labor GP Per RO": value,
        "Parts Sales": value,
        "Parts Gross Profit": "value /%value",        
        "Parts Sales Per RO": value,
        "Parts GP Per RO": value,
        "Labor + Parts Sales": value,
        "Labor + Parts Gross Profit": value,
        "Parts to Labor Ratio": value,
        "B) Pricing - Customer Pay": null,
        "Repair Price Targets / Misses / Non-Compliance %": "value/value",
        "Parts Price Targets / Misses / Non-Compliance %":  "value/value",
        "Competitive Hours / Sales / ELR": "value/ value / value",
        "Maintenance Hours / Sales / ELR": "value / value / value",
        "Repair Hours / Sales / ELR": "value / value / value",
        "Total Hours / Total Sales / Total ELR": "value / value / value",
        "What-If % Repair ELR if Non-Compliance at 0%": value,
        "What-If % Total ELR if Repair Non-Compliance at 0%": value,
        "Maintenance / Repair Work Mix": "value/ value",
        "C) Volume": null,
        "Customer Pay ROs": value,
        "Warranty ROs": value,
        "Internal ROs": value,
        "All Unique ROs": value,
        "Average ROs Per Day": value,
        "Representing What % of Total": value,
        "Average Days ROs are Open": value,
        "Average Vehicle Age": value,
        "Average Miles Per Vehicle": value,
        "All Sold Hours": value,
        "Average Hours Sold Per Day": value,
        "Customer Pay Hours Average Per RO": value,   
        "D) Opportunities - CP Vehicles Under 60K Miles": null,
        "Total Count / % of Business": "value/value%",        
        "1 Line Count / % Under 60K": "value/value%",        
        "Labor Sold Per 1 Line RO": value,
        "Parts Sold Per 1 Line RO": value,
        "Total Sold Per 1 Line RO": value,
        "Labor Sold Per Multi-Line RO": value,
        "Parts Sold Per Multi-Line RO": value,
        "Total Sold Per Multi-Line RO": value,
        "Average Jobs Per Multi-Line RO": value,           
        "E) Opportunities - MPI (CP and Wty)": null,
        "Opportunities Completed %": "value/ value/ value%",        
        "Upsell Potential $": value,
        "Sold $ / % Collected": "value/value%",
        "Potential Hours/Sold Hours/%": "value/value/value%",
        "Hours Sold Per Completed": value,
        "F) Opportunities - Menu Sales (CP and Wty)": null,
        "Opportunities Sold %": "value/ value/ value%",        
        "Upsell Potential $-Menu": value,
        "Sold $ / % Collected-Menu": "value/value%",
        "Potential Hours/Sold Hours/%-Menu": "value/value/value%",        
        "Hours Sold Per Menu ": value,
        "G) Opportunities - CP Vehicles Over 60K Miles": null,
        "Total Count / % of Business": "value/value%",
        "1 Line Count / % Over 60K": "value/value%",
        "Labor Sold Per 1-Line RO-Above": value,
        "Parts Sold Per 1-Line RO-Above": value,
        "Total Sold Per 1-Line RO-Above": value,
        "Labor Sold Per Multi-Line RO-Above": value,
        "Parts Sold Per Multi-Line RO-Above": value,
        "Total Sold Per Multi-Line RO-Above": value,
        "Average Jobs Per Multi-Line RO-Above": value
    }
}""",
)


def round_off(n, decimals=0):
    """Round a number to a given number of decimal places using ROUND_HALF_UP."""
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)

def zero_sales_check(df, columns):
    """Check if the sum of specified columns in the DataFrame is zero."""
    total_sum = df[columns].sum().sum()
    return total_sum == 0

def automate_site(config):
    """
    Automate the UI to capture KPI dashboard screenshot.
    Runs inside an async runner but exposes a sync function.
    """
    async def runner():
        auth = AuthManager(config)
        success = await auth.start()
        if not success:
            log_error("❌ Authentication failed. Exiting KPI capture.")
            return

        page = auth.page
        try:
            base_url = config.site_url.rstrip("/")
            path = "home"
            site_url = f"{base_url}/{path}"

            # Navigate to home
            await page.goto(site_url)            

            # Validate dates
            start_date_str = config.start_date
            end_date_str = config.end_date
            if not start_date_str or not end_date_str:
                raise ValueError("START_DATE and END_DATE must be set in the .env file")

            start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

            # --- Custom range picker ---
            # Open custom range picker
            await page.wait_for_selector("input#picker")
            await page.click("input#picker", force=True)
            await page.wait_for_selector("li[data-range-key='Custom Range']")
            await page.click("li[data-range-key='Custom Range']")
            await page.wait_for_selector("div.daterangepicker")

            async def navigate_to_month(calendar_selector, target_date):
                target_month_year = target_date.strftime("%b %Y")
                log_info(f"Navigating to {target_month_year} in {calendar_selector}")
                while True:
                    # CORRECTED: Await the inner_text() call before stripping
                    visible_month_year = (await page.locator(f"{calendar_selector} .month").inner_text()).strip()
                    if visible_month_year == target_month_year:
                        log_info(f"Reached target month: {visible_month_year}")
                        break
                    visible_date = datetime.strptime(visible_month_year, "%b %Y")
                    if visible_date < target_date:
                        await page.click(f"{calendar_selector} .next")
                    else:
                        await page.click(f"{calendar_selector} .prev")
                    await page.wait_for_timeout(500)

            async def select_day(calendar_selector, target_day):
                selector = f"{calendar_selector} td.available:not(.off)"
                await page.wait_for_selector(selector)
                date_cell = page.locator(selector, has_text=str(target_day)).nth(0)
                # CORRECTED: Added await for the click() method
                await date_cell.click(force=True)
                log_info(f"Clicked day {target_day} in {calendar_selector}")
                await page.wait_for_timeout(1000)

            # Start Date
            await navigate_to_month(".drp-calendar.left", start_date)
            await select_day(".drp-calendar.left", start_date.day)

            # End Date
            end_calendar = ".drp-calendar.right" if start_date.strftime("%b %Y") != end_date.strftime("%b %Y") else ".drp-calendar.left"
            await navigate_to_month(end_calendar, end_date)
            await select_day(end_calendar, end_date.day)

            # Apply the date range
            await page.click("button:has-text('Apply')")
            

            # ✅ Wait for KPI Scorecard table rows to load instead of just header
            try:
                # --- Wait for KPI container to be visible ---
                kpi_container = page.locator("#kpiScoreCards")
                await kpi_container.wait_for(state="visible", timeout=30000)

                # --- Wait until KPI values render (non-empty content) ---
                await page.wait_for_function(
                    """() => {
                        const els = document.querySelectorAll('#kpiScoreCards .content-value');
                        return Array.from(els).some(el => el.innerText.trim().length > 0);
                    }""",
                    timeout=5000  # 5 seconds should be enough if 3s is typical
                )

                # Optional tiny delay for animations to finish
                await page.wait_for_timeout(500)
                
            except Exception as e:
                log_error(f"⚠ KPI Scorecard not fully loaded: {e}")
                await page.wait_for_timeout(5000)


            # try:
            #     # await page.wait_for_selector("table.kpi-scorecard tbody tr", timeout=60000)
            #     await page.wait_for_selector(".MuiGrid-root.MuiGrid-item", timeout=60000)
            #     log_info("KPI Scorecard rows loaded successfully.")
            # except Exception as e:
            #     log_error(f"⚠ KPI Scorecard not fully loaded: {e}")
            #     await page.wait_for_timeout(5000)

            # Screenshot
            folder, filename = create_folder_file_path(
                base_folder_name="Omni_Results",
                output_file="kpi_dashboard.jpg",
                tenant_name=config.database_name
            )
            await page.screenshot(path=filename, full_page=True)
            log_info(f"✔ Screenshot saved to: {filename}")
            return True  # Return True on success
        except Exception as e:
            log_error(f"❌ Failed to capture KPI Dashboard: {e}")
            return False  # Return False on failure
        finally:
            await auth.stop()

    # Run the async part inside sync function
    loop = asyncio.get_event_loop()
    if loop.is_running():
        return asyncio.ensure_future(runner())
    else:
        return loop.run_until_complete(runner())

def run_validation():
    """
    Main function
    """
    start_time = time.time()
    log_info(f"Start Time: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
    # Initialize components and capture UI
    ui_capture_success = automate_site(config)

    # Check if UI capture failed before proceeding
    if not ui_capture_success:
        log_warn("UI extraction failed. Skipping database validation and comparison.")
        return

    columns_to_check = ["lbrsale", "lbrsoldhours", "prtextendedsale", "prtextendedcost"]
    # Get the required environment variables
    working_days = config.working_days
    storeid = config.store_id
    realm = config.database_name
    s_date_env = config.start_date
    e_date_env = config.end_date
    advisor_set = config.advisor
    tech_set = config.technician
    result_folder = os.getenv("RESULT_FOLDER")

    # get raw data from database
    all_revenue_details = config.all_revenue_details


    if "," in advisor_set:
        advisor = set(x.strip() for x in advisor_set.split(","))
    else:
        advisor = {advisor_set.strip()}

    if "," in tech_set:
        tech = set(x.strip() for x in tech_set.split(","))
    else:
        tech = {tech_set.strip()}

    # Coverting the start and end date with required format
    s_year, s_month, s_date = map(int, s_date_env.split("-"))
    e_year, e_month, e_date = map(int, e_date_env.split("-"))
    s_date_f = (s_year, s_month, s_date)
    e_date_f = (e_year, e_month, e_date)
    start_date = datetime(*s_date_f)
    end_date = datetime(*e_date_f)

    # Fetching data from DB
    retail_flag_all = config.retail_flag_all
    retail_flag = set(retail_flag_all['source_paytype'])


    all_revenue_details["closeddate"] = pd.to_datetime(all_revenue_details["closeddate"], errors="coerce")

    # Filter early to reduce rows
    filtered_df = all_revenue_details[
        (all_revenue_details["closeddate"] >= start_date) &
        (all_revenue_details["closeddate"] <= end_date)
    ].copy()

    # Now do the rest of the conversions on smaller dataframe
    filtered_df["store_id"] = filtered_df["store_id"].astype(str)
    storeid = str(storeid)
    filtered_df = filtered_df[filtered_df["store_id"] == storeid]

    # Convert numeric columns in-place
    numeric_cols = ["lbrsale", "lbrcost", "lbrsoldhours", "prtextendedsale", "prtextendedcost"]
    filtered_df[numeric_cols] = filtered_df[numeric_cols].apply(pd.to_numeric, errors="coerce")

    # Merge
    merged_df = filtered_df.merge(
        retail_flag_all,
        left_on=["paytypegroup", "store_id"],
        right_on=["source_paytype", "store_id"],
        how="left"
    )


    # # Define common filter condition once
    service_filter = (merged_df["department"] == "Service") & (merged_df["hide_ro"] != True)
    
    # Apply filter and compute unique_ro_number once
    filtered_df = merged_df[service_filter].copy()
    filtered_df["unique_ro_number"] = (
        filtered_df["ronumber"].astype(str) + "_" + 
        filtered_df["closeddate"].astype(str)
    )
    all_adv_tech_ro = set(filtered_df["unique_ro_number"])


    Labor_Sales = 0
    Labor_Gross_Profit = 0
    Labor_Gross_Profit_perc = 0
    Labor_Sales_Per_RO = 0
    Labor_GP_Per_RO = 0
    Parts_Sales = 0
    Parts_Gross_Profit = 0
    Parts_Gross_Profit_perc = 0
    Parts_Sales_Per_RO = 0
    Parts_GP_Per_RO = 0
    Labor_Parts_Sales = 0
    Labor_Parts_Gross_Profit = 0
    Parts_to_Labor_Ratio = 0
    Competitive_Hours = 0
    Competitive_L_Sales = 0
    Competitive_ELR = 0
    Maintenance_Hours = 0
    Maintenance_L_Sales = 0
    Maintenance_ELR = 0
    Repair_Hours = 0
    Repair_L_Sales = 0
    Repair_ELR = 0
    Total_ELR = 0
    Total_Hours = 0
    Total_Sales = 0
    Parts_to_Labor_Ratio = 0
    Maintenance_Work_Mix = 0
    Repair_Work_Mix = 0
    all_unique_ros = 0
    Scorecard_10_CP = 0
    Scorecard_10_Wty = 0
    Scorecard_10_Int = 0
    Average_ROs_Per_Day = 0
    Representing_What_percentage_of_Total = 0
    Average_Days_ROs_are_Open = 0
    Average_Vehicle_Age = 0
    Average_Miles_Per_Vehicle = 0
    All_Sold_Hours = 0
    Average_Hours_Sold_Per_Day = 0
    Customer_Pay_Hours_Average_Per_RO = 0
    if not filtered_df.empty:
        combined_revenue_details = filtered_df.copy()
        combined_revenue_details["group"] = pd.Series(dtype="string")
        # Define customer and warranty pay types dynamically
        if retail_flag == {"C"}:
            customer_pay_types = {"C"}
            warranty_pay_types = {"W", "F", "M", "E"}
        elif retail_flag == {"C", "M"}:
            customer_pay_types = {"C", "M"}
            warranty_pay_types = {"W", "F", "E"}
        elif retail_flag == {"C", "E"}:
            customer_pay_types = {"C", "E"}
            warranty_pay_types = {"W", "F", "M"}
        elif retail_flag == {"C", "E", "M"}:
            customer_pay_types = {"C", "E", "M"}
            warranty_pay_types = {"W", "F"}
        # Create a temporary version for zero check without modifying the original data
        temp_revenue_details = combined_revenue_details.copy()
        temp_revenue_details.loc[temp_revenue_details["opcategory"] == "N/A", columns_to_check] = 0

        # Pre-create boolean masks for pay types (computed once)
        customer_mask = temp_revenue_details["paytypegroup"].isin(customer_pay_types)
        warranty_mask = temp_revenue_details["paytypegroup"].isin(warranty_pay_types)

        # Group by RO number once to avoid repeated filtering
        grouped = temp_revenue_details.groupby("unique_ro_number")

        # Pre-allocate group assignments
        group_assignments = {}

        for ro_number, ro_group in grouped:
            # Use pre-computed masks on the group
            ro_customer_rows = ro_group[customer_mask[ro_group.index]]
            ro_warranty_rows = ro_group[warranty_mask[ro_group.index]]
            
            # Check conditions
            has_customer_sales = not ro_customer_rows.empty and not zero_sales_check(ro_customer_rows, columns_to_check)
            has_warranty_sales = not ro_warranty_rows.empty and not zero_sales_check(ro_warranty_rows, columns_to_check)
            
            if has_customer_sales:
                group_assignments[ro_number] = "C"
            elif has_warranty_sales:
                group_assignments[ro_number] = "W"
            else:
                group_assignments[ro_number] = "I"

        # Apply all assignments at once using map (vectorized operation)
        combined_revenue_details["group"] = combined_revenue_details["unique_ro_number"].map(group_assignments)

        # Pre-convert to string once to avoid repeated conversions
        combined_revenue_details["serviceadvisor_str"] = combined_revenue_details["serviceadvisor"].astype(str)
        combined_revenue_details["lbrtechno_str"] = combined_revenue_details["lbrtechno"].astype(str)

        # Create boolean masks for filtering conditions
        advisor_all = advisor == {"all"}
        tech_all = tech == {"all"}

        if advisor_all and tech_all:
            # No filtering needed
            matching_ro_numbers = combined_revenue_details["unique_ro_number"].unique()
        else:
            # Build filter mask incrementally
            filter_mask = pd.Series(True, index=combined_revenue_details.index)
            
            if not advisor_all:
                filter_mask &= combined_revenue_details["serviceadvisor_str"].isin(advisor)
            
            if not tech_all:
                filter_mask &= combined_revenue_details["lbrtechno_str"].isin(tech)
            
            # Apply filter once and get unique RO numbers
            matching_ro_numbers = combined_revenue_details.loc[filter_mask, "unique_ro_number"].unique()
        
        # Applying the Advisor and tech filter conditions
        combined_revenue_details = combined_revenue_details[
            combined_revenue_details["unique_ro_number"].isin(matching_ro_numbers)
        ]
        combined_revenue_details = combined_revenue_details.reset_index(drop=True)
        combined_revenue_details.loc[combined_revenue_details["opcategory"] == "N/A", columns_to_check] = 0

        # Pre-create boolean masks for group filtering (computed once)
        group_C_mask = combined_revenue_details["group"] == "C"
        group_W_mask = combined_revenue_details["group"] == "W"
        group_I_mask = combined_revenue_details["group"] == "I"
        customer_pay_mask = combined_revenue_details["paytypegroup"].isin(customer_pay_types)

        # Identifying the RO Count using pre-computed masks
        Scorecard_10_CP = combined_revenue_details.loc[group_C_mask, "unique_ro_number"].nunique()
        Scorecard_10_Wty = combined_revenue_details.loc[group_W_mask, "unique_ro_number"].nunique()
        Scorecard_10_Int = combined_revenue_details.loc[group_I_mask, "unique_ro_number"].nunique()

        all_unique_ros = Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int
        combined_revenue_detail = combined_revenue_details.copy()

        # Calculating Average ROs per day
        Average_ROs_Per_Day = int(round_off(all_unique_ros / working_days))

        # Pre-compute zero-value mask for efficient filtering
        zero_values_mask = (
            (combined_revenue_details["lbrsale"].fillna(0) == 0) &
            (combined_revenue_details["lbrsoldhours"].fillna(0) == 0) &
            (combined_revenue_details["prtextendedsale"].fillna(0) == 0) &
            (combined_revenue_details["prtextendedcost"].fillna(0) == 0)
        )

        # Filtering only CP job details using pre-computed masks
        cp_and_customer_mask = customer_pay_mask & group_C_mask
        list_of_paytypegroup_C = combined_revenue_details[cp_and_customer_mask]

        # Converting to DataFrame and applying zero-value filter
        total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)
        total_CP_revenue_details_df = total_CP_revenue_details_df[~zero_values_mask[cp_and_customer_mask]]

        # Handle tech filtering efficiently
        combined_revenue_details_for_all_sold_hour = combined_revenue_details
        if not tech_all:
            # Use pre-computed string column for tech filtering
            tech_mask = combined_revenue_details["lbrtechno_str"].isin(tech)
            total_CP_revenue_details_df = total_CP_revenue_details_df[
                total_CP_revenue_details_df["lbrtechno_str"].isin(tech)
            ]
            combined_revenue_details_for_all_sold_hour = combined_revenue_details[tech_mask]

        # Calculating the KPI Scorecard A values
        lbr_sale_total = 0
        lbr_cost_total = 0
        prt_ext_sale_total = 0
        prt_ext_cost_total = 0
        lbr_sold_hours_sum_C = 0
        unique_ros_C = 0
        if not total_CP_revenue_details_df.empty:
            # Convert all columns to numeric once and store in a dictionary for reuse
            numeric_columns = {}
            columns_to_convert = ["lbrsale", "lbrcost", "lbrsoldhours", "prtextendedsale", "prtextendedcost"]
            
            for col in columns_to_convert:
                numeric_columns[col] = pd.to_numeric(total_CP_revenue_details_df[col], errors="coerce").fillna(0)
            
            # Calculate all sums at once using the pre-converted numeric columns
            lbr_sale_total = numeric_columns["lbrsale"].sum()
            lbr_cost_total = numeric_columns["lbrcost"].sum()
            lbr_sold_hours_sum_C = numeric_columns["lbrsoldhours"].sum()
            prt_ext_sale_total = numeric_columns["prtextendedsale"].sum()
            prt_ext_cost_total = numeric_columns["prtextendedcost"].sum()
            unique_ros_C = Scorecard_10_CP
        else:
            print(" No data available for KPI Scorecard A calculation")
        Labor_GP = lbr_sale_total - lbr_cost_total
        Parts_GP = prt_ext_sale_total - prt_ext_cost_total

        # Pre-check division conditions to avoid repeated comparisons
        lbr_sale_nonzero = lbr_sale_total != 0
        prt_sale_nonzero = prt_ext_sale_total != 0
        unique_ros_nonzero = unique_ros_C != 0

        # Calculate percentage values
        Labor_GP_perc = (Labor_GP / lbr_sale_total) * 100 if lbr_sale_nonzero else 0
        Parts_GP_perc = (Parts_GP / prt_ext_sale_total) * 100 if prt_sale_nonzero else 0

        # Calculate per-RO metrics in one block
        if unique_ros_nonzero:
            Labor_Sales_Per_RO = lbr_sale_total / unique_ros_C
            Labor_GP_Per_RO_C = Labor_GP / unique_ros_C
            Parts_Sales_Per_RO = round_off(prt_ext_sale_total / unique_ros_C)
            Parts_GP_Per_RO_C = Parts_GP / unique_ros_C
        else:
            Labor_Sales_Per_RO = 0
            Labor_GP_Per_RO_C = 0
            Parts_Sales_Per_RO = 0
            Parts_GP_Per_RO_C = 0

        # Calculate combined metrics
        Labor_Parts_Sales_C = lbr_sale_total + prt_ext_sale_total
        Labor_Parts_GP = Labor_GP + Parts_GP

        # Calculate ratio
        Parts_to_Labor_Ratio_C = prt_ext_sale_total / lbr_sale_total if lbr_sale_nonzero else 0
        # KPI Scorecard A Output
        values_to_round = [
            (lbr_sale_total, None),           # Labor_Sales
            (Labor_GP, None),                 # Labor_Gross_Profit  
            (Labor_GP_perc, 1),              # Labor_Gross_Profit_perc
            (Labor_GP_Per_RO_C, None),       # Labor_GP_Per_RO
            (prt_ext_sale_total, None),      # Parts_Sales
            (Parts_GP, None),                # Parts_Gross_Profit
            (Parts_GP_perc, 1),              # Parts_Gross_Profit_perc
            (Parts_GP_Per_RO_C, None),       # Parts_GP_Per_RO
            (Labor_Parts_Sales_C, None),     # Labor_Parts_Sales
            (Labor_Parts_GP, None),          # Labor_Parts_Gross_Profit
            (Parts_to_Labor_Ratio_C, 1)      # Parts_to_Labor_Ratio
        ]

        # Apply rounding in batch
        rounded_values = [round_off(value, decimals) if decimals is not None else round_off(value) 
                        for value, decimals in values_to_round]

        # Assign rounded values
        (Labor_Sales, Labor_Gross_Profit, Labor_Gross_Profit_perc, Labor_GP_Per_RO,
        Parts_Sales, Parts_Gross_Profit, Parts_Gross_Profit_perc, Parts_GP_Per_RO,
        Labor_Parts_Sales, Labor_Parts_Gross_Profit, Parts_to_Labor_Ratio) = rounded_values
       
        # # Calculating the KPI Scorecard B values
        total_CP_revenue_details_df_comp = total_CP_revenue_details_df[
            total_CP_revenue_details_df["opcategory"] == "COMPETITIVE"
        ]
        total_CP_revenue_details_df_maint = total_CP_revenue_details_df[
            total_CP_revenue_details_df["opcategory"] == "MAINTENANCE"
        ]
        total_CP_revenue_details_df_repair = total_CP_revenue_details_df[
            total_CP_revenue_details_df["opcategory"] == "REPAIR"
        ]
        Competitive_Hours = pd.to_numeric(total_CP_revenue_details_df_comp["lbrsoldhours"], errors="coerce").fillna(0).sum()
        Competitive_L_Sales = pd.to_numeric(total_CP_revenue_details_df_comp["lbrsale"], errors="coerce").fillna(0).sum()
        Competitive_ELR = 0
        if Competitive_Hours != 0:
            Competitive_ELR = round_off(Competitive_L_Sales / Competitive_Hours)
        Maintenance_Hours = (
            pd.to_numeric(total_CP_revenue_details_df_maint["lbrsoldhours"], errors="coerce").fillna(0).sum()
        )
        Maintenance_L_Sales = pd.to_numeric(total_CP_revenue_details_df_maint["lbrsale"], errors="coerce").fillna(0).sum()
        Maintenance_ELR = 0
        if Maintenance_Hours != 0:
            Maintenance_ELR = round_off(Maintenance_L_Sales / Maintenance_Hours)
        Repair_Hours = pd.to_numeric(total_CP_revenue_details_df_repair["lbrsoldhours"], errors="coerce").fillna(0).sum()
        Repair_L_Sales = pd.to_numeric(total_CP_revenue_details_df_repair["lbrsale"], errors="coerce").fillna(0).sum()
        Repair_ELR = 0
        if Repair_Hours != 0:
            Repair_ELR = round_off(Repair_L_Sales / Repair_Hours)
        Total_Hours = Competitive_Hours + Maintenance_Hours + Repair_Hours
        Total_Sales = Competitive_L_Sales + Maintenance_L_Sales + Repair_L_Sales
        Total_ELR = 0
        if Total_Hours != 0:
            Total_ELR = round_off(Total_Sales / Total_Hours)
        comp_maint_total_Hours = Competitive_Hours + Maintenance_Hours
        Maintenance_Work_Mix = round_off((comp_maint_total_Hours / Total_Hours) * 100)
        Repair_Work_Mix = round_off((Repair_Hours / Total_Hours) * 100)
        # Calculating the KPI Scorecard C values
        combined_revenue_details["min_opendate"] = combined_revenue_details.groupby("unique_ro_number")[
            "opendate"
        ].transform("min")
        combined_revenue_details["open_days"] = (
            pd.to_datetime(combined_revenue_details["closeddate"])
            - pd.to_datetime(combined_revenue_details["min_opendate"])
        ).dt.days
        latest_closed_date = pd.to_datetime(combined_revenue_details["closeddate"]).max()
        # Fill missing year values with the year of latest_closed_date
        combined_revenue_details["year_filled"] = combined_revenue_details["year"].fillna(str(latest_closed_date.year))
        # Convert the year column to datetime by appending '-01-01' for January 1st of that year
        combined_revenue_details["date_year"] = pd.to_datetime(
            combined_revenue_details["year_filled"].astype(str) + "-01-01", format="%Y-%m-%d", errors="coerce"
        )
        # Calculate the age in years based on year difference only
        combined_revenue_details["age"] = (
            (latest_closed_date - combined_revenue_details["date_year"]).apply(lambda x: x.days // 365.25).fillna(0)
        )
        filtered_df_without_duplicates = combined_revenue_details.drop_duplicates(
            subset=["unique_ro_number"], keep="first"
        ).reset_index(drop=True)
        filtered_df_without_duplicates_for_open_days = combined_revenue_details.loc[
            combined_revenue_details.groupby("unique_ro_number")["open_days"].idxmin()
        ].reset_index(drop=True)
        filtered_df_C_W = combined_revenue_details[combined_revenue_details["group"].isin(["C", "W"])]
        filtered_df_CME_WF = filtered_df_C_W[filtered_df_C_W["paytypegroup"].isin(["C", "M", "E", "W", "F"])]
        filtered_df_C_W_without_duplicates = filtered_df_CME_WF.loc[
            filtered_df_CME_WF.groupby("unique_ro_number")["age"].idxmax()
        ].reset_index(drop=True)
        filtered_df_C_W_without_duplicates_for_mileage = filtered_df_CME_WF.loc[
            filtered_df_CME_WF.groupby("unique_ro_number")["mileage"].idxmax()
        ].reset_index(drop=True)
        open_days_sum = filtered_df_without_duplicates_for_open_days["open_days"].sum()
        age_sum = filtered_df_C_W_without_duplicates.loc[filtered_df_C_W_without_duplicates["age"] >= 0, "age"].sum()
        mileage_sum = (
            pd.to_numeric(filtered_df_C_W_without_duplicates_for_mileage["mileage"], errors="coerce").fillna(0).sum()
        )
        Average_Days_ROs_are_Open = round_off((open_days_sum / all_unique_ros), 1)
        Average_Vehicle_Age = round_off((age_sum / (Scorecard_10_CP + Scorecard_10_Wty)), 1)
        Average_Miles_Per_Vehicle = round_off((mileage_sum / (Scorecard_10_CP + Scorecard_10_Wty)))
        All_Sold_Hours = (
            pd.to_numeric(combined_revenue_details_for_all_sold_hour["lbrsoldhours"], errors="coerce").fillna(0).sum()
        )
        # Average_Hours_Sold_Per_Day = round_off((All_Sold_Hours / working_days),1)
        Average_Hours_Sold_Per_Day = All_Sold_Hours / working_days
        Customer_Pay_Hours_Average_Per_RO = round_off((lbr_sold_hours_sum_C / unique_ros_C), 2)
        # Calculating % of Vehicles Serviced
        if advisor == "all" and tech == "all":
            Representing_What_percentage_of_Total = 100
        else:
            Representing_What_percentage_of_Total = round_off((all_unique_ros / len(all_adv_tech_ro)) * 100)
    # Storing the KPI Scorecard data as JSON


    result_set = [
        {
            "KPI Scorecard A - Financial - Customer Pay": None,
            "Labor Sales": Labor_Sales,
            "Labor Gross Profit": f"{int(round_off(Labor_Gross_Profit))} / {Labor_Gross_Profit_perc}% ",
            "Labor Sales Per RO": round_off(Labor_Sales_Per_RO),
            "Labor GP Per RO": Labor_GP_Per_RO,
            # "KPI Scorecard 2": None,
            "Parts Sales": Parts_Sales,
            "Parts Gross Profit": f"{int(round_off(Parts_Gross_Profit))} / {Parts_Gross_Profit_perc}%",
            #   "Parts Gross Profit Percentage": Parts_Gross_Profit_perc,
            "Parts Sales Per RO": Parts_Sales_Per_RO,
            "Parts GP Per RO": Parts_GP_Per_RO,
            # "KPI Scorecard 3": None,
            "Labor + Parts Sales": Labor_Parts_Sales,
            "Labor + Parts Gross Profit": Labor_Parts_Gross_Profit,
            "Parts to Labor Ratio": Parts_to_Labor_Ratio,
            "B) Pricing - Customer Pay": None,
            "Repair Price Targets / Misses / Non-Compliance %": None,
            "Parts Price Targets / Misses / Non-Compliance %": None,
            "Competitive Hours / Sales / ELR": f"{round_off(Competitive_Hours,1)} / {int(round(Competitive_L_Sales))} / {int(round(Competitive_ELR))}",
            "Maintenance Hours / Sales / ELR": f"{round_off(Maintenance_Hours,1)} / {int(round_off(Maintenance_L_Sales))} / {int(round_off(Maintenance_ELR,0))}",
            "Repair Hours / Sales / ELR": f"{round_off(Repair_Hours,1)} / {int(round_off(Repair_L_Sales,0))} / {int(round_off(Repair_ELR,0))}",
            "Total Hours / Total Sales / Total ELR": f"{round_off(Total_Hours,1)} / {int(round_off(Total_Sales,0))} / {int(round_off(Total_ELR,0))}",
            "What-If Repair ELR if Non-Compliance at 0%": None,
            "What-If Total ELR if Repair Non-Compliance at 0%": None,
            "Maintenance / Repair Work Mix": f"{int(round_off(Maintenance_Work_Mix,0))} / {int(round_off(Repair_Work_Mix,0))}",
            "C) Volume": None,
            "Customer Pay ROs": Scorecard_10_CP,
            "Warranty ROs": Scorecard_10_Wty,
            "Internal ROs": Scorecard_10_Int,
            "All Unique ROs": all_unique_ros,
            "Average ROs Per Day": Average_ROs_Per_Day,
            "Representing What % of Total": Representing_What_percentage_of_Total,
            "Average Days ROs are Open": Average_Days_ROs_are_Open,
            "Average Vehicle Age": Average_Vehicle_Age,
            "Average Miles Per Vehicle": Average_Miles_Per_Vehicle,
            "All Sold Hours": round_off(All_Sold_Hours, 1),
            "Average Hours Sold Per Day": round_off(Average_Hours_Sold_Per_Day, 1),
            "Customer Pay Hours Average Per RO": round_off(Customer_Pay_Hours_Average_Per_RO, 1),
        }
    ]

    # Storing menu_master table from DB to a data frame
    menu_master_df = config.menu_master_df
    # Storing menu_service_type table from DB to data frame
    menu_service_type_df = config.menu_service_type_df
    # Storing assigned_menu_models table from DB to data frame
    assigned_menu_models_df = config.assigned_menu_models_df
    # Storing assigned_menu_opcodes table from DB to data frame
    assigned_menu_opcodes_df = config.assigned_menu_opcodes_df
    Total_Count_below_60k = 0
    Total_Count_above_60k = 0
    if not combined_revenue_details.empty:
        # Coverting the data frame to dictionary for applying conditions
        all_revenue_details_list = combined_revenue_details.to_dict("records")
        # Removing the jobs with paytypegroup other than C, M, E and excluding the ros with mileage as None
        total_revenue_details_C = combined_revenue_details[
            ~(
                (
                    (combined_revenue_details["lbrsale"].fillna(0) == 0)
                    & (combined_revenue_details["lbrsoldhours"].fillna(0) == 0)
                    & (combined_revenue_details["prtextendedsale"].fillna(0) == 0)
                    & (combined_revenue_details["prtextendedcost"].fillna(0) == 0)
                )
                | combined_revenue_details["mileage"].isnull()
            )
            & (combined_revenue_details["group"] == "C")
            & (combined_revenue_details["paytypegroup"].isin(customer_pay_types))
        ]
        total_revenue_details_C_df = total_revenue_details_C.copy()
        # Identifying the available menus from menu master table
        menu_names = set(menu_master_df["menu_name"])
        # storing the list of available default menu
        default_menu_series = menu_master_df[menu_master_df["is_default"].astype(int) == 1]["menu_name"]
        # Identifying the default menu name
        if not default_menu_series.empty:
            default_menu = default_menu_series.iloc[0]
        else:
            default_menu = np.nan
        # Checking whether models are assigned to the available menu
        if not assigned_menu_models_df.empty:
            # Create a mapping from 'model' to 'menu_name'
            model_to_menu_map = assigned_menu_models_df.set_index("model")["menu_name"].to_dict()
            # Map the 'model' column in Menu_Opportunity_list_df to the 'mapped_menu'
            total_revenue_details_C_df["mapped_menu"] = (
                total_revenue_details_C_df["model"].map(model_to_menu_map).fillna(default_menu)
            )
        else:
            # if no model maaping available all models mapped to default menu
            total_revenue_details_C_df["mapped_menu"] = default_menu
        # Iitializing the menu impacted ROs as set
        ro_with_item_count_more_than_1 = set()
        # if no menus are added, all values under menu sales will be 0
        if menu_names:
            service_type_mapping = menu_service_type_df.set_index("id")["service_type"].to_dict()
        for menu_name in menu_names:
            menu_rows = menu_master_df[menu_master_df["menu_name"] == menu_name]
            for _, menu_row in menu_rows.iterrows():
                mask = (
                    (Menu_Opportunity_list_df["mileage"] >= menu_row["range_from"]) &
                    (Menu_Opportunity_list_df["mileage"] <= menu_row["range_to"])
                )
                service_type = service_type_mapping.get(menu_row["service_type_id"], None)
                if service_type:
                    Menu_Opportunity_list_df.loc[mask, menu_name] = service_type
                    Menu_Opportunity_list_df.loc[mask, menu_name + "_id"] = menu_row["service_type_id"]
        total_revenue_details_C_df["lbrsale"] = pd.to_numeric(total_revenue_details_C_df["lbrsale"], errors="coerce")
        total_revenue_details_C_df["lbrsoldhours"] = pd.to_numeric(
            total_revenue_details_C_df["lbrsoldhours"], errors="coerce"
        )
        total_revenue_details_C_df["prtextendedsale"] = pd.to_numeric(
            total_revenue_details_C_df["prtextendedsale"], errors="coerce"
        )
        total_revenue_details_C_df["prtextendedcost"] = pd.to_numeric(
            total_revenue_details_C_df["prtextendedcost"], errors="coerce"
        )
        total_revenue_details_C_df = total_revenue_details_C_df[
            ~(
                (total_revenue_details_C_df["lbrsale"].fillna(0) == 0)
                & (total_revenue_details_C_df["lbrsoldhours"].fillna(0) == 0)
                & (total_revenue_details_C_df["prtextendedsale"].fillna(0) == 0)
                & (total_revenue_details_C_df["prtextendedcost"].fillna(0) == 0)
            )
        ]
        # Based on mileage and lbrsoldhours, splitting the customer pay ROs into two list for calculating the total RO count
        total_revenue_details_C_df_Below_60k = total_revenue_details_C_df[
            # (total_revenue_details_C_df['lbrsoldhours'].astype(float) >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (total_revenue_details_C_df["mileage"].astype(int) < 60000)
        ]
        total_revenue_details_C_df_Above_60k = total_revenue_details_C_df[
            # (total_revenue_details_C_df['lbrsoldhours'].astype(float) >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (total_revenue_details_C_df["mileage"].astype(int) >= 60000)
        ]
        total_ro_count_below_60k = total_revenue_details_C_df_Below_60k["unique_ro_number"].nunique()
        total_ro_count_above_60k = total_revenue_details_C_df_Above_60k["unique_ro_number"].nunique()
        perc_of_business_below_60k = round_off(
            (total_ro_count_below_60k / (total_ro_count_below_60k + total_ro_count_above_60k)) * 100
        )
        perc_of_business_above_60k = round_off(
            (total_ro_count_above_60k / (total_ro_count_below_60k + total_ro_count_above_60k)) * 100
        )
        # Based on the jobs on each RO, splitting the Customer Pay ROs into different list, One Line RO and Multi Line RO
        value_counts = total_revenue_details_C_df["unique_ro_number"].value_counts()
        one_line_ROs = value_counts[value_counts == 1].index
        One_Line_RO_Details = total_revenue_details_C_df[
            (total_revenue_details_C_df["unique_ro_number"].isin(one_line_ROs))
        ]
        Multi_Line_RO_Details = total_revenue_details_C_df[
            ~total_revenue_details_C_df["unique_ro_number"].isin(one_line_ROs)
        ]
        # One_Line_RO_Details = One_Line_RO_Details[One_Line_RO_Details['opcategory'] != 'N/A']
        available_menu = set(One_Line_RO_Details["mapped_menu"])
        if any(value and (not isinstance(value, float) or not math.isnan(value)) for value in available_menu):
            # Identifying the menu impacted One Line RO. If the item count more than 1 remove it from One Line list and add them into Multi Line
            for menu_map in available_menu:
                One_Line_RO_Details_with_menu = One_Line_RO_Details[One_Line_RO_Details[menu_map].notna()]
                for i, row in One_Line_RO_Details_with_menu.iterrows():
                    opcode = row["lbropcode"]
                    mapped_menu = row["mapped_menu"]
                    service_type_name = row[mapped_menu]
                    service_id = next(key for key, value in service_type_mapping.items() if value == service_type_name)
                    current_menu_opcodes = assigned_menu_opcodes_df[
                        assigned_menu_opcodes_df["menu_name"].astype(str) == mapped_menu
                    ]
                    current_menu_opcodes_set = set(current_menu_opcodes["menu_opcode"])
                    if mapped_menu in One_Line_RO_Details_with_menu.columns:
                        item_count = row[mapped_menu + "_items"]
                        if int(item_count) > 1:
                            matching_opcode_row = assigned_menu_opcodes_df[
                                (assigned_menu_opcodes_df["menu_opcode"] == opcode)
                                & (assigned_menu_opcodes_df["service_type"] == service_id)
                                & (assigned_menu_opcodes_df["menu_name"] == mapped_menu)
                            ]
                            if not matching_opcode_row.empty:
                                Multi_Line_RO_Details = pd.concat(
                                    [Multi_Line_RO_Details, pd.DataFrame([row])], ignore_index=True
                                )
                                One_Line_RO_Details = One_Line_RO_Details.drop(index=i)
        # Based on mileage and lbrsoldhours value, splitting the One Line and Multi line list into two, Below and Above 60k
        One_Line_RO_Details_below60k = One_Line_RO_Details[
            # (One_Line_RO_Details['lbrsoldhours'] >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (One_Line_RO_Details["mileage"].astype(int) < 60000)
        ]
        One_Line_RO_Details_above60k = One_Line_RO_Details[
            # (One_Line_RO_Details['lbrsoldhours'].astype(float) >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (One_Line_RO_Details["mileage"].astype(int) >= 60000)
        ]
        Multi_Line_RO_Details_below60k = Multi_Line_RO_Details[
            # (Multi_Line_RO_Details['lbrsoldhours'].astype(float) >= 0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (Multi_Line_RO_Details["mileage"].astype(int) < 60000)
        ]
        Multi_Line_RO_Details_above60k = Multi_Line_RO_Details[
            # (Multi_Line_RO_Details['lbrsoldhours'].astype(float) >=0.1) & // Commented this code as per new requirement from client (ADU meeting date 28/10/2024)
            (Multi_Line_RO_Details["mileage"].astype(int) >= 60000)
        ]
        # Multi_Line_RO_Details_above60k  = Multi_Line_RO_Details[(Multi_Line_RO_Details['mileage'].astype(int) >= 60000)]
        # Calculating the One Line RO count
        one_line_ro_count_below_60k = One_Line_RO_Details_below60k.shape[0]
        one_line_ro_count_above_60k = One_Line_RO_Details_above60k.shape[0]
        # Multi_line RO count calculation
        multi_line_ro_count_below_60k = Multi_Line_RO_Details_below60k["unique_ro_number"].nunique()
        multi_line_ro_count_above_60k = Multi_Line_RO_Details_above60k["unique_ro_number"].nunique()
        # Calculating the One Line RO percentage
        perc_of_one_line_below_60k = round_off((one_line_ro_count_below_60k / total_ro_count_below_60k) * 100)
        perc_of_one_line_above_60k = round_off((one_line_ro_count_above_60k / total_ro_count_above_60k) * 100)
        One_Line_RO_Details_below60k_for_sales = One_Line_RO_Details_below60k
        One_Line_RO_Details_above60k_for_sales = One_Line_RO_Details_above60k
        Multi_Line_RO_Details_below60k_for_sales = Multi_Line_RO_Details_below60k
        Multi_Line_RO_Details_above60k_for_sales = Multi_Line_RO_Details_above60k
        if tech != {"all"}:
            One_Line_RO_Details_below60k_for_sales = One_Line_RO_Details_below60k[
                One_Line_RO_Details_below60k["lbrtechno"].astype(str).isin(tech)
            ]
            One_Line_RO_Details_above60k_for_sales = One_Line_RO_Details_above60k[
                One_Line_RO_Details_above60k["lbrtechno"].astype(str).isin(tech)
            ]
            Multi_Line_RO_Details_below60k_for_sales = Multi_Line_RO_Details_below60k[
                Multi_Line_RO_Details_below60k["lbrtechno"].astype(str).isin(tech)
            ]
            Multi_Line_RO_Details_above60k_for_sales = Multi_Line_RO_Details_above60k[
                Multi_Line_RO_Details_above60k["lbrtechno"].astype(str).isin(tech)
            ]
        # One Line labor sale caculation
        one_line_labor_sale_below_60k = One_Line_RO_Details_below60k_for_sales["lbrsale"].sum()
        one_line_labor_sale_above_60k = One_Line_RO_Details_above60k_for_sales["lbrsale"].sum()
        # One Line parts sale caculation
        one_line_parts_sale_below_60k = One_Line_RO_Details_below60k_for_sales["prtextendedsale"].sum()
        one_line_parts_sale_above_60k = One_Line_RO_Details_above60k_for_sales["prtextendedsale"].sum()
        # One Line labor + parts sale caculation
        one_line_total_sale_below_60k = one_line_labor_sale_below_60k + one_line_parts_sale_below_60k
        one_line_total_sale_above_60k = one_line_labor_sale_above_60k + one_line_parts_sale_above_60k
        # Calculating the labor sale per One Line RO
        Labor_Sold_Per_1_Line_RO_below60k = one_line_labor_sale_below_60k / one_line_ro_count_below_60k
        Labor_Sold_Per_1_Line_RO_above60k = one_line_labor_sale_above_60k / one_line_ro_count_above_60k
        # Calculating the Parts sale per One Line RO
        Parts_Sold_Per_1_Line_RO_below60k = one_line_parts_sale_below_60k / one_line_ro_count_below_60k
        Parts_Sold_Per_1_Line_RO_above60k = one_line_parts_sale_above_60k / one_line_ro_count_above_60k
        # Calculating the Parts + labor sale per One Line RO
        Total_Sold_Per_1_Line_RO_below60k = one_line_total_sale_below_60k / one_line_ro_count_below_60k
        Total_Sold_Per_1_Line_RO_above60k = one_line_total_sale_above_60k / one_line_ro_count_above_60k
        # Calculating the labor sale sum of multiline ROs
        multi_line_labor_sale_below_60k = Multi_Line_RO_Details_below60k_for_sales["lbrsale"].sum()
        multi_line_labor_sale_above_60k = Multi_Line_RO_Details_above60k_for_sales["lbrsale"].sum()
        # Calculating the parts sale sum of multiline ROs
        multi_line_parts_sale_below_60k = Multi_Line_RO_Details_below60k_for_sales["prtextendedsale"].sum()
        multi_line_parts_sale_above_60k = Multi_Line_RO_Details_above60k_for_sales["prtextendedsale"].sum()
        # Calculating the total sale sum of multiline ROs
        multi_line_total_sale_below_60k = multi_line_labor_sale_below_60k + multi_line_parts_sale_below_60k
        multi_line_total_sale_above_60k = multi_line_labor_sale_above_60k + multi_line_parts_sale_above_60k
        # Calculating the labor sale per multiline RO
        Labor_Sold_Per_Multi_Line_RO_below60k = multi_line_labor_sale_below_60k / multi_line_ro_count_below_60k
        Labor_Sold_Per_Multi_Line_RO_above60k = multi_line_labor_sale_above_60k / multi_line_ro_count_above_60k
        # Calculating the parts sale per multiline RO
        Parts_Sold_Per_Multi_Line_RO_below60k = multi_line_parts_sale_below_60k / multi_line_ro_count_below_60k
        Parts_Sold_Per_Multi_Line_RO_above60k = multi_line_parts_sale_above_60k / multi_line_ro_count_above_60k
        # Calculating the total sale per multiline RO
        Total_Sold_Per_Multi_Line_RO_below60k = multi_line_total_sale_below_60k / multi_line_ro_count_below_60k
        Total_Sold_Per_Multi_Line_RO_above60k = multi_line_total_sale_above_60k / multi_line_ro_count_above_60k
        # Finding the job count of multi-line ROs
        total_jobs_of_multi_line_ROs_below_60k = Multi_Line_RO_Details_below60k.shape[0]
        total_jobs_of_multi_line_ROs_above_60k = Multi_Line_RO_Details_above60k.shape[0]
        # Calculating the jobs per multi-line RO
        Average_Jobs_Per_Multi_Line_RO_below60k = round_off(
            (total_jobs_of_multi_line_ROs_below_60k / multi_line_ro_count_below_60k), 1
        )
        Average_Jobs_Per_Multi_Line_RO_above60k = round_off(
            (total_jobs_of_multi_line_ROs_above_60k / multi_line_ro_count_above_60k), 1
        )

        result_set_d = [
            {
                "D) Opportunities - CP Vehicles Under 60K Miles": None,
                "Total Count / % of Business": f"{total_ro_count_below_60k} / {int(perc_of_business_below_60k)}%",
                "1 Line Count / % Under 60K": f"{one_line_ro_count_below_60k} / {int(perc_of_one_line_below_60k)}%",
                "Labor Sold Per 1 Line RO": round_off(Labor_Sold_Per_1_Line_RO_below60k),
                "Parts Sold Per 1 Line RO": round_off(Parts_Sold_Per_1_Line_RO_below60k),
                "Total Sold Per 1 Line RO": round_off(Total_Sold_Per_1_Line_RO_below60k),
                "Labor Sold Per Multi-Line RO": round_off(Labor_Sold_Per_Multi_Line_RO_below60k),
                "Parts Sold Per Multi-Line RO": round_off(Parts_Sold_Per_Multi_Line_RO_below60k),
                "Total Sold Per Multi-Line RO": round_off(Total_Sold_Per_Multi_Line_RO_below60k),
                "Average Jobs Per Multi-Line RO": Average_Jobs_Per_Multi_Line_RO_below60k,
                "G) Opportunities - CP Vehicles Over 60K Miles": None,
                "Total Count / % of Business": f"{total_ro_count_above_60k} / {int(perc_of_business_above_60k)}%",
                "1 Line Count / % Over 60K": f"{one_line_ro_count_above_60k} / {int(perc_of_one_line_above_60k)}%",
                "Labor Sold Per 1-Line RO-Above": round_off(Labor_Sold_Per_1_Line_RO_above60k),
                "Parts Sold Per 1-Line RO-Above": round_off(Parts_Sold_Per_1_Line_RO_above60k),
                "Total Sold Per 1-Line RO-Above": round_off(Total_Sold_Per_1_Line_RO_above60k),
                "Labor Sold Per Multi-Line RO-Above": round_off(Labor_Sold_Per_Multi_Line_RO_above60k),
                "Parts Sold Per Multi-Line RO-Above": round_off(Parts_Sold_Per_Multi_Line_RO_above60k),
                "Total Sold Per Multi-Line RO-Above": round_off(Total_Sold_Per_Multi_Line_RO_above60k),
                "Average Jobs Per Multi-Line RO-Above": Average_Jobs_Per_Multi_Line_RO_above60k,
            }
        ]
        result_set.extend(result_set_d)
 
    # Storing MPI setup table from DB to a data frame
    MPI_setup_df = config.mpi_setup_df
    # Storing MPI opcodes table from DB to a data frame
    mpi_opcodes = config.mpi_opcodes
    frh_value = 0
    combined_revenue_details = combined_revenue_detail.copy()

    if not MPI_setup_df.empty:
        frh_value = MPI_setup_df.loc[MPI_setup_df["is_active"] == "1", "frh"].iloc[0]
        combined_revenue_details["mileage"] = pd.to_numeric(combined_revenue_details["mileage"], errors="coerce")
        combined_revenue_details = combined_revenue_details[combined_revenue_details["mileage"].astype(int) > 1000]
    MPI_Opportunity_list_df = combined_revenue_details[
        (combined_revenue_details["department"] == "Service")
        & (combined_revenue_details["group"].isin(["C", "W"]))
        & (combined_revenue_details["paytypegroup"].isin(["C", "M", "E", "W", "F"]))
    ].copy()
    Opportunities = 0
    if not MPI_Opportunity_list_df.empty:
        # Same ronumber with different closeddate will be considered as two different ronumber
        MPI_Opportunity_list_df["unique_ronumber"] = (
            MPI_Opportunity_list_df["ronumber"].astype(str) + "_" + MPI_Opportunity_list_df["closeddate"].astype(str)
        )
        distinct_ronumbers = set(MPI_Opportunity_list_df["unique_ronumber"])
        Opportunities = len(distinct_ronumbers)
    MPI_Opportunity_C_df = combined_revenue_details[combined_revenue_details["paytypegroup"].isin(customer_pay_types)]
    opportunity_labor_sale_C = 0
    opportunity_parts_sale_C = 0
    opportunity_sold_hours_C = 0
    opportunity_ELR = 0
    Upsell_Potential = 0
    if not MPI_Opportunity_C_df.empty:
        opportunity_labor_sale_C = pd.to_numeric(MPI_Opportunity_C_df["lbrsale"]).fillna(0).sum().round(2)
        opportunity_parts_sale_C = pd.to_numeric(MPI_Opportunity_C_df["prtextendedsale"]).fillna(0).sum().round(2)
        opportunity_sold_hours_C = pd.to_numeric(MPI_Opportunity_C_df["lbrsoldhours"]).fillna(0).sum()
    Potential_Hours = Opportunities * frh_value
    opportunity_ELR = (opportunity_labor_sale_C + opportunity_parts_sale_C) / opportunity_sold_hours_C
    Upsell_Potential = float(Potential_Hours) * opportunity_ELR
    ronumbers_with_mpi = set(
        combined_revenue_details.loc[
            combined_revenue_details["lbropcode"].str.strip().isin(mpi_opcodes), "unique_ro_number"
        ]
    )
    # Filtering all jobs of ronumber, if MPI opcodes are available in any of its job
    filtered_rows = combined_revenue_details[combined_revenue_details["unique_ro_number"].isin(ronumbers_with_mpi)]
    final_filtered_rows_df = filtered_rows[
        ~(
            (filtered_rows["lbrsale"] == 0)
            & (filtered_rows["lbrsoldhours"] == 0)
            & (filtered_rows["prtextendedsale"] == 0)
            & (filtered_rows["prtextendedcost"] == 0)
        )
        & (filtered_rows["department"] != "Body Shop")
        & (filtered_rows["paytypegroup"] != "I")
        & (filtered_rows["hide_ro"] != True)
        & (filtered_rows["group"] != "I")
    ]
    # Pre-compute constants and sets
    COLUMNS_TO_DROP = [
        "open_month", "opendate", "month_year", "vin", "lbrlinecode", 
        "lbrsequenceno", "paytype", "opsubcategory", "lbropcodedesc", 
        "lbrcost", "lbractualhours", "filter_by_revenue", "prtextendedcost", 
        "filter_by_laborparts", "linaddonflag", "lbrtechhours", 
        "lbrgrossprofit", "prtsgrossprofit", "elr", "markup", 
        "prts_grossprofitpercentage", "lbr_grossprofitpercentage", 
        "department", "store_id", "lbrsequenceno_idx", "make", 
        "advisor_active", "model", "year", "lbrtech_active", 
        "clientid", "customer_no", "hide_ro", "unique_ro_number", "group"
    ]

    # Strip opcodes once for better performance
    stripped_opcodes = set(op.strip() for op in mpi_opcodes)

    # Calculate completed MPI ROs
    Completed_MPI_ROs = {}
    Completed_MPI_ROs_Perc = 0
    if not final_filtered_rows_df.empty:
        final_filtered_rows_df["unique_ro_number"] = (
            final_filtered_rows_df["ronumber"].astype(str) + "_" + final_filtered_rows_df["closeddate"].astype(str)
        )
        Completed_MPI_ROs = set(final_filtered_rows_df["unique_ro_number"])
        Completed_MPI_ROs_Perc = round((len(Completed_MPI_ROs) / Opportunities) * 100)

    # Pre-compute stripped opcodes for efficiency
    stripped_opcodes = {op.strip() for op in mpi_opcodes}

    # Creating a list that has ronumber and sequence number of MPI Jobs
    mpi_mask = filtered_rows["lbropcode"].str.strip().isin(stripped_opcodes)
    mpi_filtered = filtered_rows[mpi_mask]
    mpi_lbrsequenceno_ronumber_list = [
        {"lbrsequenceno": row["lbrsequenceno"], "unique_ro_number": row["unique_ro_number"]}
        for _, row in mpi_filtered.iterrows()
    ]

    # Pre-convert revenue details to records once
    all_revenue_details_list = combined_revenue_details.to_dict("records")

    # Pre-filter revenue data to exclude unwanted rows
    revenue_exclusion_mask = (
        combined_revenue_details["lbropcode"].str.strip().isin(stripped_opcodes) |
        (combined_revenue_details["department"] == "Body Shop") |
        combined_revenue_details["paytypegroup"].isin({"W", "F", "I"}) |
        (combined_revenue_details["hide_ro"] == True)
    )
    valid_revenue_list = [row for i, row in enumerate(all_revenue_details_list) if not revenue_exclusion_mask.iloc[i]]

    # Calculating the sold sales and sold hours (Considering only jobs that followed by an MPI job)
    # Create lookup dictionary for faster MPI sequence number lookup
    mpi_lookup_dict = {}
    for mpi_row in mpi_lbrsequenceno_ronumber_list:
        ro_num = mpi_row["unique_ro_number"]
        seq_num = int(mpi_row["lbrsequenceno"])
        if ro_num not in mpi_lookup_dict:
            mpi_lookup_dict[ro_num] = []
        mpi_lookup_dict[ro_num].append(seq_num)

    mpi_sold_list = []
    for revenue_row in valid_revenue_list:
        ro_num = revenue_row["unique_ro_number"]
        if ro_num in mpi_lookup_dict:
            revenue_seq = int(revenue_row["lbrsequenceno"])
            # Check if this revenue row comes after any MPI job
            for mpi_seq in mpi_lookup_dict[ro_num]:
                if revenue_seq > mpi_seq:
                    mpi_sold_list.append(revenue_row)
                    break

    greater_rows_df = pd.DataFrame(mpi_sold_list)
    sold_labor_sale_c = 0
    sold_parts_sale_c = 0
    sold_dollar = 0
    if not greater_rows_df.empty:
        sold_labor_sale_c = pd.to_numeric(greater_rows_df["lbrsale"]).fillna(0).sum()
        sold_parts_sale_c = pd.to_numeric(greater_rows_df["prtextendedsale"]).fillna(0).sum()
        sold_dollar = sold_labor_sale_c + sold_parts_sale_c
    MPI_collected_perc = 0
    if Upsell_Potential != 0:
        MPI_collected_perc = ((sold_dollar / Upsell_Potential) * 100).round(2)
    # Temporary code for calculating the sold hours
    MPI_sold_c_without_NA = [row for row in mpi_sold_list if not row["opcategory"] == "N/A"]
    MPI_sold_c_without_NA_df = pd.DataFrame(MPI_sold_c_without_NA)
    MPI_sold_hours_c = 0
    if not MPI_sold_c_without_NA_df.empty:
        MPI_sold_hours_c = pd.to_numeric(MPI_sold_c_without_NA_df["lbrsoldhours"]).fillna(0).sum()
    # Calculating the percentage of MPI sold hours
    perc_MPI_sold_hours_c = 0
    if Potential_Hours != 0:
        perc_MPI_sold_hours_c = round_off(((MPI_sold_hours_c / float(Potential_Hours)) * 100))
    hours_sold_per_completed = 0
    if len(Completed_MPI_ROs) != 0:
        hours_sold_per_completed = (MPI_sold_hours_c / len(Completed_MPI_ROs)).round(1)
    # For MPI Drilldown Calculation
    # MPI opportunities not having MPI opcodes
    ronumbers_with_mpi_set = set(ronumbers_with_mpi)  # Convert to set for O(1) lookup
    RO_Details_without_MPI = MPI_Opportunity_list_df[~MPI_Opportunity_list_df["ronumber"].isin(ronumbers_with_mpi_set)]
    RO_list_without_MPI = set(RO_Details_without_MPI["unique_ro_number"])
    RO_Details_with_MPI = combined_revenue_details[combined_revenue_details["ronumber"].isin(ronumbers_with_mpi_set)]
    MPI_stats_list = []
    # Process RO_list_without_MPI
    # Group data once for efficiency
    if not RO_Details_without_MPI.empty:
        grouped_without_mpi = RO_Details_without_MPI.groupby("unique_ro_number")
        
        for ro in RO_list_without_MPI:
            if ro in grouped_without_mpi.groups:
                filtered_rows = grouped_without_mpi.get_group(ro).sort_values("lbrsequenceno")
                row = filtered_rows.iloc[0].copy()  # Avoid SettingWithCopyWarning
                row["lbrsale"] = 0
                row["prtextendedsale"] = 0
                row["lbrsoldhours"] = 0
                row["MPI Done"] = "N"
                # Drop columns as in original
                row = row.drop([
                    "open_month", "opendate", "month_year", "vin", "lbrlinecode", 
                    "lbrsequenceno", "paytype", "opsubcategory", "lbropcodedesc", 
                    "lbrcost", "lbractualhours", "filter_by_revenue", "prtextendedcost", 
                    "filter_by_laborparts", "linaddonflag", "lbrtechhours", 
                    "lbrgrossprofit", "prtsgrossprofit", "elr", "markup", 
                    "prts_grossprofitpercentage", "lbr_grossprofitpercentage", 
                    "department", "store_id", "lbrsequenceno_idx", "make", 
                    "advisor_active", "model", "year", "lbrtech_active", 
                    "clientid", "customer_no", "hide_ro", "unique_ro_number", "group"
                ], errors='ignore')
                MPI_stats_list.append(row)

    COLUMNS_TO_DROP = [
        "open_month", "opendate", "month_year", "vin", "lbrlinecode", 
        "lbrsequenceno", "paytype", "opsubcategory", "lbropcodedesc", 
        "lbrcost", "lbractualhours", "filter_by_revenue", "prtextendedcost", 
        "filter_by_laborparts", "linaddonflag", "lbrtechhours", 
        "lbrgrossprofit", "prtsgrossprofit", "elr", "markup", 
        "prts_grossprofitpercentage", "lbr_grossprofitpercentage", 
        "department", "store_id", "lbrsequenceno_idx", "make", 
        "advisor_active", "model", "year", "lbrtech_active", 
        "clientid", "customer_no", "hide_ro", "unique_ro_number", "group"
    ]

    # Pre-filter data by paytype to reduce processing - keep only valid paytypes
    if not RO_Details_with_MPI.empty:
        paytype_filter = RO_Details_with_MPI["paytypegroup"].isin(["C", "M", "E"])
        grouped_with_mpi = RO_Details_with_MPI.groupby("unique_ro_number")
        
        for mpi_ros in Completed_MPI_ROs:
            if mpi_ros not in grouped_with_mpi.groups:
                continue
                
            # Get and sort filtered rows
            filtered_rows = grouped_with_mpi.get_group(mpi_ros).sort_values("lbrsequenceno")
            
            # Filter for MPI jobs with valid opcodes
            mpi_jobs_mask = filtered_rows["lbropcode"].str.strip().isin(stripped_opcodes)
            mpi_jobs_details = filtered_rows[mpi_jobs_mask]
            
            if mpi_jobs_details.empty:
                continue
                
            # Get first row and initialize
            row = mpi_jobs_details.iloc[0].copy()
            row.update({"lbrsale": 0, "prtextendedsale": 0, "lbrsoldhours": 0, "MPI Done": "Y"})
            
            lbr_sequence_no = row["lbrsequenceno"]
            
            # Filter MPI jobs with combined conditions - exactly as original logic
            sequence_condition = filtered_rows["lbrsequenceno"] > lbr_sequence_no
            paytype_condition = paytype_filter.reindex(filtered_rows.index, fill_value=False)
            opcode_condition = ~filtered_rows["lbropcode"].str.strip().isin(stripped_opcodes)
            
            mpi_jobs_filter = sequence_condition & paytype_condition & opcode_condition
            MPI_jobs = filtered_rows[mpi_jobs_filter]
            
            # Sum relevant columns in one operation
            if not MPI_jobs.empty:
                sums = MPI_jobs[["lbrsale", "prtextendedsale", "lbrsoldhours"]].sum()
                row.update(sums.to_dict())
            
            # Drop columns and append
            MPI_stats_list.append(row.drop(COLUMNS_TO_DROP, errors='ignore'))

    # Create DataFrame and drop columns once
    MPI_stats_df = pd.DataFrame(MPI_stats_list)
    if not MPI_stats_df.empty:
        MPI_stats_df = MPI_stats_df.drop(COLUMNS_TO_DROP, axis=1, errors='ignore')

    result_set_e = [
        {
            "E) Opportunities - MPI (CP and Wty)": None,
            "Opportunities Completed %": f"{Opportunities} / {len(Completed_MPI_ROs)} /  {Completed_MPI_ROs_Perc}%",
            "Upsell Potential $": round_off(Upsell_Potential),
            "Sold $ / % Collected": f"{int(round_off(sold_dollar))} / {int(round_off(MPI_collected_perc))}%",
            "Potential Hours/Sold Hours/%": f"{( round_off(Potential_Hours, 1))} / {(round_off(MPI_sold_hours_c,2))} / {int(perc_MPI_sold_hours_c)}%",
            "Hours Sold Per Completed": (hours_sold_per_completed),
        }
    ]
    result_set.extend(result_set_e)

    # menu_master_db_connect = menuMasterTableResult()
    menu_master_df = config.menu_master_df
    # Storing menu_service_type table from DB to data frame
    #menu_service_type_db_connect = menuServiceTypeTableResult()
    menu_service_type_df = config.menu_service_type_df
    # Storing assigned_menu_models table from DB to data frame
    #assigned_menu_models_db_connect = assignedMenuModelsTableResult()
    assigned_menu_models_df = config.assigned_menu_models_df
    # Storing assigned_menu_opcodes table from DB to data frame
    #assigned_menu_opcodes_db_connect = assignedMenuOpcodesTableResult()
    assigned_menu_opcodes_df = config.assigned_menu_opcodes_df
    # Initialize the menu sales values
    Opportunities = 0
    upsell_potential = 0
    potential_hours = 0
    menu_sold = 0
    sold_dollar = 0
    sold_hours = 0
    menu_jobs = 0
    menu_sold_perc = 0
    perc_collected = 0
    perc_menu_sold_hours = 0
    hours_sold_per_menu = 0
    if not combined_revenue_details.empty:
        Menu_Opportunity_list_df = combined_revenue_details[
            (combined_revenue_details["department"] == "Service")
            & (combined_revenue_details["group"].isin(["C", "W"]))
            & (combined_revenue_details["paytypegroup"].isin(["C", "M", "E", "W", "F"]))
        ].copy()

        # Identifying the available menus from menu master table
        menu_names = set(menu_master_df["menu_name"])
        # storing the list of available default menu
        default_menu_series = menu_master_df[menu_master_df["is_default"].astype(int) == 1]["menu_name"]
        # Identifying the default menu name
        if not default_menu_series.empty:
            default_menu = default_menu_series.iloc[0]
        else:
            default_menu = np.nan
        # Checking whether models are assigned to the available menu
        if not assigned_menu_models_df.empty:
            # Create a mapping from 'model' to 'menu_name'
            model_to_menu_map = assigned_menu_models_df.set_index("model")["menu_name"].to_dict()
            # Map the 'model' column in Menu_Opportunity_list_df to the 'mapped_menu'
            Menu_Opportunity_list_df["mapped_menu"] = (
                Menu_Opportunity_list_df["model"].map(model_to_menu_map).fillna(default_menu)
            )
        else:
            # if no model maaping available all models mapped to default menu
            Menu_Opportunity_list_df["mapped_menu"] = default_menu
        # if no menus are added, all values under menu sales will be 0
        if menu_names:
            for name in menu_names:
                Menu_Opportunity_list_df[name] = ""
            # Create a dictionary to map service_type_id to service_type using menu_service_type table
            service_type_mapping = menu_service_type_df.set_index("id")["service_type"].to_dict()
            # Create a set of menu names from the menu_master table
            menu_names = [name for name in menu_names if name in Menu_Opportunity_list_df.columns]
            # Iterate over each row in Menu_Opportunity_list_df
            for i, row in Menu_Opportunity_list_df.iterrows():
                mileage = row["mileage"]
                # Initialize menu_names columns to NaN
                Menu_Opportunity_list_df.loc[i, menu_names] = np.nan
                # Find rows in menu_master_df where mileage is within the range
                matching_menus = menu_master_df[
                    (menu_master_df["range_from"] <= mileage) & (menu_master_df["range_to"] >= mileage)
                ]
                for _, menu_row in matching_menus.iterrows():
                    menu_name = menu_row["menu_name"]
                    service_type_id = menu_row["service_type_id"]
                    # Check if the menu_name column exists in Menu_Opportunity_list_df
                    if menu_name in Menu_Opportunity_list_df.columns:
                        # Map service_type_id to service_type
                        service_type = service_type_mapping.get(service_type_id, None)
                        # Update the value of the column in Menu_Opportunity_list_df with service_type
                        if service_type:
                            Menu_Opportunity_list_df.at[i, menu_name] = service_type

                            Menu_Opportunity_list_df.at[i, menu_name + "_id"] = service_type_id
            # menu calculation is applicable to menu only if the models in current data set are assigned to the menu. Else, all models are assigned to the default menu
            mapped_menus = set()
            for menus in Menu_Opportunity_list_df["mapped_menu"]:
                mapped_menus.update(menus.split(","))
            individual_ro_counts = {menu: {"Basic": 0, "Intermediate": 0, "Major": 0} for menu in mapped_menus}
            for menu in mapped_menus:
                # Filter rows where 'mapped_menu' contains the current menu

                filtered_df_2 = Menu_Opportunity_list_df[Menu_Opportunity_list_df["mapped_menu"] == menu].copy()
                opprtunity_ros_set = set(filtered_df_2["unique_ro_number"])
                all_revenue_with_opprtunity_ros = combined_revenue_details[
                    combined_revenue_details["unique_ro_number"].isin(opprtunity_ros_set)
                ]
                ronumbers_with_menu_opcodes = {
                    row["unique_ro_number"]
                    for _, row in all_revenue_with_opprtunity_ros.iterrows()
                    if row.get("lbropcode", "").strip()
                    in assigned_menu_opcodes_df.loc[assigned_menu_opcodes_df["menu_name"] == menu, "menu_opcode"].values
                }
                # Filter out rows where the current menu column is null
                filtered_df_2 = filtered_df_2.dropna(subset=[menu])
                # Filtering the rows with menu opcodes that assigned to the current menu
                filtered_df_with_menu_opcodes = filtered_df_2[
                    filtered_df_2["unique_ro_number"].isin(ronumbers_with_menu_opcodes)
                ].copy()
                # Filtering the rows with menu opcodes that assigned to the current menu also filter only the rows that has paytypegroup C, E, and M
                filtered_df_with_menu_opcodes_all = combined_revenue_details[
                    combined_revenue_details["unique_ro_number"].isin(
                        set(filtered_df_with_menu_opcodes["unique_ro_number"])
                    )
                ]
                # Get the unique ro count for each category for calculating upsell pottentials and pottentials hours
                for category in ["Basic", "Intermediate", "Major"]:
                    # Filtering the list based on category and paytypegroup C, E, and M
                    category_filtered_df = filtered_df_2[
                        (filtered_df_2[menu] == category)
                        & (filtered_df_2["group"].isin({"C", "W"}))
                        & (filtered_df_2["paytypegroup"].isin({"C", "M", "E", "F", "W"}))
                    ].copy()
                    # Same RO with different closed date will be considered as two individual ROs. Joining the ronumber and closeddate to identify the distinct ro count
                    category_filtered_df["unique_ronumber_category"] = (
                        category_filtered_df["ronumber"].astype(str)
                        + "_"
                        + category_filtered_df["closeddate"].astype(str).copy()
                    )
                    unique_ro_count_for_cat = category_filtered_df["unique_ronumber_category"].nunique()
                    individual_ro_counts[menu][category] = unique_ro_count_for_cat
                # Same RO with different closed date will be considered as two individual ROs. Joining the ronumber and closeddate to identify the distinct ro count
                filtered_df_2["unique_ro_number"] = (
                    filtered_df_2["ronumber"].astype(str) + "_" + filtered_df_2["closeddate"].astype(str).copy()
                )
                # Identifying the ro count of data with menu opcodes
                filtered_df_with_menu_opcodes["unique_ro_number"] = (
                    filtered_df_with_menu_opcodes["ronumber"].astype(str)
                    + "_"
                    + filtered_df_with_menu_opcodes["closeddate"].astype(str).copy()
                )

                # Labor sale, Parts sale and sold hour calculation
                labor_sale_with_menu_opcodes = pd.to_numeric(filtered_df_with_menu_opcodes_all["lbrsale"]).fillna(0).sum()
                parts_sale_with_menu_opcodes = (
                    pd.to_numeric(filtered_df_with_menu_opcodes_all["prtextendedsale"]).fillna(0).sum()
                )
                labor_parts_sale = labor_sale_with_menu_opcodes + parts_sale_with_menu_opcodes
                sold_hours_with_menu_opcodes = (
                    pd.to_numeric(filtered_df_with_menu_opcodes_all["lbrsoldhours"]).fillna(0).sum()
                )
                sold_dollar += labor_parts_sale
                sold_hours += sold_hours_with_menu_opcodes
                # Get the unique ro count
                unique_ro_count = filtered_df_2["unique_ro_number"].nunique()
                rocount_with_menu_opcodes = filtered_df_with_menu_opcodes["unique_ro_number"].nunique()
                jobcount_with_menu_opcodes = filtered_df_with_menu_opcodes_all.shape[0]
                # Add the unique ro count to the total
                Opportunities += unique_ro_count
                menu_sold += rocount_with_menu_opcodes
                menu_jobs += jobcount_with_menu_opcodes
            if Opportunities != 0:
                menu_sold_perc = round((menu_sold / Opportunities) * 100)
            else:
                menu_sold_perc = 0
            # identifying the service type id and store it on the dictionary
            for menu_name, categories in individual_ro_counts.items():
                for category in categories:
                    if Menu_Opportunity_list_df[menu_name].isin([category]).any():
                        row_index = Menu_Opportunity_list_df[Menu_Opportunity_list_df[menu_name] == category].index[0]
                        individual_ro_counts[menu_name][category] = {
                            "ro_count": categories[category],
                            "service_type_id": Menu_Opportunity_list_df.loc[row_index, menu_name + "_id"],
                        }
            # Iterate through the dictionary to calculate the values
            for menu_name, categories in individual_ro_counts.items():
                for category, details in categories.items():
                    # Ensure it's a dictionary with ro_count and service_type_id
                    if isinstance(details, dict):
                        ro_count = details["ro_count"]
                        service_type_id = details["service_type_id"]
                        # Find the corresponding price and frh in menu_master_df
                        price_row = menu_master_df[
                            (menu_master_df["service_type_id"] == service_type_id)
                            & (menu_master_df["menu_name"] == menu_name)
                        ]
                        if not price_row.empty:
                            price = price_row["price"].iloc[0]
                            frh = price_row["frh"].iloc[0]
                            # Calculate Upsell Potential and Potential Hours
                            upsell_potential += price * ro_count
                            potential_hours += frh * ro_count
        # Handling Divisible by 0 error
        perc_collected = 0
        if upsell_potential != 0:
            perc_collected = round((float(sold_dollar) / float(upsell_potential)) * 100)
        # Handling Divisible by 0 error
        perc_menu_sold_hours = 0
        if potential_hours != 0:
            # Changed the below calculation based on the communication from David Stonham
            # perc_menu_sold_hours = round(((float(potential_hours) - float(sold_hours)) / float(potential_hours)) * 100)
            perc_menu_sold_hours = round((float(sold_hours) / float(potential_hours)) * 100)
        # Handling Divisible by 0 error
        hours_sold_per_menu = 0
        if menu_sold != 0:
            hours_sold_per_menu = round((float(sold_hours) / float(menu_sold)), 1)
    result_set_f = [
        {
            "F) Opportunities - Menu Sales (CP and Wty)": None,
            "Opportunities Sold %": f"{Opportunities} / {menu_sold} / {menu_sold_perc}%",
            "Upsell Potential $-Menu": round_off(upsell_potential),
            "Sold $ / % Collected-Menu": f"{int(round_off(sold_dollar))} / {int(round_off(perc_collected))}%",
            "Potential Hours/Sold Hours/%-Menu": f"{(round_off(potential_hours, 1))} / {(round_off(sold_hours, 1))} / {int(perc_menu_sold_hours)}%",
            "Hours Sold Per Menu ": hours_sold_per_menu,
        }
    ]
    result_set.extend(result_set_f)
    result_folder = create_folder_file_path(base_folder_name="Omni_Results",
                                tenant_name=config.database_name)
    json_output_path = os.path.join(result_folder, "kpi_dashboard.json")
    output_md_file_path = os.path.join(result_folder, "kpi_dashboard.md")
    image_path = os.path.join(result_folder, "kpi_dashboard.jpg")
    with open(json_output_path, "w") as json_file:
        json.dump(result_set, json_file, indent=4)

    # extract data from UI screenshot
    extract_image_data(CUSTOM_SYSTEM_PROMPT, image_path)

    # comparison -data from the UI screenshot and the python function results (on raw data)
    compare_dashboard_kpis(output_md_file_path, json_output_path)

    log_info("Completed validation of the KPI scorecard data")




def main():    
    run_validation()

if __name__ == "__main__":
    main()