import math
import json
import sys
import numpy as np
from datetime import datetime, date, timedelta
from dateutil.relativedelta import relativedelta
from collections import Counter
import pandas as pd
import os
from difflib import get_close_matches
import re
import openpyxl
from db_handler.db_connector import DbConnector, opcodePayTypeFixedRateStatus,allRevenueDetailsCPOverview, payTypeFixedRates, opcodeFixedRates, gridData, opcodeTable, payTypeMasterTableResult, MPISetupTableResult,MPIOpcodesTableResult,menuMasterTableResult, menuServiceTypeTableResult, assignedMenuModelsTableResult, assignedMenuOpcodesTableResult
# from db_handler.db_connector import DbConnector, opcodePayTypeFixedRateStatus,allRevenueDetailsTable, payTypeFixedRates, opcodeFixedRates, gridData, opcodeTable, payTypeMasterTableResult, MPISetupTableResult,MPIOpcodesTableResult,menuMasterTableR<PERSON>ult, menuServiceTypeTable<PERSON><PERSON>ult, assignedMenuModelsTableResult, assignedMenuOpcodesTableResult
from dotenv import load_dotenv
load_dotenv()
from decimal import Decimal, ROUND_HALF_UP
from lib.std.universal.logger import logger, log_info, log_warn, log_error
namespace = {
    'ns': 'http://www.dmotorworks.com/service-repair-order-history'
}

def round_off(n, decimals=0):
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal("1"), rounding=ROUND_HALF_UP) / multiplier)


#Function used for checking zero sales
def zero_sales_check(df, columns):
    total_sum = df[columns].sum().sum()
    return total_sum == 0

columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
# retail_flag = {'C', 'M','E'}
retail_flag = {'C'}
#Get the required environment variables
working_days = float(os.environ.get('working_days'))
storeid = os.environ.get('store_id')
realm = os.environ.get('realm') 
s_date_env = os.environ.get('start_date')
e_date_env = os.environ.get('end_date')
advisor_set = os.environ.get('advisor')
tech_set = os.environ.get('technician')
if isinstance(advisor_set, str):
    if ',' in advisor_set:
        advisor = {x.strip() for x in advisor_set.split(',')}
    else:
        advisor = {advisor_set.strip()}
else:
    advisor = set()  # Handle cases where advisor_set is not a string
# Ensure advisor is not empty before extracting an element
if advisor:
    advisor_id = next(iter(advisor))   
else:
    log_info("No valid advisor ID found.")

if ',' in tech_set:
    tech = set(x.strip() for x in tech_set.split(','))
else:
    tech = {tech_set.strip()}
# Coverting the start and end date with required format
s_year, s_month, s_date = map(int, s_date_env.split('-'))
e_year, e_month, e_date = map(int, e_date_env.split('-'))
s_date_f = (s_year, s_month, s_date)
e_date_f = (e_year, e_month, e_date)
start_date = datetime(*s_date_f)
end_date = datetime(*e_date_f)
all_revenue_details_table_db_connect = allRevenueDetailsCPOverview()
all_revenue_details = all_revenue_details_table_db_connect.getTableResult()
# all_revenue_details = pd.read_csv('raw-data/all_revenue_details.csv', na_values=[], keep_default_na=False)
paytype_retail_flag_setting = pd.read_csv('raw-data/paytype_retail_flag_setting.csv', na_values=[], keep_default_na=False)
all_revenue_details['closeddate'] = pd.to_datetime(all_revenue_details['closeddate'], errors= 'coerce')
all_revenue_details['store_id'] = all_revenue_details['store_id'].astype(str)
paytype_retail_flag_setting['store_id'] = paytype_retail_flag_setting['store_id'].astype(str)
storeid = str(storeid)  # Ensure storeid is also a string
def process_month_data(all_revenue_details, start_date_month, end_date_month, storeid, advisor, tech, retail_flag, customer_pay_types, warranty_pay_types, working_days, columns_to_check):
    """Process data for a specific month and return results"""
        
    all_revenue_details_df = all_revenue_details[
        (all_revenue_details['closeddate'] >= start_date) &
        (all_revenue_details['closeddate'] <= end_date) &
        (all_revenue_details['store_id'] == storeid)
    ].copy()  # Create a copy to avoid SettingWithCopyWarning

    # Debug: log_info available columns
    log_info(f"Available columns in all_revenue_details_df: {list(all_revenue_details_df.columns)}")

    # Convert numeric columns to avoid type errors
    all_revenue_details_df.loc[:, 'lbrsale'] = pd.to_numeric(all_revenue_details_df['lbrsale'], errors='coerce')
    all_revenue_details_df.loc[:, 'lbrcost'] = pd.to_numeric(all_revenue_details_df['lbrcost'], errors='coerce')
    all_revenue_details_df.loc[:, 'lbrsoldhours'] = pd.to_numeric(all_revenue_details_df['lbrsoldhours'], errors='coerce')
    all_revenue_details_df.loc[:, 'prtextendedsale'] = pd.to_numeric(all_revenue_details_df['prtextendedsale'], errors='coerce')
    all_revenue_details_df.loc[:, 'prtextendedcost'] = pd.to_numeric(all_revenue_details_df['prtextendedcost'], errors='coerce')

    # Convert mileage and age to numeric to avoid idxmax() errors - only if columns exist
    if 'mileage' in all_revenue_details_df.columns:
        all_revenue_details_df.loc[:, 'mileage'] = pd.to_numeric(all_revenue_details_df['mileage'], errors='coerce')
    else:
        log_warn("Warning: 'mileage' column not found in data")

    if 'age' in all_revenue_details_df.columns:
        all_revenue_details_df.loc[:, 'age'] = pd.to_numeric(all_revenue_details_df['age'], errors='coerce')
    else:
        log_warn("Warning: 'age' column not found in data")
    merged_df = all_revenue_details_df.merge(
        paytype_retail_flag_setting, 
        left_on=['paytypegroup', 'store_id'], 
        right_on=['source_paytype', 'store_id'], 
        how='left'
    )
    # Filtering service department and hide_ro is not True
    all_adv_tech_df = merged_df[
        (merged_df['department'] == 'Service')  
        & (merged_df['hide_ro'] != True)
    ].copy()  # Creating a copy to avoid SettingWithCopyWarning

    # Finding Total RO count for calculating the % of Vehicles Serviced value on KPI Scorecard C
    all_adv_tech_df = all_adv_tech_df.copy()  # Create a deep copy of filtered_df to avoid the warning
    all_adv_tech_df['unique_ro_number'] = all_adv_tech_df['ronumber'].astype(str) + '_' + all_adv_tech_df['closeddate'].astype(str)
    all_adv_tech_ro = set(all_adv_tech_df['unique_ro_number'])
    # Initializing new data frame to filter only required advisor and technician
    filtered_df = merged_df
    if advisor=={'all'}:
        filtered_df = filtered_df[
            (filtered_df['department'] == 'Service') & 
            (filtered_df['hide_ro'] != True) 
            # & (filtered_df['opcategory'] != 'N/A')     
            ]
    else :
        filtered_df = filtered_df[
            (filtered_df['department'] == 'Service') & 
            (filtered_df['hide_ro'] != True) 
            & (filtered_df['serviceadvisor']==7368)
            # & (filtered_df['opcategory'] != 'N/A')       
                ]    
    filtered_df = filtered_df.copy()  # Create a deep copy of filtered_df to avoid the warning
    # RO number with different closeddate will be considered as different RO, joining RO number and closeddate to find out the unique RO number
    filtered_df["unique_ro_number"] = filtered_df["ronumber"].astype(str) + "_" + filtered_df["closeddate"].astype(str)
    filtered_data = filtered_df.copy()
    Labor_Sales = 0
    Labor_Gross_Profit = 0
    Labor_Gross_Profit_perc = 0
    Labor_Sales_Per_RO = 0
    Labor_GP_Per_RO = 0
    Parts_Sales = 0
    Parts_Gross_Profit = 0
    Parts_Gross_Profit_perc = 0
    Parts_Sales_Per_RO = 0
    Parts_GP_Per_RO = 0
    Labor_Parts_Sales = 0
    Labor_Parts_Gross_Profit = 0
    Parts_to_Labor_Ratio = 0
    Competitive_Hours = 0
    Competitive_L_Sales = 0
    Competitive_ELR = 0
    Maintenance_Hours = 0
    Maintenance_L_Sales = 0
    Maintenance_ELR = 0
    Repair_Hours = 0
    Repair_L_Sales = 0
    Repair_ELR = 0
    Total_ELR = 0
    Total_Hours = 0
    Total_Sales = 0
    Parts_to_Labor_Ratio = 0
    Maintenance_Work_Mix = 0
    Repair_Work_Mix = 0
    all_unique_ros = 0
    Scorecard_10_CP = 0
    Scorecard_10_Wty = 0
    Scorecard_10_Int = 0
    Average_ROs_Per_Day = 0
    Representing_What_percentage_of_Total = 0
    Average_Days_ROs_are_Open = 0
    Average_Vehicle_Age = 0
    Average_Miles_Per_Vehicle = 0
    All_Sold_Hours = 0
    Average_Hours_Sold_Per_Day = 0
    Customer_Pay_Hours_Average_Per_RO = 0


    if not filtered_df.empty:
        combined_revenue_details = filtered_df.copy()
        combined_revenue_details["group"] = pd.Series(dtype="string")
        # Define customer and warranty pay types dynamically
        if retail_flag == {"C"}:
            customer_pay_types = {"C"}
            warranty_pay_types = {"W", "F", "M", "E"}
        elif retail_flag == {"C", "M"}:
            customer_pay_types = {"C", "M"}
            warranty_pay_types = {"W", "F", "E"}
        elif retail_flag == {"C", "E"}:
            customer_pay_types = {"C", "E"}
            warranty_pay_types = {"W", "F", "M"}
        elif retail_flag == {"C", "E", "M"}:
            customer_pay_types = {"C", "E", "M"}
            warranty_pay_types = {"W", "F"}
        # Create a temporary version for zero check without modifying the original data
        temp_revenue_details = combined_revenue_details.copy()
        temp_revenue_details.loc[temp_revenue_details["opcategory"] == "N/A", columns_to_check] = 0
        # Iterate through each unique RO number
        for ro_number in combined_revenue_details["unique_ro_number"].unique():
            ro_specific_rows = temp_revenue_details[temp_revenue_details["unique_ro_number"] == ro_number]
            ro_specific_rows_C = ro_specific_rows[ro_specific_rows["paytypegroup"].isin(customer_pay_types)]
            ro_specific_rows_W = ro_specific_rows[ro_specific_rows["paytypegroup"].isin(warranty_pay_types)]
            zero_sales_C = zero_sales_check(ro_specific_rows_C, columns_to_check)
            zero_sales_W = zero_sales_check(ro_specific_rows_W, columns_to_check)
            if not ro_specific_rows_C.empty and not zero_sales_C:
                combined_revenue_details.loc[combined_revenue_details["unique_ro_number"] == ro_number, "group"] = "C"
            elif not ro_specific_rows_W.empty and not zero_sales_W:
                combined_revenue_details.loc[combined_revenue_details["unique_ro_number"] == ro_number, "group"] = "W"
            else:
                combined_revenue_details.loc[combined_revenue_details["unique_ro_number"] == ro_number, "group"] = "I"
        # Apply filters based on the conditions
        if advisor == {"all"} and tech == {"all"}:
            # No filtering needed
            matching_ro_numbers = combined_revenue_details["unique_ro_number"].unique()
        elif advisor != {"all"} and tech == {"all"}:
            # Filter based on advisor only
            matching_ro_numbers = combined_revenue_details.loc[
                combined_revenue_details["serviceadvisor"].astype(str).isin(advisor), "unique_ro_number"
            ].unique()
        elif advisor == {"all"} and tech != {"all"}:
            # Filter based on tech only
            matching_ro_numbers = combined_revenue_details.loc[
                combined_revenue_details["lbrtechno"].astype(str).isin(tech), "unique_ro_number"
            ].unique()
        elif advisor != {"all"} and tech != {"all"}:
            # Filter based on both advisor and tech
            matching_ro_numbers = combined_revenue_details.loc[
                (all_revenue_details_df["serviceadvisor"].astype(str).isin(advisor))
                & (all_revenue_details_df["lbrtechno"].astype(str).isin(tech)),
                "unique_ro_number",
            ].unique()
        # Applying the Advisor and tech filter conditions
        combined_revenue_details = combined_revenue_details[
            combined_revenue_details["unique_ro_number"].isin(matching_ro_numbers)
        ]
        combined_revenue_details = combined_revenue_details.reset_index(drop=True)
        combined_revenue_details.loc[combined_revenue_details["opcategory"] == "N/A", columns_to_check] = 0
        
        # Filter CP job details
        list_of_paytypegroup_C = combined_revenue_details[combined_revenue_details['paytypegroup'].isin(customer_pay_types) & (combined_revenue_details['group'] == 'C')].to_dict('records')
        total_revenue_CP = pd.DataFrame(list_of_paytypegroup_C)
        
        if total_revenue_CP.empty:
            return None
        
        total_revenue_CP.loc[total_revenue_CP['opcategory'] == 'N/A', columns_to_check] = 0
        total_revenue_CP = total_revenue_CP[
            ~((total_revenue_CP['lbrsale'].fillna(0) == 0) &
            (total_revenue_CP['lbrsoldhours'].fillna(0) == 0) &
            (total_revenue_CP['prtextendedsale'].fillna(0) == 0) &
            (total_revenue_CP['prtextendedcost'].fillna(0) == 0))
        ]
        
        def filter_revenue(df, paytypegroup, group):
            return df[(df['paytypegroup'] == paytypegroup) & (df['group'] == group)]

        # Calculate revenue details
        total_revenue_C = filter_revenue(combined_revenue_details, 'C', 'C')

        if 'M' in customer_pay_types:
            total_revenue_M = filter_revenue(combined_revenue_details, 'M', 'C')
        else:
            total_revenue_M = filter_revenue(combined_revenue_details, 'M', 'W')

        if 'E' in customer_pay_types:
            total_revenue_E = filter_revenue(combined_revenue_details, 'E', 'C')
        else:
            total_revenue_E = filter_revenue(combined_revenue_details, 'E', 'W')

        total_revenue_W = filter_revenue(combined_revenue_details, 'W', 'W')
        total_revenue_F = filter_revenue(combined_revenue_details, 'F', 'W')
        total_revenue_I = combined_revenue_details[combined_revenue_details['group'] == 'I']

        if tech != {'all'}:
            total_revenue_CP = total_revenue_CP[total_revenue_CP['lbrtechno'].astype(str).isin(tech)]
            total_revenue_C = total_revenue_C[total_revenue_C['lbrtechno'].astype(str).isin(tech)]
            total_revenue_M = total_revenue_M[total_revenue_M['lbrtechno'].astype(str).isin(tech)]
            total_revenue_E = total_revenue_E[total_revenue_E['lbrtechno'].astype(str).isin(tech)]
            total_revenue_W = total_revenue_W[total_revenue_W['lbrtechno'].astype(str).isin(tech)]
            total_revenue_F = total_revenue_F[total_revenue_F['lbrtechno'].astype(str).isin(tech)]
            total_revenue_I = total_revenue_I[total_revenue_I['lbrtechno'].astype(str).isin(tech)]
        
        # Calculate all metrics
        labor_revenue = pd.to_numeric(total_revenue_CP['lbrsale']).fillna(0).sum()
        parts_revenue = pd.to_numeric(total_revenue_CP['prtextendedsale']).fillna(0).sum()
        combined_revenue = labor_revenue + parts_revenue
        
        labor_cost = pd.to_numeric(total_revenue_CP['lbrcost']).fillna(0).sum()
        labor_gross_profit = labor_revenue - labor_cost
        
        parts_ext_cost = pd.to_numeric(total_revenue_CP['prtextendedcost']).fillna(0).sum()
        parts_gross_profit = parts_revenue - parts_ext_cost
        
        combined_gross_profit = (labor_revenue + parts_revenue) - (labor_cost + parts_ext_cost)
        
        # Calculate percentages
        labor_gross_profit_percentage = round_off((labor_gross_profit / labor_revenue) * 100, 1) if labor_revenue != 0 else 0
        parts_gross_profit_percentage = round_off((parts_gross_profit / parts_revenue) * 100, 1) if parts_revenue != 0 else 0
        combined_gross_profit_percentage = round_off((combined_gross_profit / (labor_revenue + parts_revenue)) * 100, 1) if (labor_revenue + parts_revenue) != 0 else 0
        
        # Labor sold hours
        labor_sold_hours_value = pd.to_numeric(total_revenue_CP['lbrsoldhours']).fillna(0).sum()
        labor_sold_hours = round_off(labor_sold_hours_value, 2)
        
        # Effective labor rate
        effective_labor_rate_CP = round_off(labor_revenue / labor_sold_hours, 2) if labor_sold_hours != 0 else 0
        
        # Parts markup
        cp_parts_markup_CP = round_off(parts_revenue / parts_ext_cost, 4) if parts_ext_cost != 0 else 0
        #jobcount
        job_count = len(total_revenue_CP["unique_ro_number"])
        log_info(job_count,"job_count***********************")
        # job_count = len(combined_revenue_details.loc[
        # combined_revenue_details["group"] == "C", "unique_ro_number"
        # ])
        # Identifying the RO Count
        Scorecard_10_CP = combined_revenue_details.loc[
            combined_revenue_details["group"] == "C", "unique_ro_number"
        ].nunique()
        Scorecard_10_Wty = combined_revenue_details.loc[
            combined_revenue_details["group"] == "W", "unique_ro_number"
        ].nunique()
        Scorecard_10_Int = combined_revenue_details.loc[
            combined_revenue_details["group"] == "I", "unique_ro_number"
        ].nunique()   
        all_unique_ros = Scorecard_10_CP + Scorecard_10_Wty + Scorecard_10_Int
        combined_revenue_detail = combined_revenue_details.copy()
        # Calculating Average ROs per day
        Average_ROs_Per_Day = int(round_off(all_unique_ros / working_days))
        # Filtering only CP job details
        list_of_paytypegroup_C = combined_revenue_details[
            combined_revenue_details["paytypegroup"].isin(customer_pay_types) & (combined_revenue_details["group"] == "C")
        ]
        total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)
        total_CP_revenue_details_df = total_CP_revenue_details_df[
            ~(
                (total_CP_revenue_details_df["lbrsale"].fillna(0) == 0)
                & (total_CP_revenue_details_df["lbrsoldhours"].fillna(0) == 0)
                & (total_CP_revenue_details_df["prtextendedsale"].fillna(0) == 0)
                & (total_CP_revenue_details_df["prtextendedcost"].fillna(0) == 0)
            )
        ]
        combined_revenue_details_for_all_sold_hour = combined_revenue_details
        if tech != {"all"}:
            # Filter based on tech only
            total_CP_revenue_details_df = total_CP_revenue_details_df[
                total_CP_revenue_details_df["lbrtechno"].astype(str).isin(tech)
            ]
            combined_revenue_details_for_all_sold_hour = combined_revenue_details[
                combined_revenue_details["lbrtechno"].astype(str).isin(tech)
            ]
        # Calculating the KPI Scorecard A values
        lbr_sale_total = 0
        lbr_cost_total = 0
        prt_ext_sale_total = 0
        prt_ext_cost_total = 0
        lbr_sold_hours_sum_C = 0
        unique_ros_C = 0
        if not total_CP_revenue_details_df.empty:
            lbr_sale_total = pd.to_numeric(total_CP_revenue_details_df["lbrsale"], errors="coerce").fillna(0).sum()
            lbr_cost_total = pd.to_numeric(total_CP_revenue_details_df["lbrcost"], errors="coerce").fillna(0).sum()
            lbr_sold_hours_sum_C = (
                pd.to_numeric(total_CP_revenue_details_df["lbrsoldhours"], errors="coerce").fillna(0).sum()
            )
            prt_ext_sale_total = (
                pd.to_numeric(total_CP_revenue_details_df["prtextendedsale"], errors="coerce").fillna(0).sum()
            )
            prt_ext_cost_total = (
                pd.to_numeric(total_CP_revenue_details_df["prtextendedcost"], errors="coerce").fillna(0).sum()
            )
            unique_ros_C = Scorecard_10_CP
        else:
            log_info(" No data available for KPI Scorecard A calculation")
        Labor_GP = lbr_sale_total - lbr_cost_total
        Parts_GP = prt_ext_sale_total - prt_ext_cost_total
        Labor_GP_perc = 0
        Parts_GP_perc = 0
        if lbr_sale_total != 0:
            Labor_GP_perc = (Labor_GP / lbr_sale_total) * 100
        if prt_ext_sale_total != 0:
            Parts_GP_perc = (Parts_GP / prt_ext_sale_total) * 100
        Labor_Sales_Per_RO = 0
        Labor_GP_Per_RO_C = 0
        Parts_Sales_Per_RO = 0
        Parts_GP_Per_RO_C = 0
        if unique_ros_C != 0:
            Labor_Sales_Per_RO = lbr_sale_total / unique_ros_C
            Labor_GP_Per_RO_C = Labor_GP / unique_ros_C
            Parts_Sales_Per_RO = round_off(prt_ext_sale_total / unique_ros_C)
            Parts_GP_Per_RO_C = Parts_GP / unique_ros_C
        Labor_Parts_Sales_C = lbr_sale_total + prt_ext_sale_total
        Labor_Parts_GP = Labor_GP + Parts_GP
        Parts_to_Labor_Ratio_C = 0
        if lbr_sale_total != 0:
            Parts_to_Labor_Ratio_C = prt_ext_sale_total / lbr_sale_total
        # KPI Scorecard A Output
        Labor_Sales = round_off(lbr_sale_total)
        Labor_Gross_Profit = round_off(Labor_GP)
        Labor_Gross_Profit_perc = round_off(Labor_GP_perc, 1)
        Labor_GP_Per_RO = round_off(Labor_GP_Per_RO_C)
        Parts_Sales = round_off(prt_ext_sale_total)
        Parts_Gross_Profit = round_off(Parts_GP)
        Parts_Gross_Profit_perc = round_off(Parts_GP_perc, 1)
        Parts_GP_Per_RO = round_off(Parts_GP_Per_RO_C)
        Labor_Parts_Sales = round_off(Labor_Parts_Sales_C)
        Labor_Parts_Gross_Profit = round_off(Labor_Parts_GP)
        Parts_to_Labor_Ratio = round_off(Parts_to_Labor_Ratio_C, 1)
        
        # # Calculating the KPI Scorecard B values
        total_CP_revenue_details_df_comp = total_CP_revenue_details_df[
            total_CP_revenue_details_df["opcategory"] == "COMPETITIVE"
        ]
        total_CP_revenue_details_df_maint = total_CP_revenue_details_df[
            total_CP_revenue_details_df["opcategory"] == "MAINTENANCE"
        ]
        total_CP_revenue_details_df_repair = total_CP_revenue_details_df[
            total_CP_revenue_details_df["opcategory"] == "REPAIR"
        ]
        Competitive_Hours = pd.to_numeric(total_CP_revenue_details_df_comp["lbrsoldhours"], errors="coerce").fillna(0).sum()
        Competitive_L_Sales = pd.to_numeric(total_CP_revenue_details_df_comp["lbrsale"], errors="coerce").fillna(0).sum()
        Competitive_ELR = 0
        if Competitive_Hours != 0:
            Competitive_ELR = round_off(Competitive_L_Sales / Competitive_Hours)
        Maintenance_Hours = (
            pd.to_numeric(total_CP_revenue_details_df_maint["lbrsoldhours"], errors="coerce").fillna(0).sum()
        )
        Maintenance_L_Sales = pd.to_numeric(total_CP_revenue_details_df_maint["lbrsale"], errors="coerce").fillna(0).sum()
        #labor sale competitive + maintenance
        labor_sale_com_maint=Competitive_L_Sales+Maintenance_L_Sales
        #labor sold hrs competitive + maintenance
        labor_sold_hours_com_maint=Competitive_Hours+Maintenance_Hours
        Maintenance_ELR = 0
        if Maintenance_Hours != 0:
            Maintenance_ELR = round_off(Maintenance_L_Sales / Maintenance_Hours)
        #ELR_maintance_compaative
        ELR_maintance_compaative=round_off((labor_sale_com_maint/labor_sold_hours_com_maint),2)
        Repair_Hours = pd.to_numeric(total_CP_revenue_details_df_repair["lbrsoldhours"], errors="coerce").fillna(0).sum()
        Repair_L_Sales = pd.to_numeric(total_CP_revenue_details_df_repair["lbrsale"], errors="coerce").fillna(0).sum()
        Repair_ELR = 0
        if Repair_Hours != 0:
            Repair_ELR = round_off((Repair_L_Sales / Repair_Hours),2)
        Total_Hours = Competitive_Hours + Maintenance_Hours + Repair_Hours
        Total_Sales = Competitive_L_Sales + Maintenance_L_Sales + Repair_L_Sales    
        Total_ELR = 0
        if Total_Hours != 0:
            Total_ELR = round_off(Total_Sales / Total_Hours)
        comp_maint_total_Hours = Competitive_Hours + Maintenance_Hours
        Maintenance_Work_Mix = round_off((comp_maint_total_Hours / Total_Hours) * 100)
        Repair_Work_Mix = round_off((Repair_Hours / Total_Hours) * 100)
        # Calculating the KPI Scorecard C values
        combined_revenue_details["min_opendate"] = combined_revenue_details.groupby("unique_ro_number")[
            "opendate"
        ].transform("min")
        combined_revenue_details["open_days"] = (
            pd.to_datetime(combined_revenue_details["closeddate"])
            - pd.to_datetime(combined_revenue_details["min_opendate"])
        ).dt.days
        latest_closed_date = pd.to_datetime(combined_revenue_details["closeddate"]).max()
        # Fill missing year values with the year of latest_closed_date
        combined_revenue_details["year_filled"] = combined_revenue_details["year"].fillna(str(latest_closed_date.year))
        # Convert the year column to datetime by appending '-01-01' for January 1st of that year
        combined_revenue_details["date_year"] = pd.to_datetime(
            combined_revenue_details["year_filled"].astype(str) + "-01-01", format="%Y-%m-%d", errors="coerce"
        )
        # Calculate the age in years based on year difference only
        combined_revenue_details["age"] = (
            (latest_closed_date - combined_revenue_details["date_year"]).apply(lambda x: x.days // 365.25).fillna(0)
        )
        filtered_df_without_duplicates = combined_revenue_details.drop_duplicates(
            subset=["unique_ro_number"], keep="first"
        ).reset_index(drop=True)
        filtered_df_without_duplicates_for_open_days = combined_revenue_details.loc[
            combined_revenue_details.groupby("unique_ro_number")["open_days"].idxmin()
        ].reset_index(drop=True)
        filtered_df_C_W = combined_revenue_details[combined_revenue_details["group"].isin(["C", "W"])]
        filtered_df_CME_WF = filtered_df_C_W[filtered_df_C_W["paytypegroup"].isin(["C", "M", "E", "W", "F"])]
        # Safely handle idxmax for age column
        if 'age' in filtered_df_CME_WF.columns:
            try:
                # Fill NaN values with 0 for age before idxmax
                filtered_df_CME_WF_age = filtered_df_CME_WF.copy()
                filtered_df_CME_WF_age['age'] = filtered_df_CME_WF_age['age'].fillna(0)
                filtered_df_C_W_without_duplicates = filtered_df_CME_WF_age.loc[
                    filtered_df_CME_WF_age.groupby("unique_ro_number")["age"].idxmax()
                ].reset_index(drop=True)
            except (TypeError, ValueError, KeyError) as e:
                log_warn(f"Warning: Error with age idxmax, using first occurrence: {e}")
                filtered_df_C_W_without_duplicates = filtered_df_CME_WF.groupby("unique_ro_number").first().reset_index()
        else:
            log_warn("Warning: 'age' column not found, using first occurrence for age-based filtering")
            filtered_df_C_W_without_duplicates = filtered_df_CME_WF.groupby("unique_ro_number").first().reset_index()

        # Safely handle idxmax for mileage column
        if 'mileage' in filtered_df_CME_WF.columns:
            try:
                # Fill NaN values with 0 for mileage before idxmax
                filtered_df_CME_WF_mileage = filtered_df_CME_WF.copy()
                filtered_df_CME_WF_mileage['mileage'] = filtered_df_CME_WF_mileage['mileage'].fillna(0)
                filtered_df_C_W_without_duplicates_for_mileage = filtered_df_CME_WF_mileage.loc[
                    filtered_df_CME_WF_mileage.groupby("unique_ro_number")["mileage"].idxmax()
                ].reset_index(drop=True)
            except (TypeError, ValueError, KeyError) as e:
                log_warn(f"Warning: Error with mileage idxmax, using first occurrence: {e}")
                filtered_df_C_W_without_duplicates_for_mileage = filtered_df_CME_WF.groupby("unique_ro_number").first().reset_index()
        else:
            log_warn("Warning: 'mileage' column not found, using first occurrence for mileage-based filtering")
            filtered_df_C_W_without_duplicates_for_mileage = filtered_df_CME_WF.groupby("unique_ro_number").first().reset_index()
        open_days_sum = filtered_df_without_duplicates_for_open_days["open_days"].sum()

        # Safely calculate age sum
        if 'age' in filtered_df_C_W_without_duplicates.columns:
            age_sum = filtered_df_C_W_without_duplicates.loc[filtered_df_C_W_without_duplicates["age"] >= 0, "age"].sum()
        else:
            log_warn("Warning: 'age' column not found, setting age_sum to 0")
            age_sum = 0

        # Safely calculate mileage sum
        if 'mileage' in filtered_df_C_W_without_duplicates_for_mileage.columns:
            mileage_sum = (
                pd.to_numeric(filtered_df_C_W_without_duplicates_for_mileage["mileage"], errors="coerce").fillna(0).sum()
            )
        else:
            log_warn("Warning: 'mileage' column not found, setting mileage_sum to 0")
            mileage_sum = 0
        Average_Days_ROs_are_Open = round_off((open_days_sum / all_unique_ros), 1)
        Average_Vehicle_Age = round_off((age_sum / (Scorecard_10_CP + Scorecard_10_Wty)), 1)
        Average_Miles_Per_Vehicle = round_off((mileage_sum / (Scorecard_10_CP + Scorecard_10_Wty)))
        All_Sold_Hours = (
            pd.to_numeric(combined_revenue_details_for_all_sold_hour["lbrsoldhours"], errors="coerce").fillna(0).sum()
        )
        Average_Hours_Sold_Per_Day = All_Sold_Hours / working_days
        Customer_Pay_Hours_Average_Per_RO = round_off((lbr_sold_hours_sum_C / unique_ros_C), 2)
        # Calculating % of Vehicles Serviced
        if advisor == {"all"} and tech == {"all"}:
                Representing_What_percentage_of_Total = 100
        else:
                Representing_What_percentage_of_Total = round_off((all_unique_ros / len(all_adv_tech_ro)) * 100)
        return {
            "labor_revenue": round_off(labor_revenue, 2),
            "parts_revenue": round_off(parts_revenue, 2),
            "combined_revenue": round_off(combined_revenue, 2),
            "labor_gross_profit": round_off(labor_gross_profit, 2),
            "parts_gross_profit": round_off(parts_gross_profit, 2),
            "combined_gross_profit": round_off(combined_gross_profit, 2),
            "labor_gross_profit_percentage": labor_gross_profit_percentage,
            "parts_gross_profit_percentage": parts_gross_profit_percentage,
            "combined_gross_profit_percentage": combined_gross_profit_percentage,
            "labor_sold_hours": labor_sold_hours,
            "effective_labor_rate_cp": effective_labor_rate_CP,
            "cp_parts_markup_cp": round_off(cp_parts_markup_CP,2),
            "labor_cost": labor_cost,
            "total_parts_sales": round_off(parts_revenue, 2),  # Same as parts_revenue
            "total_parts_cost": round_off(parts_ext_cost, 2),   # Same as parts_ext_cost
            "labor_sold_hours_com_maint": labor_sold_hours_com_maint,
            "labor_sale_com_maint": round_off(labor_sale_com_maint,2),
            "ELR_maintance_compaative": round_off(ELR_maintance_compaative,2),
            "Repair_Hours": Repair_Hours,
            "Repair_L_Sales": round(Repair_L_Sales,2),
            "Repair_ELR": Repair_ELR,
            "Total_Hours": Total_Hours,
            "labor_hours_per_ro": round_off((Total_Hours/Scorecard_10_CP),2),
            "Total_Sales": round(Total_Sales,2),
            "job_count": job_count,
            "ro_count": Scorecard_10_CP,
            "Labor_Sales_Per_RO": round_off(Labor_Sales_Per_RO,2),
            "Repair_ELR": round_off(Repair_ELR,2),
            "Total_ELR": round_off(Total_ELR,2) ,
            "labor_sale_repair": round_off(Repair_L_Sales,2),
            "Labor_Sold_Hours_Repair":round_off(Repair_Hours,2),
            "Effective_Labor_Rate_Main_and_Competitive":round_off(ELR_maintance_compaative,2),
            "Labor_Sold Hours_Maint_and_Competitive":round_off(labor_sold_hours_com_maint,2),
            "Labor_Sale_Maint_and_Competitive":round_off(labor_sale_com_maint,2)
                
        }

        # result_set = [
        #     {
        #         "KPI Single Advisor - Financial - Customer Pay": None,
        #         "Labor Sales / GP $ / GP %": f"{Labor_Sales} / {int(round_off(Labor_Gross_Profit))} / {Labor_Gross_Profit_perc}% ",
        #         "Labor Sales Per RO / GP $ Per RO": f"{ round_off(Labor_Sales_Per_RO)} / {Labor_GP_Per_RO}%",
        #         "Parts Sales / GP $ / GP %" :f"{Parts_Sales} / {int(round_off(Parts_Gross_Profit))} / {Parts_Gross_Profit_perc}%",           
        #         "Parts Sales Per RO / GP $ Per RO":f"{Parts_Sales_Per_RO} / {Parts_GP_Per_RO}",
        #         "Labor & Parts Sales / Total GP $": f"{Labor_Parts_Sales} / {Labor_Parts_Gross_Profit}%",
        #         "Parts to Labor Ratio": Parts_to_Labor_Ratio,
        #         "B) Pricing - Customer Pay": None,
        #         "Repair Price Targets / Misses / % of Non-Compliance": None,
        #         "Parts Price Targets / Misses / % of Non-Compliance": None,
        #         "Competitive Hours / Sales / ELR": f"{round_off(Competitive_Hours,1)} / {int(round(Competitive_L_Sales))} / {int(round(Competitive_ELR))}",
        #         "Maintenance Hours / Sales / ELR": f"{round_off(Maintenance_Hours,1)} / {int(round_off(Maintenance_L_Sales))} / {int(round_off(Maintenance_ELR,0))}",
        #         "Repair Hours / Sales / ELR": f"{round_off(Repair_Hours,1)} / {int(round_off(Repair_L_Sales,0))} / {int(round_off(Repair_ELR,0))}",
        #         "Total Hrs / Sales / ELR": f"{round_off(Total_Hours,1)} / {Labor_Sales} / {int(round_off(Total_ELR,0))}",
        #         "What-If Repair / Total ELR Target Misses at 0%": None,        
        #         "Maintenance / Repair Work Mix": f"{int(round_off(Maintenance_Work_Mix,0))} / {int(round_off(Repair_Work_Mix,0))}",
        #         "C) Volume": None,
        #         "CP / Wty / Int / All Vehicles" : f"{Scorecard_10_CP} / {Scorecard_10_Wty} / {Scorecard_10_Int} / {all_unique_ros}",
        #         "Average Vehicles Per Day / % of Vehicles Serviced":f"{Average_ROs_Per_Day} / {Representing_What_percentage_of_Total}",
        #         "All ROs - Average Days Open": Average_Days_ROs_are_Open,
        #         "CP & Wty - Avg Age / Miles Per Vehicle" :  f"{Average_Vehicle_Age} / {Average_Miles_Per_Vehicle} ",
        #         "Total Sold Hrs / Avg Hrs Per Day / CP Avg Hrs Per Vehicle":f"{round_off(All_Sold_Hours, 1)} / {round_off(Average_Hours_Sold_Per_Day, 1)} / { round_off(Customer_Pay_Hours_Average_Per_RO, 1)} "
        #     }
    # ]

    
# Main execution starts here
columns_to_check = ['lbrsale', 'lbrsoldhours', 'prtextendedsale', 'prtextendedcost']
retail_flag = {'C'}

# Get environment variables
working_days = float(os.environ.get('working_days'))
storeid = os.environ.get('store_id')
realm = os.environ.get('realm') 
s_date_env = os.environ.get('start_date')
e_date_env = os.environ.get('end_date')
advisor_set = os.environ.get('advisor')
tech_set = os.environ.get('technician')

# Process advisor and tech sets
if isinstance(advisor_set, str):
    if ',' in advisor_set:
        advisor = {x.strip() for x in advisor_set.split(',')}
    else:
        advisor = {advisor_set.strip()}
else:
    advisor = set()

if advisor:
    advisor_id = next(iter(advisor))   
else:
    log_info("No valid advisor ID found.")

if ',' in tech_set:
    tech = set(x.strip() for x in tech_set.split(','))
else:
    tech = {tech_set.strip()}

# Parse start date
s_year, s_month, s_date = map(int, s_date_env.split("-"))
start_date_base = datetime(s_year, s_month, s_date)

# Parse end date to determine if we need single month or 12 months
e_year, e_month, e_date = map(int, e_date_env.split("-"))
end_date_base = datetime(e_year, e_month, e_date)

# Check if start and end dates are in the same month and year
same_month_year = (s_year == e_year and s_month == e_month)

# Load data
# Fetch all data for the year
all_revenue_details_table_db_connect = allRevenueDetailsCPOverview()
all_revenue_details = all_revenue_details_table_db_connect.getTableResult()

# Prepare data
all_revenue_details["closeddate"] = pd.to_datetime(all_revenue_details["closeddate"], errors="coerce")
# all_revenue_details["store_id"] = pd.to_numeric(all_revenue_details["store_id"], errors='coerce')
storeid = str(storeid)

# Define customer and warranty pay types
if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
    customer_pay_types = {'C'}
    warranty_pay_types = {'W', 'F', 'M', 'E'}
elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
    customer_pay_types = {'C', 'M'}
    warranty_pay_types = {'W', 'F', 'E'}
elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
    customer_pay_types = {'C', 'E'}
    warranty_pay_types = {'W', 'F', 'M'}
elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
    customer_pay_types = {'C', 'E', 'M'}
    warranty_pay_types = {'W', 'F'}

# Initialize data structures
monthly_results = {}
monthly_summary = {
    "Labor Sale - Customer Pay": [],
    "Parts Revenue": [],
    "Combined Revenue": [],
    "Labor Gross Profit": [],
    "Parts Gross Profit": [],
    "Combined Gross Profit": [],
    "Labor Gross Profit %": [],
    "Parts Gross Profit %": [],
    "Combined Gross Profit %": [],
    "Labor Sold Hours": [],
    "ELR": [],
    "Parts Markup": [],
    "Total Parts Sales": [],
    "Total Parts Cost": [],
    "Total Labor Cost": [],
    "RO Count": [],
    "Job Count": [],
    "Labor Sale/RO": [],
    "ELR Total Shop": [],
    "Effective Labor Rate - Repair": [],
    "Repair Hours": [],
    "Labor Sale - Repair": [],
    "Labor Sold Hours - Repair": [],
    "Labor Sale - Maint and Comp": [],
    "Labor Sold Hours - Maint and Comp": [],
    "Effective Labor Rate - Maint and Comp": []
    
    }
# Determine number of months to process
if same_month_year:
    # Process only one month when specific month/year is specified
    months_to_process = 1
    log_info(f"Processing single month: {s_year}-{s_month:02d}")
else:
    # Process 12 months for year view
    months_to_process = 12
    log_info("Processing 12 months of data")

for month_offset in range(months_to_process):
    # Calculate the start and end date for current month
    current_month_start = start_date_base + relativedelta(months=month_offset)
    current_month_end = current_month_start + relativedelta(months=1) - timedelta(days=1)

    month_key = current_month_start.strftime('%Y-%m-01')

    log_info(f"Processing month: {month_key}")

    # Process data for current month
    month_data = process_month_data(
        all_revenue_details,
        current_month_start,
        current_month_end,
        storeid,
        advisor,
        tech,
        retail_flag,
        customer_pay_types,
        warranty_pay_types,
        working_days,
        columns_to_check
    )

    if month_data:
        monthly_results[month_key] = month_data

        # Add to summary with month formatting
        monthly_summary["Labor Sale - Customer Pay"].append(f"{month_data['labor_revenue']} ({month_key})")
        monthly_summary["Parts Revenue"].append(f"{month_data['parts_revenue']} ({month_key})")
        monthly_summary["Combined Revenue"].append(f"{month_data['combined_revenue']} ({month_key})")
        monthly_summary["Labor Gross Profit"].append(f"{month_data['labor_gross_profit']} ({month_key})")
        monthly_summary["Parts Gross Profit"].append(f"{month_data['parts_gross_profit']} ({month_key})")
        monthly_summary["Combined Gross Profit"].append(f"{month_data['combined_gross_profit']} ({month_key})")
        monthly_summary["Labor Gross Profit %"].append(f"{month_data['labor_gross_profit_percentage']}% ({month_key})")
        monthly_summary["Parts Gross Profit %"].append(f"{month_data['parts_gross_profit_percentage']}% ({month_key})")
        monthly_summary["Combined Gross Profit %"].append(f"{month_data['combined_gross_profit_percentage']}% ({month_key})")
        monthly_summary["Labor Sold Hours"].append(f"{month_data['labor_sold_hours']} ({month_key})")
        monthly_summary["ELR"].append(f"{month_data['effective_labor_rate_cp']} ({month_key})")
        monthly_summary["Parts Markup"].append(f"{month_data['cp_parts_markup_cp']} ({month_key})")
        monthly_summary["Total Parts Sales"].append(f"{month_data['total_parts_sales']} ({month_key})")
        monthly_summary["Total Parts Cost"].append(f"{month_data['total_parts_cost']} ({month_key})")
        monthly_summary["Total Labor Cost"].append(f"{month_data['labor_cost']} ({month_key})")
        monthly_summary["RO Count"].append(f"{month_data['ro_count']} ({month_key})"),
        monthly_summary["Job Count"].append(f"{month_data['job_count']} ({month_key})"),
        monthly_summary["Labor Sale/RO"].append(f"{month_data['Labor_Sales_Per_RO']} ({month_key})")
        monthly_summary["ELR Total Shop"].append(f"{month_data['Total_ELR']} ({month_key})")
        monthly_summary["Effective Labor Rate - Repair"].append(f"{month_data['Repair_ELR']} ({month_key})")
        
        monthly_summary["Repair Hours"].append(f"{month_data['Repair_Hours']} ({month_key})")
        monthly_summary["Labor Sale - Repair"].append(f"{month_data['Repair_L_Sales']} ({month_key})")
        monthly_summary["Labor Sold Hours - Repair"].append(f"{month_data['Labor_Sold_Hours_Repair']} ({month_key})")
        monthly_summary["Labor Sale - Maint and Comp"].append(f"{month_data['Labor_Sale_Maint_and_Competitive']} ({month_key})")
        monthly_summary["Labor Sold Hours - Maint and Comp"].append(f"{month_data['Labor_Sold Hours_Maint_and_Competitive']} ({month_key})")
        monthly_summary["Effective Labor Rate - Maint and Comp"].append(f"{month_data['Effective_Labor_Rate_Main_and_Competitive']} ({month_key})")

    else:
        log_info(f"No data available for month: {month_key}")

# Create the final result set with dynamic naming
period_description = "single_month" if same_month_year else "12_months"
result_set = {
    f"cp_summary_overview_{period_description}": {
        "monthly_data": monthly_results,
        "summary_format": monthly_summary
    }
}

# Print results in the requested format
period_label = "Single Month" if same_month_year else "12-Month"
log_info(f"\n{period_label} Summary Results:")
log_info("=" * 60)
for key, values in monthly_summary.items():
    if values:  # Only print if there are values
        log_info(f"\n{key}:")
        for value in values:
            log_info(f"    {value}")

# Create results directory if it doesn't exist
results_dir = "chart_processing_results"
if not os.path.exists(results_dir):
    os.makedirs(results_dir)

# Generate timestamp for file naming
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Save to JSON file with dynamic naming in results directory
output_filename = f"cp_overview_{period_description}_results.json"
with open(output_filename, 'w', encoding='utf-8') as json_file:
    json.dump(result_set, json_file, indent=4, ensure_ascii=False)

# Also save in results directory with timestamp
db_results_file = os.path.join(results_dir, f"db_calculated_value_labor.json")
with open(db_results_file, 'w', encoding='utf-8') as json_file:
    json.dump(result_set, json_file, indent=4, ensure_ascii=False)

log_info(f"\n{period_label} CP summary data written successfully to {output_filename}")
log_info(f"DB calculated values saved to {db_results_file}")

# Function to find the latest chart processing success file
def find_latest_chart_success_file(results_dir):
    """Find the most recent chart_processing_success file"""
    success_files = []
    if os.path.exists(results_dir):
        for file in os.listdir(results_dir):
            if file.startswith("chart_processing_success_") and file.endswith(".json"):
                success_files.append(file)

    if success_files:
        # Sort by timestamp in filename and return the latest
        success_files.sort(reverse=True)
        return os.path.join(results_dir, success_files[0])
    return None

# Function to extract UI values from chart processing success file
def extract_ui_values(chart_success_file):
    """Extract UI values from chart processing success file"""
    ui_values = {}

    try:
        with open(chart_success_file, 'r', encoding='utf-8') as f:
            chart_data = json.load(f)

        # Extract values from the chart data
        for entry in chart_data:
            if 'extracted_data' in entry and 'extraction_data' in entry['extracted_data']:
                extraction_data = entry['extracted_data']['extraction_data']
                target_month = entry.get('target_month_year', 'Unknown')

                if 'mui_grid_data' in extraction_data:
                    for container in extraction_data['mui_grid_data']:
                        if 'items' in container:
                            for item in container['items']:
                                title = item.get('title', '')
                                value = item.get('value', '')

                                # Clean the value (remove $ and commas)
                                clean_value = value.replace('$', '').replace(',', '').replace('%', '').strip()

                                # Store the value with month and title as key
                                key = f"{title} ({target_month})"
                                if key not in ui_values:
                                    ui_values[key] = clean_value

    except Exception as e:
        log_error(f"Error reading chart success file: {e}")

    return ui_values

# Function to compare DB and UI values
def compare_values(db_summary, ui_values, target_month):
    """Compare DB calculated values with UI extracted values"""
    comparison_results = []

    # Mapping between DB keys and UI titles
    key_mapping = {
        "Labor Sale - Customer Pay": "Labor Sale - Customer Pay",
        "Parts Revenue": "Total Parts Sale",
        "Combined Revenue": "Combined Revenue",
        "Labor Gross Profit": "Labor Gross Profit",
        "Parts Gross Profit": "Parts Gross Profit",
        "Combined Gross Profit": "Combined Gross Profit",
        "Labor Gross Profit %": "Labor Gross Profit %",
        "Parts Gross Profit %": "Parts Gross Profit %",
        "Combined Gross Profit %": "Combined Gross Profit %",
        "Labor Sold Hours": "Labor Sold Hours",
        "Total Parts Sales": "Total Parts Sale",
        "Total Parts Cost": "Total Parts Cost",
        "Total Labor Cost": "Total Labor Cost"
    }

    for db_key, db_values_list in db_summary.items():
        if db_values_list:  # Only process if there are values
            ui_title = key_mapping.get(db_key, db_key)

            for db_value_str in db_values_list:
                # Extract the numeric value and month from DB format: "value (YYYY-MM-DD)"
                if '(' in db_value_str and ')' in db_value_str:
                    db_value_part = db_value_str.split('(')[0].strip()
                    month_part = db_value_str.split('(')[1].replace(')', '').strip()

                    # Convert month format from YYYY-MM-DD to YYYY-MM-01 for matching
                    if month_part == target_month:
                        # Clean DB value
                        db_clean = db_value_part.replace('$', '').replace(',', '').replace('%', '').strip()

                        # Find corresponding UI value
                        ui_key = f"{ui_title} ({target_month})"
                        ui_value = ui_values.get(ui_key, "Not Found")

                        # Compare values
                        match = False
                        if ui_value != "Not Found":
                            try:
                                db_float = float(db_clean)
                                ui_float = float(ui_value)
                                # Allow small floating point differences
                                match = abs(db_float - ui_float) < 0.01
                            except ValueError:
                                match = db_clean == ui_value

                        comparison_results.append({
                            "Metric": db_key,
                            "Month": target_month,
                            "DB_Value": db_value_part,
                            "UI_Value": ui_value if ui_value != "Not Found" else "Not Found",
                            "Match": match
                        })

    return comparison_results

# Find and process chart success file
chart_success_file = find_latest_chart_success_file(results_dir)

if chart_success_file:
    log_info(f"\nFound chart processing success file: {chart_success_file}")

    # Extract UI values
    ui_values = extract_ui_values(chart_success_file)
    log_info(f"Extracted {len(ui_values)} UI values")

    # Get target month from the data (use the first month in results)
    target_month = None
    if monthly_results:
        target_month = list(monthly_results.keys())[0]

    if target_month:
        log_info(f"Comparing values for target month: {target_month}")

        # Compare values
        comparison_results = compare_values(monthly_summary, ui_values, target_month)

        if comparison_results:
            # Create comparison CSV
            comparison_csv_file = os.path.join(results_dir, f"ui_db_comparison_{timestamp}.csv")

            import csv
            with open(comparison_csv_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ["Metric", "Month", "DB_Value", "UI_Value", "Match"]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(comparison_results)

            log_info(f"\nComparison results saved to: {comparison_csv_file}")

            # log_info summary
            total_comparisons = len(comparison_results)
            matches = sum(1 for result in comparison_results if result["Match"])
            log_info(f"\nComparison Summary:")
            log_info(f"Total comparisons: {total_comparisons}")
            log_info(f"Matches: {matches}")
            log_info(f"Mismatches: {total_comparisons - matches}")
            log_info(f"Match rate: {(matches/total_comparisons)*100:.1f}%" if total_comparisons > 0 else "No comparisons")

        else:
            log_info("No comparison results generated")
    else:
        log_info("No target month found in DB results")
else:
    log_info(f"\nNo chart processing success file found in {results_dir}")
    log_info("Skipping UI vs DB comparison")