2025-09-19 14:42:11,556 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_1: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-19 14:43:56,850 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_5: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-19 14:44:43,879 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_1: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-19 14:46:30,007 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_5: Error in enhanced processing: cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-19 14:55:31,617 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Attempt 1 failed, retrying in 2 seconds...
2025-09-19 14:55:36,738 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Attempt 2 failed, retrying in 2 seconds...
2025-09-19 14:55:41,886 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2979) All 3 attempts failed
2025-09-19 14:55:41,887 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_938_point_0: Data extraction failed or incomplete
2025-09-19 14:56:10,373 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Attempt 1 failed, retrying in 2 seconds...
2025-09-19 14:56:15,485 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Attempt 2 failed, retrying in 2 seconds...
2025-09-19 14:56:20,635 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:2979) All 3 attempts failed
2025-09-19 14:56:20,635 [ERROR] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_938_point_1: Data extraction failed or incomplete
2025-09-19 14:58:13,410 [ERROR] [FOPC QA AUTOMATION] (events.py:88)  6 tasks failed - check failed results file for details
2025-09-19 14:58:55,680 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 1: float() argument must be a string or a real number, not 'NoneType'
2025-09-19 14:58:55,681 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 5: float() argument must be a string or a real number, not 'NoneType'
2025-09-19 14:58:55,681 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 7: float() argument must be a string or a real number, not 'NoneType'
2025-09-19 14:58:55,681 [ERROR] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Error processing chart 11: float() argument must be a string or a real number, not 'NoneType'
