2025-09-19 14:40:46,311 [INFO] [root] (run_all_tests.py:39) Started running: validate_metrics
2025-09-19 14:40:46,311 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-09-19 14:40:46
2025-09-19 14:40:46,311 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Processing mode: Parallel (3 browsers, different charts)
2025-09-19 14:40:46,311 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Max concurrent browsers: 3
2025-09-19 14:40:46,311 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Target months/years: ['2025-07-01']
2025-09-19 14:40:46,311 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Browser timeout: 30000ms
2025-09-19 14:40:46,311 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-19 14:40:46,311 [INFO] [FOPC QA AUTOMATION] (events.py:88) No valid authentication found. Setting up authentication...
2025-09-19 14:40:46,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3480) Setting up authentication...
2025-09-19 14:40:46,977 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Login attempt 1/3
2025-09-19 14:40:46,977 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Navigating to login page...
2025-09-19 14:40:52,153 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Clicking login button...
2025-09-19 14:40:52,485 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Filling username and password...
2025-09-19 14:40:53,492 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Selecting store...
2025-09-19 14:40:56,641 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442) Accessing dashboard...
2025-09-19 14:40:58,109 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:442)  Login completed successfully
2025-09-19 14:40:58,163 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:446) Auth state saved to file
2025-09-19 14:40:58,163 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3480)  Authentication setup completed successfully
2025-09-19 14:40:58,282 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
 Starting parallel chart processing workflow ...
2025-09-19 14:40:58,282 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Creating chart-point combinations...
2025-09-19 14:41:09,194 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Discovering charts...BEFORE...................target_months_years=============== ['2025-07-01']
2025-09-19 14:41:25,335 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 948: CP 1-Line-RO Count
2025-09-19 14:41:25,335 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,335 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 0 for target: 2025-07-01
2025-09-19 14:41:25,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 3 matching points in chart 0 for 2025-07-01
2025-09-19 14:41:25,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 0, 'datasetIndex': 0, 'pointIndex': 1, 'value': '309', 'xLabel': '2025-07-01', 'screenX': 395.9605438701923, 'screenY': 447.5137726000436, 'canvasX': 120.96054387019231, 'canvasY': 145.51377260004355, 'datasetLabel': 'Single line Mileage Under 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 0, 'datasetIndex': 1, 'pointIndex': 1, 'value': '110', 'xLabel': '2025-07-01', 'screenX': 395.9605438701923, 'screenY': 517.908761723851, 'canvasX': 120.96054387019231, 'canvasY': 215.90876172385092, 'datasetLabel': 'Single line Mileage Over 60k', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 0, 'datasetIndex': 2, 'pointIndex': 1, 'value': '419', 'xLabel': '2025-07-01', 'screenX': 395.9605438701923, 'screenY': 408.6019695667832, 'canvasX': 120.96054387019231, 'canvasY': 106.60196956678324, 'datasetLabel': 'Single line Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 3 matching points for 2025-07-01
2025-09-19 14:41:25,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 0 - 2025-07-01 (3 points)
2025-09-19 14:41:25,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 1357: Average RO Open Days
2025-09-19 14:41:25,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 1 for target: 2025-07-01
2025-09-19 14:41:25,353 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 6 matching points in chart 1 for 2025-07-01
2025-09-19 14:41:25,354 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 1, 'datasetIndex': 0, 'pointIndex': 1, 'value': '1.95', 'xLabel': '2025-07-01', 'screenX': 1199.0567728365386, 'screenY': 548.7728964025051, 'canvasX': 115.05677283653847, 'canvasY': 246.77289640250513, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 1, 'datasetIndex': 1, 'pointIndex': 1, 'value': None, 'xLabel': '2025-07-01', 'screenX': None, 'screenY': None, 'canvasX': 115.05677283653847, 'canvasY': nan, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 1, 'datasetIndex': 2, 'pointIndex': 1, 'value': '2.66', 'xLabel': '2025-07-01', 'screenX': 1199.0567728365386, 'screenY': 545.842719719546, 'canvasX': 115.05677283653847, 'canvasY': 243.84271971954598, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 1, 'datasetIndex': 3, 'pointIndex': 1, 'value': '1.76', 'xLabel': '2025-07-01', 'screenX': 1199.0567728365386, 'screenY': 549.5570281909027, 'canvasX': 115.05677283653847, 'canvasY': 247.55702819090266, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 1, 'datasetIndex': 4, 'pointIndex': 1, 'value': '5.17', 'xLabel': '2025-07-01', 'screenX': 1199.0567728365386, 'screenY': 535.4839260938735, 'canvasX': 115.05677283653847, 'canvasY': 233.48392609387346, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 1, 'datasetIndex': 5, 'pointIndex': 1, 'value': None, 'xLabel': '2025-07-01', 'screenX': None, 'screenY': None, 'canvasX': 115.05677283653847, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,354 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 6 matching points for 2025-07-01
2025-09-19 14:41:25,354 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 1 - 2025-07-01 (6 points)
2025-09-19 14:41:25,354 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 923: CP 1-Line-RO Count Percentage
2025-09-19 14:41:25,354 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,354 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 2 for target: 2025-07-01
2025-09-19 14:41:25,362 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 3 matching points in chart 2 for 2025-07-01
2025-09-19 14:41:25,362 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 2, 'datasetIndex': 0, 'pointIndex': 1, 'value': '69.12751677852348993300', 'xLabel': '2025-07-01', 'screenX': 402.44484375, 'screenY': 766.6272898131705, 'canvasX': 127.44484374999999, 'canvasY': 64.62728981317049, 'datasetLabel': 'Single line Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 2, 'datasetIndex': 1, 'pointIndex': 1, 'value': '51.40186915887850467300', 'xLabel': '2025-07-01', 'screenX': 402.44484375, 'screenY': 815.3965661938597, 'canvasX': 127.44484374999999, 'canvasY': 113.39656619385975, 'datasetLabel': 'Single line Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 2, 'datasetIndex': 2, 'pointIndex': 1, 'value': '63.38880484114977307100', 'xLabel': '2025-07-01', 'screenX': 402.44484375, 'screenY': 782.4164352677356, 'canvasX': 127.44484374999999, 'canvasY': 80.41643526773555, 'datasetLabel': 'Single line Total Shop perc', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,362 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 3 matching points for 2025-07-01
2025-09-19 14:41:25,362 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 2 - 2025-07-01 (3 points)
2025-09-19 14:41:25,362 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 1354: Multi-Line-RO Count
2025-09-19 14:41:25,362 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,362 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 3 for target: 2025-07-01
2025-09-19 14:41:25,367 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 3 matching points in chart 3 for 2025-07-01
2025-09-19 14:41:25,368 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 3, 'datasetIndex': 0, 'pointIndex': 1, 'value': '138', 'xLabel': '2025-07-01', 'screenX': 1204.9605438701924, 'screenY': 842.9151049688401, 'canvasX': 120.96054387019231, 'canvasY': 140.91510496884007, 'datasetLabel': 'Multi line Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 3, 'datasetIndex': 1, 'pointIndex': 1, 'value': '104', 'xLabel': '2025-07-01', 'screenX': 1204.9605438701924, 'screenY': 870.9787689746461, 'canvasX': 120.96054387019231, 'canvasY': 168.97876897464602, 'datasetLabel': 'Multi line Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 3, 'datasetIndex': 2, 'pointIndex': 1, 'value': '242', 'xLabel': '2025-07-01', 'screenX': 1204.9605438701924, 'screenY': 757.0733091863749, 'canvasX': 120.96054387019231, 'canvasY': 55.07330918637484, 'datasetLabel': 'Multi line Total Shop', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,368 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 3 matching points for 2025-07-01
2025-09-19 14:41:25,368 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 3 - 2025-07-01 (3 points)
2025-09-19 14:41:25,368 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 1355: Multi-Line-RO Count Percentage
2025-09-19 14:41:25,368 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,368 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 4 for target: 2025-07-01
2025-09-19 14:41:25,375 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 3 matching points in chart 4 for 2025-07-01
2025-09-19 14:41:25,375 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 4, 'datasetIndex': 0, 'pointIndex': 1, 'value': '30.87248322147651006700', 'xLabel': '2025-07-01', 'screenX': 402.44484375, 'screenY': 1203.927330141982, 'canvasX': 127.44484374999999, 'canvasY': 101.92733014198217, 'datasetLabel': 'Multi line Mileage Under 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 4, 'datasetIndex': 1, 'pointIndex': 1, 'value': '48.59813084112149532700', 'xLabel': '2025-07-01', 'screenX': 402.44484375, 'screenY': 1116.1426326567414, 'canvasX': 127.44484374999999, 'canvasY': 14.142632656741437, 'datasetLabel': 'Multi line Mileage Over 60K', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 4, 'datasetIndex': 2, 'pointIndex': 1, 'value': '36.61119515885022692900', 'xLabel': '2025-07-01', 'screenX': 402.44484375, 'screenY': 1175.506868323765, 'canvasX': 127.44484374999999, 'canvasY': 73.50686832376503, 'datasetLabel': 'Multi line Total Shop perc', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,375 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 3 matching points for 2025-07-01
2025-09-19 14:41:25,375 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 4 - 2025-07-01 (3 points)
2025-09-19 14:41:25,375 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 938: CP Return Rate
2025-09-19 14:41:25,375 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,375 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 5 for target: 2025-07-01
2025-09-19 14:41:25,380 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 2 matching points in chart 5 for 2025-07-01
2025-09-19 14:41:25,381 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 5, 'datasetIndex': 0, 'pointIndex': 1, 'value': '7.35', 'xLabel': '2025-07', 'screenX': 1199.0567728365386, 'screenY': 1205.1529688433807, 'canvasX': 115.05677283653847, 'canvasY': 103.15296884338063, 'datasetLabel': '12 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 5, 'datasetIndex': 1, 'pointIndex': 1, 'value': '8.06', 'xLabel': '2025-07', 'screenX': 1199.0567728365386, 'screenY': 1190.502085428585, 'canvasX': 115.05677283653847, 'canvasY': 88.50208542858485, 'datasetLabel': '6 Months Return Rate', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,381 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 2 matching points for 2025-07-01
2025-09-19 14:41:25,381 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 5 - 2025-07-01 (2 points)
2025-09-19 14:41:25,381 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 930: CP Parts to Labor Ratio
2025-09-19 14:41:25,381 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,381 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 6 for target: 2025-07-01
2025-09-19 14:41:25,385 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 1 matching points in chart 6 for 2025-07-01
2025-09-19 14:41:25,386 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 6, 'datasetIndex': 0, 'pointIndex': 1, 'value': '0.96', 'xLabel': '2025-07-01', 'screenX': 401.8591316105769, 'screenY': 1519.1048225902844, 'canvasX': 126.85913161057692, 'canvasY': 17.104822590284456, 'datasetLabel': 'Parts to Labor Ratio', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,386 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 1 matching points for 2025-07-01
2025-09-19 14:41:25,386 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 6 - 2025-07-01 (1 points)
2025-09-19 14:41:25,386 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 935: Labor Sold Hours Percentage By Pay Type
2025-09-19 14:41:25,386 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,386 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 7 for target: 2025-07-01
2025-09-19 14:41:25,394 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 6 matching points in chart 7 for 2025-07-01
2025-09-19 14:41:25,394 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 7, 'datasetIndex': 0, 'pointIndex': 1, 'value': '0.51', 'xLabel': '2025-07-01', 'screenX': 1217.3486147836538, 'screenY': 1651.581824735339, 'canvasX': 133.34861478365386, 'canvasY': 149.58182473533896, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 7, 'datasetIndex': 1, 'pointIndex': 1, 'value': None, 'xLabel': '2025-07-01', 'screenX': None, 'screenY': None, 'canvasX': 133.34861478365386, 'canvasY': nan, 'datasetLabel': 'Extended Service', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 7, 'datasetIndex': 2, 'pointIndex': 1, 'value': '0.24', 'xLabel': '2025-07-01', 'screenX': 1217.3486147836538, 'screenY': 1707.296451805689, 'canvasX': 133.34861478365386, 'canvasY': 205.296451805689, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 7, 'datasetIndex': 3, 'pointIndex': 1, 'value': '0.08', 'xLabel': '2025-07-01', 'screenX': 1217.3486147836538, 'screenY': 1740.3125271066372, 'canvasX': 133.34861478365386, 'canvasY': 238.31252710663716, 'datasetLabel': 'Maintenance Plan', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 7, 'datasetIndex': 4, 'pointIndex': 1, 'value': '0.17', 'xLabel': '2025-07-01', 'screenX': 1217.3486147836538, 'screenY': 1721.7409847498539, 'canvasX': 133.34861478365386, 'canvasY': 219.7409847498538, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 7, 'datasetIndex': 5, 'pointIndex': 1, 'value': None, 'xLabel': '2025-07-01', 'screenX': None, 'screenY': None, 'canvasX': 133.34861478365386, 'canvasY': nan, 'datasetLabel': 'Factory Service Contract', 'chartType': 'bar', 'coordinatesValid': False, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,394 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 6 matching points for 2025-07-01
2025-09-19 14:41:25,394 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 7 - 2025-07-01 (6 points)
2025-09-19 14:41:25,394 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 936: CP Parts to Labor Ratio By Category
2025-09-19 14:41:25,394 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,394 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 8 for target: 2025-07-01
2025-09-19 14:41:25,400 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 3 matching points in chart 8 for 2025-07-01
2025-09-19 14:41:25,400 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 8, 'datasetIndex': 0, 'pointIndex': 1, 'value': '1.93', 'xLabel': '2025-07-01', 'screenX': 393.00606670673073, 'screenY': 1997.5180014300363, 'canvasX': 118.00606670673076, 'canvasY': 95.51800143003635, 'datasetLabel': 'Competitive', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 8, 'datasetIndex': 1, 'pointIndex': 1, 'value': '0.91', 'xLabel': '2025-07-01', 'screenX': 393.00606670673073, 'screenY': 2081.7089934474543, 'canvasX': 118.00606670673076, 'canvasY': 179.70899344745416, 'datasetLabel': 'Maintenance', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 8, 'datasetIndex': 2, 'pointIndex': 1, 'value': '1.02', 'xLabel': '2025-07-01', 'screenX': 393.00606670673073, 'screenY': 2072.6295727396932, 'canvasX': 118.00606670673076, 'canvasY': 170.6295727396934, 'datasetLabel': 'Repair', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,400 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 3 matching points for 2025-07-01
2025-09-19 14:41:25,400 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 8 - 2025-07-01 (3 points)
2025-09-19 14:41:25,400 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 1239: Revenue - Shop Supplies
2025-09-19 14:41:25,400 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,400 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 9 for target: 2025-07-01
2025-09-19 14:41:25,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 3 matching points in chart 9 for 2025-07-01
2025-09-19 14:41:25,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 9, 'datasetIndex': 0, 'pointIndex': 1, 'value': '59116.59', 'xLabel': '2025-07-01', 'screenX': 1228.5704447115386, 'screenY': 1994.170749175838, 'canvasX': 144.57044471153847, 'canvasY': 92.17074917583798, 'datasetLabel': 'Customer Pay', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 9, 'datasetIndex': 1, 'pointIndex': 1, 'value': '73.23', 'xLabel': '2025-07-01', 'screenX': 1228.5704447115386, 'screenY': 2156.619084157587, 'canvasX': 144.57044471153847, 'canvasY': 254.6190841575872, 'datasetLabel': 'Warranty', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}, {'canvasIndex': 9, 'datasetIndex': 2, 'pointIndex': 1, 'value': '1531.18', 'xLabel': '2025-07-01', 'screenX': 1228.5704447115386, 'screenY': 2152.6077685755026, 'canvasX': 144.57044471153847, 'canvasY': 250.6077685755024, 'datasetLabel': 'Internal', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 3 matching points for 2025-07-01
2025-09-19 14:41:25,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 9 - 2025-07-01 (3 points)
2025-09-19 14:41:25,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 1316: MPI Penetration Percentage
2025-09-19 14:41:25,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,408 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 10 for target: 2025-07-01
2025-09-19 14:41:25,412 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 1 matching points in chart 10 for 2025-07-01
2025-09-19 14:41:25,412 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 10, 'datasetIndex': 0, 'pointIndex': 0, 'value': '2.34', 'xLabel': '2025-07-01', 'screenX': 328.74908528645835, 'screenY': 2440.934140450783, 'canvasX': 53.74908528645833, 'canvasY': 138.9341404507832, 'datasetLabel': 'MPI Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,412 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 1 matching points for 2025-07-01
2025-09-19 14:41:25,413 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 10 - 2025-07-01 (1 points)
2025-09-19 14:41:25,413 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Processing Chart 1317: Menu Penetration Percentage
2025-09-19 14:41:25,413 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Looking for data points matching: 2025-07-01
2025-09-19 14:41:25,413 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Finding points in chart 11 for target: 2025-07-01
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233)  Found 1 matching points in chart 11 for 2025-07-01
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:1233) Matching points: [{'canvasIndex': 11, 'datasetIndex': 0, 'pointIndex': 0, 'value': '2.34', 'xLabel': '2025-07-01', 'screenX': 1151.1694954427082, 'screenY': 2440.934140450783, 'canvasX': 67.16949544270832, 'canvasY': 138.9341404507832, 'datasetLabel': 'Menu Penetration Percentage', 'chartType': 'bar', 'coordinatesValid': True, 'targetMonthYear': '2025-07-01'}]
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Found 1 matching points for 2025-07-01
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)    Added combination: Chart 11 - 2025-07-01 (1 points)
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 1: Average RO Open Days (6 points)
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 7: Labor Sold Hours Percentage By Pay Type (6 points)
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 0: CP 1-Line-RO Count (3 points)
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 2: CP 1-Line-RO Count Percentage (3 points)
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 3: Multi-Line-RO Count (3 points)
2025-09-19 14:41:25,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 4: Multi-Line-RO Count Percentage (3 points)
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 8: CP Parts to Labor Ratio By Category (3 points)
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 9: Revenue - Shop Supplies (3 points)
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 5: CP Return Rate (2 points)
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 6: CP Parts to Labor Ratio (1 points)
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 10: MPI Penetration Percentage (1 points)
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   Added Chart 11: Menu Penetration Percentage (1 points)
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) 
Summary: Created 12 chart-point combinations from 12 charts
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   1357: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   935: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   948: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   923: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   1354: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   1355: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   936: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   1239: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   938: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   930: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   1316: 1 combinations
2025-09-19 14:41:25,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342)   1317: 1 combinations
2025-09-19 14:41:33,281 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:41:33,420 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:41:36,447 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:41:38,881 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:41:39,908 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Customer Pay in chart chart_1357
2025-09-19 14:41:41,937 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1357 interactivity ensured
2025-09-19 14:41:42,938 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1357
2025-09-19 14:41:42,938 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1357_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:41:43,071 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:41:46,074 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357
2025-09-19 14:41:49,077 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1357
2025-09-19 14:41:49,078 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:41:52,079 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-19 14:41:52,099 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:41:52,151 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:41:52,223 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 3
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_0: Data extraction successful
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_0: Enhanced processing completed for 2025-07-01 from Customer Pay
2025-09-19 14:42:03,018 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:42:03,060 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:42:06,087 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:42:07,498 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:42:08,521 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Extended Service in chart chart_1357
2025-09-19 14:42:10,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1357 interactivity ensured
2025-09-19 14:42:11,542 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1357
2025-09-19 14:42:11,542 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1357_point_1: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:42:11,556 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1357_point_1: Trying coordinate-based clicking...
2025-09-19 14:42:22,380 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:42:22,401 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:42:25,410 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:42:26,793 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:42:27,824 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Internal in chart chart_1357
2025-09-19 14:42:29,844 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1357 interactivity ensured
2025-09-19 14:42:30,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1357
2025-09-19 14:42:30,845 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1357_point_2: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:42:30,980 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_2: Chart.js event click successful, checking for navigation...
2025-09-19 14:42:33,982 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_2: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357
2025-09-19 14:42:36,986 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1357
2025-09-19 14:42:36,986 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:42:39,990 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-19 14:42:40,008 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:42:40,057 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:42:40,130 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 3
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_2: Data extraction successful
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_2: Enhanced processing completed for 2025-07-01 from Internal
2025-09-19 14:42:50,910 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:42:50,939 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:42:53,968 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:42:55,378 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:42:56,412 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Maintenance in chart chart_1357
2025-09-19 14:42:58,432 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1357 interactivity ensured
2025-09-19 14:42:59,434 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1357
2025-09-19 14:42:59,434 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1357_point_3: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:42:59,563 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_3: Chart.js event click successful, checking for navigation...
2025-09-19 14:43:02,566 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_3: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357
2025-09-19 14:43:05,569 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1357
2025-09-19 14:43:05,570 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:43:08,571 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Maintenance FROM DRILL DWON
2025-09-19 14:43:08,586 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:43:08,639 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:43:08,718 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 3
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_3: Data extraction successful
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_3: Enhanced processing completed for 2025-07-01 from Maintenance
2025-09-19 14:43:19,559 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:43:19,638 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:43:22,668 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:43:24,124 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:43:25,165 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Warranty in chart chart_1357
2025-09-19 14:43:27,184 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1357 interactivity ensured
2025-09-19 14:43:28,186 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1357
2025-09-19 14:43:28,186 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1357_point_4: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:43:28,317 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_4: Chart.js event click successful, checking for navigation...
2025-09-19 14:43:31,320 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_4: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357
2025-09-19 14:43:34,322 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1357
2025-09-19 14:43:34,322 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:43:37,326 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-19 14:43:37,333 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:43:37,389 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:43:37,466 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 3
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_4: Data extraction successful
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1357_point_4: Enhanced processing completed for 2025-07-01 from Warranty
2025-09-19 14:43:48,292 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:43:48,360 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:43:51,387 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:43:52,796 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:43:53,817 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Factory Service Contract in chart chart_1357
2025-09-19 14:43:55,835 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1357 interactivity ensured
2025-09-19 14:43:56,836 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1357
2025-09-19 14:43:56,836 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1357_point_5: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:43:56,850 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1357_point_5: Trying coordinate-based clicking...
2025-09-19 14:44:05,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:44:05,465 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:44:08,496 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:44:10,943 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:44:11,960 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Customer Pay in chart chart_935
2025-09-19 14:44:13,983 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_935 interactivity ensured
2025-09-19 14:44:14,984 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_935
2025-09-19 14:44:14,984 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_935_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:44:15,123 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:44:18,126 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:44:21,129 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_935
2025-09-19 14:44:21,130 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:44:24,133 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-19 14:44:24,143 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:44:24,289 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:44:24,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 12
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_0: Data extraction successful
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_0: Enhanced processing completed for 2025-07-01 from Customer Pay
2025-09-19 14:44:35,350 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:44:35,395 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:44:38,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:44:39,816 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:44:40,839 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Extended Service in chart chart_935
2025-09-19 14:44:42,856 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_935 interactivity ensured
2025-09-19 14:44:43,856 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_935
2025-09-19 14:44:43,856 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_935_point_1: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:44:43,878 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_935_point_1: Trying coordinate-based clicking...
2025-09-19 14:44:54,698 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:44:54,750 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:44:57,777 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:44:59,210 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:45:00,239 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Internal in chart chart_935
2025-09-19 14:45:02,256 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_935 interactivity ensured
2025-09-19 14:45:03,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_935
2025-09-19 14:45:03,257 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_935_point_2: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:45:03,385 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_2: Chart.js event click successful, checking for navigation...
2025-09-19 14:45:06,386 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_2: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:45:09,387 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_935
2025-09-19 14:45:09,387 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:45:12,390 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-19 14:45:12,410 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:45:12,546 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:45:12,664 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:45:12,778 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:45:12,778 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:45:12,778 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:45:12,778 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:45:12,779 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_2: Data extraction successful
2025-09-19 14:45:12,779 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_2: Enhanced processing completed for 2025-07-01 from Internal
2025-09-19 14:45:23,570 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:45:23,638 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:45:26,668 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:45:28,103 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:45:29,138 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Maintenance Plan in chart chart_935
2025-09-19 14:45:31,165 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_935 interactivity ensured
2025-09-19 14:45:32,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_935
2025-09-19 14:45:32,166 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_935_point_3: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:45:32,301 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_3: Chart.js event click successful, checking for navigation...
2025-09-19 14:45:35,302 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_3: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:45:38,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_935
2025-09-19 14:45:38,306 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:45:41,307 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Maintenance Plan FROM DRILL DWON
2025-09-19 14:45:41,315 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:45:41,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:45:41,541 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_3: Data extraction successful
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_3: Enhanced processing completed for 2025-07-01 from Maintenance Plan
2025-09-19 14:45:52,414 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:45:52,493 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:45:55,522 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:45:56,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:45:57,939 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Warranty in chart chart_935
2025-09-19 14:45:59,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_935 interactivity ensured
2025-09-19 14:46:00,986 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_935
2025-09-19 14:46:00,986 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_935_point_4: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:46:01,124 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_4: Chart.js event click successful, checking for navigation...
2025-09-19 14:46:04,126 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_4: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:46:07,129 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_935
2025-09-19 14:46:07,130 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:46:10,131 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-19 14:46:10,160 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:46:10,333 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:46:10,447 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:46:10,561 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:46:10,561 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:46:10,561 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:46:10,561 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:46:10,561 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_4: Data extraction successful
2025-09-19 14:46:10,562 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_935_point_4: Enhanced processing completed for 2025-07-01 from Warranty
2025-09-19 14:46:21,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:46:21,491 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:46:24,519 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:46:25,936 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:46:26,957 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Factory Service Contract in chart chart_935
2025-09-19 14:46:28,989 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_935 interactivity ensured
2025-09-19 14:46:29,990 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_935
2025-09-19 14:46:29,990 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_935_point_5: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:46:30,006 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_935_point_5: Trying coordinate-based clicking...
2025-09-19 14:46:38,553 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:46:38,687 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:46:41,721 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:46:44,114 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:46:45,133 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Single line Mileage Under 60k in chart chart_948
2025-09-19 14:46:47,161 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_948 interactivity ensured
2025-09-19 14:46:48,162 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_948
2025-09-19 14:46:48,162 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_948_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:46:48,305 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:46:51,309 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:46:54,310 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_948
2025-09-19 14:46:54,311 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:46:57,314 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Single line Mileage Under 60k FROM DRILL DWON
2025-09-19 14:46:57,346 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:46:57,469 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:46:57,596 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:46:57,709 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:46:57,709 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:46:57,709 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:46:57,709 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:46:57,709 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_0: Data extraction successful
2025-09-19 14:46:57,709 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_0: Enhanced processing completed for 2025-07-01 from Single line Mileage Under 60k
2025-09-19 14:47:08,430 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:47:08,486 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:47:11,530 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:47:12,938 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:47:13,958 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Single line Mileage Over 60k in chart chart_948
2025-09-19 14:47:15,985 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_948 interactivity ensured
2025-09-19 14:47:16,986 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_948
2025-09-19 14:47:16,986 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_948_point_1: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:47:17,133 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_1: Chart.js event click successful, checking for navigation...
2025-09-19 14:47:20,136 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_1: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:47:23,138 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_948
2025-09-19 14:47:23,139 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:47:26,142 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Single line Mileage Over 60k FROM DRILL DWON
2025-09-19 14:47:26,169 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:47:26,300 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:47:26,416 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_1: Data extraction successful
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_1: Enhanced processing completed for 2025-07-01 from Single line Mileage Over 60k
2025-09-19 14:47:37,320 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:47:37,398 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:47:40,432 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:47:41,867 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:47:42,891 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Single line Total Shop in chart chart_948
2025-09-19 14:47:44,909 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_948 interactivity ensured
2025-09-19 14:47:45,910 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_948
2025-09-19 14:47:45,910 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_948_point_2: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:47:46,007 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_2: Chart.js event click successful, checking for navigation...
2025-09-19 14:47:49,011 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_2: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:47:52,014 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_948
2025-09-19 14:47:52,015 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:47:55,017 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Single line Total Shop FROM DRILL DWON
2025-09-19 14:47:55,050 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:47:55,168 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:47:55,281 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_2: Data extraction successful
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_948_point_2: Enhanced processing completed for 2025-07-01 from Single line Total Shop
2025-09-19 14:48:03,889 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:48:03,931 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:48:06,964 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:48:09,399 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:48:10,431 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Single line Mileage Under 60K in chart chart_923
2025-09-19 14:48:12,446 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_923 interactivity ensured
2025-09-19 14:48:13,446 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_923
2025-09-19 14:48:13,446 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_923_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:48:13,597 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:48:16,600 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:48:19,602 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_923
2025-09-19 14:48:19,603 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:48:22,605 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Single line Mileage Under 60K FROM DRILL DWON
2025-09-19 14:48:22,641 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:48:22,791 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:48:22,925 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_0: Data extraction successful
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_0: Enhanced processing completed for 2025-07-01 from Single line Mileage Under 60K
2025-09-19 14:48:33,808 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:48:33,840 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:48:36,864 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:48:38,237 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:48:39,261 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Single line Mileage Over 60K in chart chart_923
2025-09-19 14:48:41,284 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_923 interactivity ensured
2025-09-19 14:48:42,286 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_923
2025-09-19 14:48:42,286 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_923_point_1: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:48:42,410 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_1: Chart.js event click successful, checking for navigation...
2025-09-19 14:48:45,413 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_1: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:48:48,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_923
2025-09-19 14:48:48,417 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:48:51,421 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Single line Mileage Over 60K FROM DRILL DWON
2025-09-19 14:48:51,451 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:48:51,562 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:48:51,684 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:48:51,794 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:48:51,794 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:48:51,794 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:48:51,794 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:48:51,794 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_1: Data extraction successful
2025-09-19 14:48:51,795 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_1: Enhanced processing completed for 2025-07-01 from Single line Mileage Over 60K
2025-09-19 14:49:02,723 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:49:02,794 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:49:05,811 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:49:07,237 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:49:08,262 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Single line Total Shop perc in chart chart_923
2025-09-19 14:49:10,277 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_923 interactivity ensured
2025-09-19 14:49:11,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_923
2025-09-19 14:49:11,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_923_point_2: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:49:11,400 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_2: Chart.js event click successful, checking for navigation...
2025-09-19 14:49:14,404 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_2: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:49:17,406 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_923
2025-09-19 14:49:17,407 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:49:20,411 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Single line Total Shop perc FROM DRILL DWON
2025-09-19 14:49:20,442 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:49:20,572 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:49:20,690 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:49:20,802 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:49:20,803 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:49:20,803 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:49:20,803 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:49:20,803 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_2: Data extraction successful
2025-09-19 14:49:20,803 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_923_point_2: Enhanced processing completed for 2025-07-01 from Single line Total Shop perc
2025-09-19 14:49:28,916 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:49:29,059 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:49:32,084 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:49:34,494 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:49:35,520 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Multi line Mileage Under 60K in chart chart_1354
2025-09-19 14:49:37,542 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1354 interactivity ensured
2025-09-19 14:49:38,544 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1354
2025-09-19 14:49:38,544 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1354_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:49:38,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:49:41,682 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354
2025-09-19 14:49:44,686 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1354
2025-09-19 14:49:44,686 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:49:47,690 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Multi line Mileage Under 60K FROM DRILL DWON
2025-09-19 14:49:47,727 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:49:47,803 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:49:47,888 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 3
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_0: Data extraction successful
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_0: Enhanced processing completed for 2025-07-01 from Multi line Mileage Under 60K
2025-09-19 14:49:58,654 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:49:58,722 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:50:01,750 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:50:03,189 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:50:04,221 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Multi line Mileage Over 60K in chart chart_1354
2025-09-19 14:50:06,244 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1354 interactivity ensured
2025-09-19 14:50:07,245 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1354
2025-09-19 14:50:07,245 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1354_point_1: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:50:07,373 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_1: Chart.js event click successful, checking for navigation...
2025-09-19 14:50:10,374 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_1: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354
2025-09-19 14:50:13,376 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1354
2025-09-19 14:50:13,376 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:50:16,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Multi line Mileage Over 60K FROM DRILL DWON
2025-09-19 14:50:16,388 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:50:16,449 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:50:16,523 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:50:16,588 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:50:16,588 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:50:16,588 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 3
2025-09-19 14:50:16,588 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:50:16,589 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_1: Data extraction successful
2025-09-19 14:50:16,589 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_1: Enhanced processing completed for 2025-07-01 from Multi line Mileage Over 60K
2025-09-19 14:50:27,418 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:50:27,495 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:50:30,527 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:50:31,964 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:50:32,988 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Multi line Total Shop in chart chart_1354
2025-09-19 14:50:35,017 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1354 interactivity ensured
2025-09-19 14:50:36,018 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1354
2025-09-19 14:50:36,018 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1354_point_2: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:50:36,126 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_2: Chart.js event click successful, checking for navigation...
2025-09-19 14:50:39,130 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_2: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354
2025-09-19 14:50:42,133 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1354
2025-09-19 14:50:42,134 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:50:45,134 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Multi line Total Shop FROM DRILL DWON
2025-09-19 14:50:45,143 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:50:45,200 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:50:45,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:50:45,331 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:50:45,331 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:50:45,331 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 3
2025-09-19 14:50:45,331 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:50:45,332 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_2: Data extraction successful
2025-09-19 14:50:45,332 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1354_point_2: Enhanced processing completed for 2025-07-01 from Multi line Total Shop
2025-09-19 14:50:53,702 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:50:53,907 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:50:56,957 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:50:59,394 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:51:00,423 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Multi line Mileage Under 60K in chart chart_1355
2025-09-19 14:51:02,454 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1355 interactivity ensured
2025-09-19 14:51:03,456 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1355
2025-09-19 14:51:03,456 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1355_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:51:03,595 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:51:06,598 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:51:09,602 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1355
2025-09-19 14:51:09,602 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:51:12,606 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Multi line Mileage Under 60K FROM DRILL DWON
2025-09-19 14:51:12,640 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:51:12,791 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:51:12,910 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_0: Data extraction successful
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_0: Enhanced processing completed for 2025-07-01 from Multi line Mileage Under 60K
2025-09-19 14:51:23,734 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:51:23,802 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:51:26,831 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:51:28,251 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:51:29,280 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Multi line Mileage Over 60K in chart chart_1355
2025-09-19 14:51:31,297 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1355 interactivity ensured
2025-09-19 14:51:32,298 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1355
2025-09-19 14:51:32,298 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1355_point_1: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:51:32,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_1: Chart.js event click successful, checking for navigation...
2025-09-19 14:51:35,430 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_1: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:51:38,432 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1355
2025-09-19 14:51:38,432 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:51:41,434 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Multi line Mileage Over 60K FROM DRILL DWON
2025-09-19 14:51:41,471 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:51:41,584 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:51:41,707 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_1: Data extraction successful
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_1: Enhanced processing completed for 2025-07-01 from Multi line Mileage Over 60K
2025-09-19 14:51:52,665 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:51:52,738 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:51:55,768 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:51:57,199 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:51:58,241 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Multi line Total Shop perc in chart chart_1355
2025-09-19 14:52:00,269 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1355 interactivity ensured
2025-09-19 14:52:01,270 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1355
2025-09-19 14:52:01,270 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1355_point_2: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:52:01,372 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_2: Chart.js event click successful, checking for navigation...
2025-09-19 14:52:04,374 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_2: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:52:07,376 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1355
2025-09-19 14:52:07,376 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:52:10,380 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Multi line Total Shop perc FROM DRILL DWON
2025-09-19 14:52:10,411 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:52:10,528 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:52:10,646 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_2: Data extraction successful
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1355_point_2: Enhanced processing completed for 2025-07-01 from Multi line Total Shop perc
2025-09-19 14:52:19,178 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:52:19,241 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:52:22,275 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:52:24,711 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:52:25,743 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Competitive in chart chart_936
2025-09-19 14:52:27,762 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_936 interactivity ensured
2025-09-19 14:52:28,762 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_936
2025-09-19 14:52:28,762 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_936_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:52:28,909 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:52:31,910 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:52:34,911 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_936
2025-09-19 14:52:34,911 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:52:37,914 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Competitive FROM DRILL DWON
2025-09-19 14:52:37,925 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:52:38,031 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:52:38,143 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_0: Data extraction successful
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_0: Enhanced processing completed for 2025-07-01 from Competitive
2025-09-19 14:52:49,018 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:52:49,038 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:52:52,067 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:52:53,486 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:52:54,506 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Maintenance in chart chart_936
2025-09-19 14:52:56,537 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_936 interactivity ensured
2025-09-19 14:52:57,538 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_936
2025-09-19 14:52:57,538 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_936_point_1: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:52:57,677 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_1: Chart.js event click successful, checking for navigation...
2025-09-19 14:53:00,678 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_1: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:53:03,682 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_936
2025-09-19 14:53:03,682 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:53:06,686 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Maintenance FROM DRILL DWON
2025-09-19 14:53:06,717 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:53:06,835 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:53:06,980 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_1: Data extraction successful
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_1: Enhanced processing completed for 2025-07-01 from Maintenance
2025-09-19 14:53:17,890 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:53:17,951 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:53:20,981 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:53:22,345 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:53:23,360 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Repair in chart chart_936
2025-09-19 14:53:25,377 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_936 interactivity ensured
2025-09-19 14:53:26,378 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_936
2025-09-19 14:53:26,378 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_936_point_2: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:53:26,508 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_2: Chart.js event click successful, checking for navigation...
2025-09-19 14:53:29,510 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_2: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:53:32,514 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_936
2025-09-19 14:53:32,514 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:53:35,514 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Repair FROM DRILL DWON
2025-09-19 14:53:35,551 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:53:35,649 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:53:35,759 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_2: Data extraction successful
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_936_point_2: Enhanced processing completed for 2025-07-01 from Repair
2025-09-19 14:53:47,578 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:53:47,792 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:53:50,827 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:53:53,253 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:53:54,274 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Customer Pay in chart chart_1239
2025-09-19 14:53:56,308 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1239 interactivity ensured
2025-09-19 14:53:57,309 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1239
2025-09-19 14:53:57,309 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1239_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:53:57,436 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:54:00,440 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239
2025-09-19 14:54:03,442 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1239
2025-09-19 14:54:03,443 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:54:06,445 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Customer Pay FROM DRILL DWON
2025-09-19 14:54:06,477 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:54:06,597 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:54:06,690 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 6
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_0: Data extraction successful
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_0: Enhanced processing completed for 2025-07-01 from Customer Pay
2025-09-19 14:54:17,486 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:54:17,542 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:54:20,557 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:54:21,909 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:54:22,944 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Warranty in chart chart_1239
2025-09-19 14:54:24,970 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1239 interactivity ensured
2025-09-19 14:54:25,972 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1239
2025-09-19 14:54:25,972 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1239_point_1: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:54:26,095 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_1: Chart.js event click successful, checking for navigation...
2025-09-19 14:54:29,098 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_1: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239
2025-09-19 14:54:32,102 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1239
2025-09-19 14:54:32,102 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:54:35,106 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Warranty FROM DRILL DWON
2025-09-19 14:54:35,134 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:54:35,238 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:54:35,338 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:54:35,426 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:54:35,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:54:35,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 6
2025-09-19 14:54:35,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:54:35,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_1: Data extraction successful
2025-09-19 14:54:35,427 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_1: Enhanced processing completed for 2025-07-01 from Warranty
2025-09-19 14:54:46,234 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:54:46,312 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:54:49,361 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:54:50,777 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:54:51,792 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Internal in chart chart_1239
2025-09-19 14:54:53,814 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1239 interactivity ensured
2025-09-19 14:54:54,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1239
2025-09-19 14:54:54,815 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1239_point_2: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:54:54,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_2: Chart.js event click successful, checking for navigation...
2025-09-19 14:54:57,914 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_2: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239
2025-09-19 14:55:00,918 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1239
2025-09-19 14:55:00,919 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:55:03,920 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Internal FROM DRILL DWON
2025-09-19 14:55:03,929 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:55:04,022 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:55:04,127 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:55:04,214 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:55:04,215 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:55:04,215 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 6
2025-09-19 14:55:04,215 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:55:04,215 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_2: Data extraction successful
2025-09-19 14:55:04,215 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1239_point_2: Enhanced processing completed for 2025-07-01 from Internal
2025-09-19 14:55:12,549 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:55:12,728 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:55:15,764 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:55:18,234 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:55:19,254 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for 12 Months Return Rate in chart chart_938
2025-09-19 14:55:21,272 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_938 interactivity ensured
2025-09-19 14:55:22,274 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_938
2025-09-19 14:55:22,274 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_938_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:55:22,435 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_938_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:55:25,438 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_938_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:55:28,442 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_938
2025-09-19 14:55:28,442 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:55:31,446 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: 12 Months Return Rate FROM DRILL DWON
2025-09-19 14:55:31,472 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:55:31,502 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:55:31,571 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:55:31,617 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:55:31,617 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: False
2025-09-19 14:55:31,617 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 0
2025-09-19 14:55:33,618 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 2/3)
2025-09-19 14:55:36,622 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: 12 Months Return Rate FROM DRILL DWON
2025-09-19 14:55:36,644 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:55:36,654 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:55:36,702 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:55:36,737 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:55:36,737 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: False
2025-09-19 14:55:36,737 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 0
2025-09-19 14:55:38,738 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 3/3)
2025-09-19 14:55:41,742 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: 12 Months Return Rate FROM DRILL DWON
2025-09-19 14:55:41,765 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:55:41,787 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:55:41,843 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:55:41,886 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:55:41,886 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: False
2025-09-19 14:55:41,886 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 0
2025-09-19 14:55:41,887 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_938_point_0: Enhanced processing completed for 2025-07 from 12 Months Return Rate
2025-09-19 14:55:52,642 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262) Waiting for charts to load...
2025-09-19 14:55:52,677 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:55:55,704 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3262)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:55:57,066 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:55:58,083 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for 6 Months Return Rate in chart chart_938
2025-09-19 14:56:00,107 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_938 interactivity ensured
2025-09-19 14:56:01,109 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_938
2025-09-19 14:56:01,109 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_938_point_1: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:56:01,263 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_938_point_1: Chart.js event click successful, checking for navigation...
2025-09-19 14:56:04,267 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_938_point_1: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:56:07,270 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_938
2025-09-19 14:56:07,271 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:56:10,272 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: 6 Months Return Rate FROM DRILL DWON
2025-09-19 14:56:10,278 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:56:10,286 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:56:10,334 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:56:10,373 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:56:10,373 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: False
2025-09-19 14:56:10,373 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 0
2025-09-19 14:56:12,374 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 2/3)
2025-09-19 14:56:15,378 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: 6 Months Return Rate FROM DRILL DWON
2025-09-19 14:56:15,396 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:56:15,403 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:56:15,450 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:56:15,485 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:56:15,485 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: False
2025-09-19 14:56:15,485 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 0
2025-09-19 14:56:17,486 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 3/3)
2025-09-19 14:56:20,490 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: 6 Months Return Rate FROM DRILL DWON
2025-09-19 14:56:20,512 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 0 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:56:20,534 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:56:20,599 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:56:20,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:56:20,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: False
2025-09-19 14:56:20,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 0
2025-09-19 14:56:20,635 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_938_point_1: Enhanced processing completed for 2025-07 from 6 Months Return Rate
2025-09-19 14:56:29,013 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:56:29,049 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:56:32,081 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:56:34,534 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:56:35,551 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Parts to Labor Ratio in chart chart_930
2025-09-19 14:56:37,579 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_930 interactivity ensured
2025-09-19 14:56:38,581 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_930
2025-09-19 14:56:38,581 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_930_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:56:38,731 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_930_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:56:41,731 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_930_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:56:44,734 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_930
2025-09-19 14:56:44,734 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:56:47,738 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Parts to Labor Ratio FROM DRILL DWON
2025-09-19 14:56:47,757 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:56:47,864 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:56:47,989 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:56:48,099 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:56:48,100 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:56:48,100 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:56:48,100 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:56:48,100 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_930_point_0: Data extraction successful
2025-09-19 14:56:48,100 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_930_point_0: Enhanced processing completed for 2025-07-01 from Parts to Labor Ratio
2025-09-19 14:56:56,863 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:56:57,053 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:57:00,083 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:57:02,522 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:57:03,545 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for MPI Penetration Percentage in chart chart_1316
2025-09-19 14:57:05,558 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1316 interactivity ensured
2025-09-19 14:57:06,560 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1316
2025-09-19 14:57:06,560 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1316_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:57:06,560 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1316_point_0: Waiting for chart to load...IFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
2025-09-19 14:57:36,722 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1316_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:57:39,722 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1316_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1316
2025-09-19 14:57:42,726 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1316
2025-09-19 14:57:42,726 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:57:45,730 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: MPI Penetration Percentage FROM DRILL DWON
2025-09-19 14:57:45,765 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:57:45,912 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:57:46,037 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:57:46,151 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:57:46,152 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:57:46,152 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:57:46,152 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:57:46,152 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1316_point_0: Data extraction successful
2025-09-19 14:57:46,152 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1316_point_0: Enhanced processing completed for 2025-07-01 from MPI Penetration Percentage
2025-09-19 14:57:54,210 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179) Waiting for charts to load...
2025-09-19 14:57:54,349 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:57:57,386 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3179)  Enhanced legend control applied successfully with comprehensive detection
2025-09-19 14:57:59,770 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3201)  All legends disabled
2025-09-19 14:58:00,786 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3206)  Enabled only legend for Menu Penetration Percentage in chart chart_1317
2025-09-19 14:58:02,800 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3216)  Chart chart_1317 interactivity ensured
2025-09-19 14:58:03,801 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------222>chart_1317
2025-09-19 14:58:03,801 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) chart_1317_point_0: Current URL before click: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:58:03,942 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1317_point_0: Chart.js event click successful, checking for navigation...
2025-09-19 14:58:06,943 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1317_point_0: Navigation successful to: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1317
2025-09-19 14:58:09,946 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230) Chart ID-----------------55-->: chart_1317
2025-09-19 14:58:09,947 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extracting drill-down page data... (Attempt 1/3)
2025-09-19 14:58:12,950 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------1-->: , Dataset Label: Menu Penetration Percentage FROM DRILL DWON
2025-09-19 14:58:12,994 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 1 grid containers with selector: .MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3
2025-09-19 14:58:13,095 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: .MuiGrid-root.MuiGrid-container
2025-09-19 14:58:13,218 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Found 2 grid containers with selector: [class*="MuiGrid-container"]
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Chart ID-----------------4-->: 
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Extraction success: True
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Total items found: 9
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:2979) Data extraction successful on attempt 1
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1317_point_0: Data extraction successful
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3230)  chart_1317_point_0: Enhanced processing completed for 2025-07-01 from Menu Penetration Percentage
2025-09-19 14:58:13,409 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3368) All results saved to chart_processing_results/chart_processing_all.json
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
================================================================================
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88) Parallel processing with 3 browsers completed successfully!
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88) Final Results:
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Total tasks processed: 35
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Charts processed: 12
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Batches processed: 1
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Successful tasks: 29
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Failed tasks: 6
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Success rate: 82.9%
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88)  Parallel processing completed with 29 successful extractions
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
================================================================================
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88) GENERATING FINAL UI vs DB COMPARISON REPORT
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:342) Target month range: 2025-07-01 to 2025-07-31
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:342) Fetching data from database...
2025-09-19 14:58:52,933 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:284) Target month data shape: (3229, 48)
2025-09-19 14:58:55,678 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513) 
================================================================================
2025-09-19 14:58:55,678 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513) RESULTS PROCESSING
2025-09-19 14:58:55,678 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513) ================================================================================
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513) 
Target month Special Metrics data written successfully to chart_processing_results/db_calculated_value.json
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513) 
Target Month Summary for July 2025:
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513)   Total Revenue: $228,438.94
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513)   Total Gross Profit: $139,498.01
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513)   GP Percentage: 61.1%
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513)   Total ROs: 1144
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513)     - Customer Pay ROs: 527
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513)     - Warranty ROs: 284
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513)     - Internal ROs: 333
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513)   Labor sold hours: 798.8
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513) 
================================================================================
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513) Special Metrics ANALYSIS - MAIN EXECUTION COMPLETED
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3513) ================================================================================
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3517) Starting CP Overview comparison...
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3517) Loading UI data from: chart_processing_results/chart_processing_all.json
2025-09-19 14:58:55,679 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3517) Loading DB data from: chart_processing_results/db_calculated_value.json
2025-09-19 14:58:55,680 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Processing 35 UI charts
2025-09-19 14:58:55,681 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:463) Extracted 22 UI chart values
2025-09-19 14:58:55,681 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:464) Target month: 2025-07-01, Processing DB values for date: 2025-07-01
2025-09-19 14:58:55,681 [INFO] [FOPC QA AUTOMATION] (compare_cp_overview.py:464) Extracted 12 DB values from CP overview data
2025-09-19 14:58:55,682 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3517) CSV comparison results saved to Individual_Reports-fopc_simt_prime/cp_overview_comparison_results.csv
2025-09-19 14:58:55,707 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3517) Excel file with highlighted mismatches saved as Individual_Reports-fopc_simt_prime/cp_overview_comparison_highlighted.xlsx
2025-09-19 14:58:55,709 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3517) JSON report saved to Individual_Reports-fopc_simt_prime/cp_overview_comparison.json
2025-09-19 14:58:55,710 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3517) HTML report saved to Individual_Reports-fopc_simt_prime/cp_overview_comparison.html
2025-09-19 14:58:55,710 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3517) CP Overview comparison completed successfully
2025-09-19 14:58:55,710 [INFO] [FOPC QA AUTOMATION] (events.py:88) End Time: 1089.3993406295776
2025-09-19 14:58:55,711 [INFO] [root] (run_all_tests.py:42) Completed: validate_metrics | Time taken: 1089.40 seconds
2025-09-19 14:58:55,711 [INFO] [root] (run_all_tests.py:202) All validations completed in 1105.39 seconds
2025-09-19 14:58:55,963 [INFO] [root] (report_generator.py:96) Combined HTML report created at: Final_Consolidated_Report-fopc_simt_prime/consolidated_report.html
