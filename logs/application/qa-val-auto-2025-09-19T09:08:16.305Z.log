2025-09-19 14:38:16,307 [INFO] [root] (run_all_tests.py:39) Started running: validate_metrics
2025-09-19 14:38:16,307 [INFO] [FOPC QA AUTOMATION] (events.py:88) Start Time: 2025-09-19 14:38:16
2025-09-19 14:38:16,307 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Processing mode: Parallel (3 browsers, different charts)
2025-09-19 14:38:16,307 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Max concurrent browsers: 3
2025-09-19 14:38:16,307 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Target months/years: ['2025-07-01']
2025-09-19 14:38:16,307 [INFO] [FOPC QA AUTOMATION] (events.py:88)    - Browser timeout: 30000ms
2025-09-19 14:38:16,307 [INFO] [FOPC QA AUTOMATION] (events.py:88) ================================================================================
2025-09-19 14:38:16,308 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3477) Auth state loaded from file
2025-09-19 14:38:16,308 [INFO] [FOPC QA AUTOMATION] (events.py:88)  Valid authentication found, proceeding with processing...
2025-09-19 14:38:16,308 [INFO] [FOPC QA AUTOMATION] (events.py:88) 
 Starting parallel chart processing workflow ...
2025-09-19 14:38:16,308 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Creating chart-point combinations...
2025-09-19 14:38:32,366 [INFO] [FOPC QA AUTOMATION] (validate_metrics.py:3342) Discovering charts...BEFORE...................target_months_years=============== ['2025-07-01']
2025-09-19 14:39:08,387 [INFO] [root] (run_all_tests.py:42) Completed: validate_metrics | Time taken: 52.08 seconds
2025-09-19 14:39:08,387 [INFO] [root] (run_all_tests.py:202) All validations completed in 66.22 seconds
2025-09-19 14:39:08,388 [INFO] [root] (report_generator.py:96) Combined HTML report created at: Final_Consolidated_Report-fopc_simt_prime/consolidated_report.html
