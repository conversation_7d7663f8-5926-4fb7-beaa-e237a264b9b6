2025-09-19 14:40:58,282 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🚀 Starting complete chart processing workflow with single browser sequential processing...
2025-09-19 14:41:09,195 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Discovering charts on new component page: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:41:09,195 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Discovering charts on new component page: https://carriageag-simt.fixedopspc.com/SpecialMetrics
2025-09-19 14:41:17,217 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Found charts using selector: canvas.chartjs-render-monitor
2025-09-19 14:41:17,217 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ⏳ Waiting for charts to fully load...
2025-09-19 14:41:20,221 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ⏳ Waiting for chart containers to load...
2025-09-19 14:41:25,242 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Found 12 chart containers on page
2025-09-19 14:41:25,251 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✓ Found 12 canvas elements - charts appear to be loaded
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 📊 Found 12 charts on the new component page
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ✅ Charts with data: 12
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) ❌ Charts with no data: 0
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 1. Chart ID: 948 - CP 1-Line-RO Count ✅ HAS DATA
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-948
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_948 (Method: container-based)
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 302, 'width': 777, 'height': 329}
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 2. Chart ID: 1357 - Average RO Open Days ✅ HAS DATA
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1357
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1357 (Method: container-based)
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 302, 'width': 777, 'height': 329}
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 3. Chart ID: 923 - CP 1-Line-RO Count Percentage ✅ HAS DATA
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-923
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_923 (Method: container-based)
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 702, 'width': 777, 'height': 329}
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 4. Chart ID: 1354 - Multi-Line-RO Count ✅ HAS DATA
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1354
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1354 (Method: container-based)
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 702, 'width': 777, 'height': 329}
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 5. Chart ID: 1355 - Multi-Line-RO Count Percentage ✅ HAS DATA
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1355
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1355 (Method: container-based)
2025-09-19 14:41:25,259 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1102, 'width': 777, 'height': 329}
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 6. Chart ID: 938 - CP Return Rate ✅ HAS DATA
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-938
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_938 (Method: container-based)
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1102, 'width': 777, 'height': 329}
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 7. Chart ID: 930 - CP Parts to Labor Ratio ✅ HAS DATA
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-930
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_930 (Method: container-based)
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1502, 'width': 777, 'height': 329}
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 8. Chart ID: 935 - Labor Sold Hours Percentage By Pay Type ✅ HAS DATA
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-935
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_935 (Method: container-based)
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1502, 'width': 777, 'height': 329}
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 9. Chart ID: 936 - CP Parts to Labor Ratio By Category ✅ HAS DATA
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-936
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_936 (Method: container-based)
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 1902, 'width': 777, 'height': 329}
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 10. Chart ID: 1239 - Revenue - Shop Supplies ✅ HAS DATA
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1239
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1239 (Method: container-based)
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 1902, 'width': 777, 'height': 329}
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 11. Chart ID: 1316 - MPI Penetration Percentage ✅ HAS DATA
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1316
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1316 (Method: container-based)
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 275, 'y': 2302, 'width': 777, 'height': 329}
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 12. Chart ID: 1317 - Menu Penetration Percentage ✅ HAS DATA
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Container: chartContainterId-1317
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Canvas: chart_1317 (Method: container-based)
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Position: {'x': 1084, 'y': 2302, 'width': 777, 'height': 329}
2025-09-19 14:41:25,260 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Visible: True, ChartJS: True
2025-09-19 14:41:25,264 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) All chart container IDs on page: ['chartContainterId-948', 'chartContainterId-1357', 'chartContainterId-923', 'chartContainterId-1354', 'chartContainterId-1355', 'chartContainterId-938', 'chartContainterId-930', 'chartContainterId-935', 'chartContainterId-936', 'chartContainterId-1239', 'chartContainterId-1316', 'chartContainterId-1317']
2025-09-19 14:41:25,498 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Created 12 chart-point combinations
2025-09-19 14:41:25,498 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Step 2: Processing combinations sequentially with single browser...
2025-09-19 14:41:25,498 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Average RO Open Days (chart_1357) with 6 points
2025-09-19 14:41:26,234 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1357
2025-09-19 14:41:38,484 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1357
2025-09-19 14:41:38,484 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2025-07-01 (Customer Pay)
2025-09-19 14:41:38,484 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-19 14:41:39,882 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Customer Pay
2025-09-19 14:41:41,910 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-19 14:41:41,910 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-19 14:41:42,938 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357
2025-09-19 14:41:52,287 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:42:07,088 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:42:07,089 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2025-07-01 (Extended Service)
2025-09-19 14:42:07,089 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-19 14:42:08,499 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Extended Service
2025-09-19 14:42:10,522 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-19 14:42:10,522 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-19 14:42:11,543 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-19 14:42:11,556 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 2: 2025-07-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-19 14:42:11,556 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:42:26,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:42:26,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2025-07-01 (Internal)
2025-09-19 14:42:26,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-19 14:42:27,794 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Internal
2025-09-19 14:42:29,826 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-19 14:42:29,826 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-19 14:42:30,845 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2025-07-01
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357
2025-09-19 14:42:40,193 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:42:54,969 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:42:54,970 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 4/6: 2025-07-01 (Maintenance)
2025-09-19 14:42:54,970 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-19 14:42:56,378 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Maintenance
2025-09-19 14:42:58,414 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-19 14:42:58,414 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-19 14:42:59,434 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 4: 2025-07-01
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357
2025-09-19 14:43:08,790 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:43:23,669 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:43:23,669 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 5/6: 2025-07-01 (Warranty)
2025-09-19 14:43:23,669 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-19 14:43:25,125 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Warranty
2025-09-19 14:43:27,166 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-19 14:43:27,166 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-19 14:43:28,186 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 5: 2025-07-01
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357
2025-09-19 14:43:37,533 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:43:52,389 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:43:52,389 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 6/6: 2025-07-01 (Factory Service Contract)
2025-09-19 14:43:52,389 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1357
2025-09-19 14:43:53,797 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1357 - Factory Service Contract
2025-09-19 14:43:55,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1357 legend is active
2025-09-19 14:43:55,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1357 is interactive after legend control...
2025-09-19 14:43:56,836 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1357
2025-09-19 14:43:56,850 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 6: 2025-07-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-19 14:43:56,850 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Average RO Open Days
2025-09-19 14:43:56,956 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Labor Sold Hours Percentage By Pay Type (chart_935) with 6 points
2025-09-19 14:43:57,678 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_935
2025-09-19 14:44:10,534 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_935
2025-09-19 14:44:10,534 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/6: 2025-07-01 (Customer Pay)
2025-09-19 14:44:10,534 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-19 14:44:11,944 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Customer Pay
2025-09-19 14:44:13,962 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-19 14:44:13,962 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-19 14:44:14,984 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:44:24,574 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:44:39,418 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:44:39,419 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/6: 2025-07-01 (Extended Service)
2025-09-19 14:44:39,419 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-19 14:44:40,817 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Extended Service
2025-09-19 14:44:42,841 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-19 14:44:42,841 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-19 14:44:43,857 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-19 14:44:43,879 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 2: 2025-07-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-19 14:44:43,879 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:44:58,778 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:44:58,779 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/6: 2025-07-01 (Internal)
2025-09-19 14:44:58,779 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-19 14:45:00,210 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Internal
2025-09-19 14:45:02,241 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-19 14:45:02,241 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-19 14:45:03,257 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-19 14:45:12,779 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2025-07-01
2025-09-19 14:45:12,779 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:45:12,779 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:45:12,779 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:45:27,669 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:45:27,669 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 4/6: 2025-07-01 (Maintenance Plan)
2025-09-19 14:45:27,669 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-19 14:45:29,104 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Maintenance Plan
2025-09-19 14:45:31,138 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-19 14:45:31,138 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-19 14:45:32,166 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 4: 2025-07-01
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:45:41,648 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:45:56,524 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:45:56,524 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 5/6: 2025-07-01 (Warranty)
2025-09-19 14:45:56,524 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-19 14:45:57,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Warranty
2025-09-19 14:45:59,941 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-19 14:45:59,941 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-19 14:46:00,986 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-19 14:46:10,562 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 5: 2025-07-01
2025-09-19 14:46:10,562 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:46:10,562 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:46:10,562 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:46:25,521 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:46:25,521 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 6/6: 2025-07-01 (Factory Service Contract)
2025-09-19 14:46:25,521 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_935
2025-09-19 14:46:26,938 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_935 - Factory Service Contract
2025-09-19 14:46:28,959 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_935 legend is active
2025-09-19 14:46:28,959 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_935 is interactive after legend control...
2025-09-19 14:46:29,990 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_935
2025-09-19 14:46:30,007 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 6: 2025-07-01 - cannot access local variable 'extracted_data' where it is not associated with a value
2025-09-19 14:46:30,007 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Labor Sold Hours Percentage By Pay Type
2025-09-19 14:46:30,099 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP 1-Line-RO Count (chart_948) with 3 points
2025-09-19 14:46:30,863 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_948
2025-09-19 14:46:43,747 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_948
2025-09-19 14:46:43,747 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2025-07-01 (Single line Mileage Under 60k)
2025-09-19 14:46:43,747 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-19 14:46:45,115 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Single line Mileage Under 60k
2025-09-19 14:46:47,135 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-19 14:46:47,135 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-19 14:46:48,162 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-19 14:46:57,709 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:46:57,709 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:46:57,710 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:46:57,710 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:47:12,530 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:47:12,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2025-07-01 (Single line Mileage Over 60k)
2025-09-19 14:47:12,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-19 14:47:13,939 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Single line Mileage Over 60k
2025-09-19 14:47:15,960 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-19 14:47:15,960 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-19 14:47:16,986 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2025-07-01
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:47:26,531 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:47:41,434 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:47:41,434 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2025-07-01 (Single line Total Shop)
2025-09-19 14:47:41,434 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_948
2025-09-19 14:47:42,869 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_948 - Single line Total Shop
2025-09-19 14:47:44,892 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_948 legend is active
2025-09-19 14:47:44,892 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_948 is interactive after legend control...
2025-09-19 14:47:45,910 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_948
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2025-07-01
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:47:55,392 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP 1-Line-RO Count
2025-09-19 14:47:55,469 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP 1-Line-RO Count Percentage (chart_923) with 3 points
2025-09-19 14:47:56,189 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_923
2025-09-19 14:48:09,003 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_923
2025-09-19 14:48:09,003 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2025-07-01 (Single line Mileage Under 60K)
2025-09-19 14:48:09,003 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-19 14:48:10,400 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Single line Mileage Under 60K
2025-09-19 14:48:12,433 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-19 14:48:12,433 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-19 14:48:13,446 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:48:23,043 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:48:37,865 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:48:37,865 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2025-07-01 (Single line Mileage Over 60K)
2025-09-19 14:48:37,865 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-19 14:48:39,238 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Single line Mileage Over 60K
2025-09-19 14:48:41,262 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-19 14:48:41,262 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-19 14:48:42,286 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-19 14:48:51,795 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2025-07-01
2025-09-19 14:48:51,795 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:48:51,795 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:48:51,795 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:49:06,812 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:49:06,812 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2025-07-01 (Single line Total Shop perc)
2025-09-19 14:49:06,812 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_923
2025-09-19 14:49:08,238 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_923 - Single line Total Shop perc
2025-09-19 14:49:10,262 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_923 legend is active
2025-09-19 14:49:10,263 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_923 is interactive after legend control...
2025-09-19 14:49:11,278 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_923
2025-09-19 14:49:20,803 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2025-07-01
2025-09-19 14:49:20,803 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:49:20,803 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:49:20,803 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP 1-Line-RO Count Percentage
2025-09-19 14:49:20,888 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Multi-Line-RO Count (chart_1354) with 3 points
2025-09-19 14:49:21,645 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1354
2025-09-19 14:49:34,116 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1354
2025-09-19 14:49:34,116 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2025-07-01 (Multi line Mileage Under 60K)
2025-09-19 14:49:34,116 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1354
2025-09-19 14:49:35,495 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1354 - Multi line Mileage Under 60K
2025-09-19 14:49:37,522 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1354 legend is active
2025-09-19 14:49:37,522 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1354 is interactive after legend control...
2025-09-19 14:49:38,544 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1354
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354
2025-09-19 14:49:47,952 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:50:02,752 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:50:02,752 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2025-07-01 (Multi line Mileage Over 60K)
2025-09-19 14:50:02,752 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1354
2025-09-19 14:50:04,190 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1354 - Multi line Mileage Over 60K
2025-09-19 14:50:06,222 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1354 legend is active
2025-09-19 14:50:06,222 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1354 is interactive after legend control...
2025-09-19 14:50:07,245 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1354
2025-09-19 14:50:16,589 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2025-07-01
2025-09-19 14:50:16,589 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:50:16,589 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354
2025-09-19 14:50:16,589 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:50:31,528 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:50:31,529 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2025-07-01 (Multi line Total Shop)
2025-09-19 14:50:31,529 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1354
2025-09-19 14:50:32,965 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1354 - Multi line Total Shop
2025-09-19 14:50:34,990 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1354 legend is active
2025-09-19 14:50:34,990 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1354 is interactive after legend control...
2025-09-19 14:50:36,019 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1354
2025-09-19 14:50:45,332 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2025-07-01
2025-09-19 14:50:45,332 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:50:45,332 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354
2025-09-19 14:50:45,332 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Multi-Line-RO Count
2025-09-19 14:50:45,423 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Multi-Line-RO Count Percentage (chart_1355) with 3 points
2025-09-19 14:50:46,188 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1355
2025-09-19 14:50:58,992 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1355
2025-09-19 14:50:58,993 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2025-07-01 (Multi line Mileage Under 60K)
2025-09-19 14:50:58,993 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1355
2025-09-19 14:51:00,396 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1355 - Multi line Mileage Under 60K
2025-09-19 14:51:02,423 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1355 legend is active
2025-09-19 14:51:02,424 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1355 is interactive after legend control...
2025-09-19 14:51:03,456 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1355
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:51:13,029 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:51:27,833 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:51:27,833 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2025-07-01 (Multi line Mileage Over 60K)
2025-09-19 14:51:27,833 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1355
2025-09-19 14:51:29,252 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1355 - Multi line Mileage Over 60K
2025-09-19 14:51:31,282 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1355 legend is active
2025-09-19 14:51:31,282 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1355 is interactive after legend control...
2025-09-19 14:51:32,298 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1355
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2025-07-01
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:51:41,818 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:51:56,769 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:51:56,769 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2025-07-01 (Multi line Total Shop perc)
2025-09-19 14:51:56,769 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1355
2025-09-19 14:51:58,201 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1355 - Multi line Total Shop perc
2025-09-19 14:52:00,242 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1355 legend is active
2025-09-19 14:52:00,242 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1355 is interactive after legend control...
2025-09-19 14:52:01,270 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1355
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2025-07-01
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:52:10,760 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Multi-Line-RO Count Percentage
2025-09-19 14:52:10,833 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP Parts to Labor Ratio By Category (chart_936) with 3 points
2025-09-19 14:52:11,569 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_936
2025-09-19 14:52:24,313 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_936
2025-09-19 14:52:24,313 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2025-07-01 (Competitive)
2025-09-19 14:52:24,313 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_936
2025-09-19 14:52:25,712 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_936 - Competitive
2025-09-19 14:52:27,745 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_936 legend is active
2025-09-19 14:52:27,745 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_936 is interactive after legend control...
2025-09-19 14:52:28,763 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_936
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:52:38,261 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:52:53,069 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:52:53,069 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2025-07-01 (Maintenance)
2025-09-19 14:52:53,069 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_936
2025-09-19 14:52:54,486 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_936 - Maintenance
2025-09-19 14:52:56,508 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_936 legend is active
2025-09-19 14:52:56,508 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_936 is interactive after legend control...
2025-09-19 14:52:57,539 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_936
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2025-07-01
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:53:07,094 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:53:21,982 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:53:21,982 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2025-07-01 (Repair)
2025-09-19 14:53:21,982 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_936
2025-09-19 14:53:23,346 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_936 - Repair
2025-09-19 14:53:25,362 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_936 legend is active
2025-09-19 14:53:25,362 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_936 is interactive after legend control...
2025-09-19 14:53:26,378 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_936
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2025-07-01
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:53:35,870 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP Parts to Labor Ratio By Category
2025-09-19 14:53:35,955 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Revenue - Shop Supplies (chart_1239) with 3 points
2025-09-19 14:53:36,708 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1239
2025-09-19 14:53:52,865 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1239
2025-09-19 14:53:52,865 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/3: 2025-07-01 (Customer Pay)
2025-09-19 14:53:52,866 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1239
2025-09-19 14:53:54,254 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1239 - Customer Pay
2025-09-19 14:53:56,277 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1239 legend is active
2025-09-19 14:53:56,277 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1239 is interactive after legend control...
2025-09-19 14:53:57,309 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1239
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239
2025-09-19 14:54:06,774 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:54:21,558 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:54:21,559 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/3: 2025-07-01 (Warranty)
2025-09-19 14:54:21,559 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1239
2025-09-19 14:54:22,910 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1239 - Warranty
2025-09-19 14:54:24,946 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1239 legend is active
2025-09-19 14:54:24,946 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1239 is interactive after legend control...
2025-09-19 14:54:25,972 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1239
2025-09-19 14:54:35,427 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 2: 2025-07-01
2025-09-19 14:54:35,427 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:54:35,427 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239
2025-09-19 14:54:35,427 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:54:50,362 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:54:50,363 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 3/3: 2025-07-01 (Internal)
2025-09-19 14:54:50,363 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1239
2025-09-19 14:54:51,778 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1239 - Internal
2025-09-19 14:54:53,794 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1239 legend is active
2025-09-19 14:54:53,794 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1239 is interactive after legend control...
2025-09-19 14:54:54,815 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1239
2025-09-19 14:55:04,215 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 3: 2025-07-01
2025-09-19 14:55:04,215 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:55:04,215 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239
2025-09-19 14:55:04,215 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Revenue - Shop Supplies
2025-09-19 14:55:04,300 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP Return Rate (chart_938) with 2 points
2025-09-19 14:55:05,021 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_938
2025-09-19 14:55:17,801 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_938
2025-09-19 14:55:17,801 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/2: 2025-07 (12 Months Return Rate)
2025-09-19 14:55:17,801 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_938
2025-09-19 14:55:19,236 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_938 - 12 Months Return Rate
2025-09-19 14:55:21,255 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_938 legend is active
2025-09-19 14:55:21,255 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_938 is interactive after legend control...
2025-09-19 14:55:22,274 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_938
2025-09-19 14:55:41,887 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 1: 2025-07 - Unknown error
2025-09-19 14:55:41,887 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating back to SpecialMetrics for next point
2025-09-19 14:55:56,706 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Successfully navigated back to SpecialMetrics
2025-09-19 14:55:56,706 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 2/2: 2025-07 (6 Months Return Rate)
2025-09-19 14:55:56,706 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_938
2025-09-19 14:55:58,068 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_938 - 6 Months Return Rate
2025-09-19 14:56:00,085 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_938 legend is active
2025-09-19 14:56:00,085 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_938 is interactive after legend control...
2025-09-19 14:56:01,109 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_938
2025-09-19 14:56:20,635 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Failed point 2: 2025-07 - Unknown error
2025-09-19 14:56:20,635 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP Return Rate
2025-09-19 14:56:20,728 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: CP Parts to Labor Ratio (chart_930) with 1 points
2025-09-19 14:56:21,463 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_930
2025-09-19 14:56:34,118 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_930
2025-09-19 14:56:34,118 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/1: 2025-07-01 (Parts to Labor Ratio)
2025-09-19 14:56:34,119 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_930
2025-09-19 14:56:35,534 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_930 - Parts to Labor Ratio
2025-09-19 14:56:37,553 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_930 legend is active
2025-09-19 14:56:37,553 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_930 is interactive after legend control...
2025-09-19 14:56:38,581 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_930
2025-09-19 14:56:48,100 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:56:48,100 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:56:48,100 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown
2025-09-19 14:56:48,100 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: CP Parts to Labor Ratio
2025-09-19 14:56:48,189 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: MPI Penetration Percentage (chart_1316) with 1 points
2025-09-19 14:56:48,913 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1316
2025-09-19 14:57:02,120 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1316
2025-09-19 14:57:02,121 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/1: 2025-07-01 (MPI Penetration Percentage)
2025-09-19 14:57:02,121 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1316
2025-09-19 14:57:03,522 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1316 - MPI Penetration Percentage
2025-09-19 14:57:05,546 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1316 legend is active
2025-09-19 14:57:05,546 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1316 is interactive after legend control...
2025-09-19 14:57:26,579 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1316
2025-09-19 14:57:46,152 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:57:46,152 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:57:46,152 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1316
2025-09-19 14:57:46,152 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: MPI Penetration Percentage
2025-09-19 14:57:46,233 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🎯 Processing chart: Menu Penetration Percentage (chart_1317) with 1 points
2025-09-19 14:57:47,025 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Navigating to SpecialMetrics for chart_1317
2025-09-19 14:57:59,397 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Page setup completed for chart_1317
2025-09-19 14:57:59,397 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Processing point 1/1: 2025-07-01 (Menu Penetration Percentage)
2025-09-19 14:57:59,397 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔒 Disabling all legends before processing chart_1317
2025-09-19 14:58:00,770 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔓 Enabling ONLY legend for chart_1317 - Menu Penetration Percentage
2025-09-19 14:58:02,789 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Legend control successful - ONLY chart_1317 legend is active
2025-09-19 14:58:02,789 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Ensuring chart chart_1317 is interactive after legend control...
2025-09-19 14:58:03,801 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Chart ID-----------------3333--> chart_1317
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed point 1: 2025-07-01
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Click:  | Navigation:  | Extraction:
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) 🔗 Drilldown URL: https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1317
2025-09-19 14:58:13,327 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Completed all points for chart: Menu Penetration Percentage
2025-09-19 14:58:13,405 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Step 3: Saving results...
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Complete chart processing workflow finished successfully
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) Final Summary:
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Total combinations processed: 12
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Total tasks completed: 35
2025-09-19 14:58:13,410 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) - Success rate: 82.9%
2025-09-19 14:58:55,710 [INFO] [FOPC QA AUTOMATION.OMNI] (logger.py:102) HTML report generated: Individual_Reports-fopc_simt_prime/cp_overview_comparison.html
