[{"task_id": "chart_1357_point_0", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 0, "pointIndex": 1, "value": "1.95", "xLabel": "2025-07-01", "screenX": 1199.0567728365386, "screenY": 548.7728964025051, "canvasX": 115.05677283653847, "canvasY": 246.77289640250513, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Customer Pay", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 118.00606670673076, "y": 201.16944239307048}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 0, "pointIndex": 1, "value": "1.95", "xLabel": "2025-07-01", "screenX": 1199.0567728365386, "screenY": 548.7728964025051, "canvasX": 115.05677283653847, "canvasY": 246.77289640250513, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:41:52.079616", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Customer Pay", "value": "1.95", "html_structure": {"h5_html": "Customer Pay", "h6_html": " 1.95"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Customer Pay", "value": "1.95", "html_structure": {"h5_html": "Customer Pay", "h6_html": " 1.95"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Customer Pay", "value": "1.95", "html_structure": {"h5_html": "Customer Pay", "h6_html": " 1.95"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Customer Pay", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-19T14:41:52.287345", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1357_point_1", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 1, "pointIndex": 1, "value": null, "xLabel": "2025-07-01", "screenX": null, "screenY": null, "canvasX": 115.05677283653847, "canvasY": NaN, "datasetLabel": "Extended Service", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2025-07-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-19T14:42:11.556832", "success": false, "legend_controlled": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1357_point_2", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 2, "pointIndex": 1, "value": "2.66", "xLabel": "2025-07-01", "screenX": 1199.0567728365386, "screenY": 545.842719719546, "canvasX": 115.05677283653847, "canvasY": 243.84271971954598, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Internal", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 118.00606670673076, "y": 188.95349453171966}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 2, "pointIndex": 1, "value": "2.66", "xLabel": "2025-07-01", "screenX": 1199.0567728365386, "screenY": 545.842719719546, "canvasX": 115.05677283653847, "canvasY": 243.84271971954598, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:42:39.990433", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Internal", "value": "2.66", "html_structure": {"h5_html": "Internal", "h6_html": " 2.66"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Internal", "value": "2.66", "html_structure": {"h5_html": "Internal", "h6_html": " 2.66"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Internal", "value": "2.66", "html_structure": {"h5_html": "Internal", "h6_html": " 2.66"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Internal", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-19T14:42:40.193531", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1357_point_3", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 3, "pointIndex": 1, "value": "1.76", "xLabel": "2025-07-01", "screenX": 1199.0567728365386, "screenY": 549.5570281909027, "canvasX": 115.05677283653847, "canvasY": 247.55702819090266, "datasetLabel": "Maintenance", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Maintenance", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 115.05677283653847, "y": 241.20143369547014}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 3, "pointIndex": 1, "value": "1.76", "xLabel": "2025-07-01", "screenX": 1199.0567728365386, "screenY": 549.5570281909027, "canvasX": 115.05677283653847, "canvasY": 247.55702819090266, "datasetLabel": "Maintenance", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:43:08.571504", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Maintenance Plan", "value": "1.76", "html_structure": {"h5_html": "Maintenance Plan", "h6_html": " 1.76"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Maintenance Plan", "value": "1.76", "html_structure": {"h5_html": "Maintenance Plan", "h6_html": " 1.76"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Maintenance Plan", "value": "1.76", "html_structure": {"h5_html": "Maintenance Plan", "h6_html": " 1.76"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Maintenance", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-19T14:43:08.790780", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 4, "method": "sequential_processing"}, {"task_id": "chart_1357_point_4", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 4, "pointIndex": 1, "value": "5.17", "xLabel": "2025-07-01", "screenX": 1199.0567728365386, "screenY": 535.4839260938735, "canvasX": 115.05677283653847, "canvasY": 233.48392609387346, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Warranty", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 109.15300180288463, "y": 174.80816976996968}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 4, "pointIndex": 1, "value": "5.17", "xLabel": "2025-07-01", "screenX": 1199.0567728365386, "screenY": 535.4839260938735, "canvasX": 115.05677283653847, "canvasY": 233.48392609387346, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:43:37.326028", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Warranty", "value": "5.17", "html_structure": {"h5_html": " Warranty", "h6_html": " 5.17"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Warranty", "value": "5.17", "html_structure": {"h5_html": " Warranty", "h6_html": " 5.17"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Warranty", "value": "5.17", "html_structure": {"h5_html": " Warranty", "h6_html": " 5.17"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Warranty", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-19T14:43:37.533319", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1357", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 5, "method": "sequential_processing"}, {"task_id": "chart_1357_point_5", "chart_id": "chart_1357", "chart_title": "Average RO Open Days", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 1, "datasetIndex": 5, "pointIndex": 1, "value": null, "xLabel": "2025-07-01", "screenX": null, "screenY": null, "canvasX": 115.05677283653847, "canvasY": NaN, "datasetLabel": "Factory Service Contract", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2025-07-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-19T14:43:56.850623", "success": false, "legend_controlled": true, "point_sequence": 6, "method": "sequential_processing"}, {"task_id": "chart_935_point_0", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 0, "pointIndex": 1, "value": "0.51", "xLabel": "2025-07-01", "screenX": 1217.3486147836538, "screenY": 1651.581824735339, "canvasX": 133.34861478365386, "canvasY": 149.58182473533896, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Customer Pay", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 127.44484374999999, "y": 149.58182473533896}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 0, "pointIndex": 1, "value": "0.51", "xLabel": "2025-07-01", "screenX": 1217.3486147836538, "screenY": 1651.581824735339, "canvasX": 133.34861478365386, "canvasY": 149.58182473533896, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:44:24.133336", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$119,723.81", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $119,723.81"}}, {"item_index": 1, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 2, "title": "Labor Sold Hours - Customer Pay", "value": "798.8", "html_structure": {"h5_html": "Labor Sold Hours - Customer Pay", "h6_html": " 798.8"}}, {"item_index": 3, "title": "Labor Sold Hours % - Customer Pay", "value": "51%", "html_structure": {"h5_html": "Labor Sold Hours % - Customer Pay", "h6_html": " 51%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$119,723.81", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $119,723.81"}}, {"item_index": 1, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 2, "title": "Labor Sold Hours - Customer Pay", "value": "798.8", "html_structure": {"h5_html": "Labor Sold Hours - Customer Pay", "h6_html": " 798.8"}}, {"item_index": 3, "title": "Labor Sold Hours % - Customer Pay", "value": "51%", "html_structure": {"h5_html": "Labor Sold Hours % - Customer Pay", "h6_html": " 51%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$119,723.81", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $119,723.81"}}, {"item_index": 1, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 2, "title": "Labor Sold Hours - Customer Pay", "value": "798.8", "html_structure": {"h5_html": "Labor Sold Hours - Customer Pay", "h6_html": " 798.8"}}, {"item_index": 3, "title": "Labor Sold Hours % - Customer Pay", "value": "51%", "html_structure": {"h5_html": "Labor Sold Hours % - Customer Pay", "h6_html": " 51%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Customer Pay", "total_items_found": 12}, "error": null}, "timestamp": "2025-09-19T14:44:24.574361", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_935_point_1", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 1, "pointIndex": 1, "value": null, "xLabel": "2025-07-01", "screenX": null, "screenY": null, "canvasX": 133.34861478365386, "canvasY": NaN, "datasetLabel": "Extended Service", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2025-07-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-19T14:44:43.879178", "success": false, "legend_controlled": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_935_point_2", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 2, "pointIndex": 1, "value": "0.24", "xLabel": "2025-07-01", "screenX": 1217.3486147836538, "screenY": 1707.296451805689, "canvasX": 133.34861478365386, "canvasY": 205.296451805689, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Internal", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 127.44484374999999, "y": 212.3713250844636}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 2, "pointIndex": 1, "value": "0.24", "xLabel": "2025-07-01", "screenX": 1217.3486147836538, "screenY": 1707.296451805689, "canvasX": 133.34861478365386, "canvasY": 205.296451805689, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:45:12.390777", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 1, "title": "Labor Sold Hours - Internal", "value": "378.20", "html_structure": {"h5_html": "Labor Sold Hours - Internal", "h6_html": " 378.20"}}, {"item_index": 2, "title": "Labor Sold Hours % - Internal", "value": "24%", "html_structure": {"h5_html": "Labor Sold Hours % - Internal", "h6_html": " 24%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 1, "title": "Labor Sold Hours - Internal", "value": "378.20", "html_structure": {"h5_html": "Labor Sold Hours - Internal", "h6_html": " 378.20"}}, {"item_index": 2, "title": "Labor Sold Hours % - Internal", "value": "24%", "html_structure": {"h5_html": "Labor Sold Hours % - Internal", "h6_html": " 24%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 1, "title": "Labor Sold Hours - Internal", "value": "378.20", "html_structure": {"h5_html": "Labor Sold Hours - Internal", "h6_html": " 378.20"}}, {"item_index": 2, "title": "Labor Sold Hours % - Internal", "value": "24%", "html_structure": {"h5_html": "Labor Sold Hours % - Internal", "h6_html": " 24%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Internal", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:45:12.779033", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_935_point_3", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 3, "pointIndex": 1, "value": "0.08", "xLabel": "2025-07-01", "screenX": 1217.3486147836538, "screenY": 1740.3125271066372, "canvasX": 133.34861478365386, "canvasY": 238.31252710663716, "datasetLabel": "Maintenance Plan", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Maintenance Plan", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 121.54107271634615, "y": 131.01028237855562}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 3, "pointIndex": 1, "value": "0.08", "xLabel": "2025-07-01", "screenX": 1217.3486147836538, "screenY": 1740.3125271066372, "canvasX": 133.34861478365386, "canvasY": 238.31252710663716, "datasetLabel": "Maintenance Plan", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:45:41.307087", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 1, "title": "Labor Sold Hours - Maintenance", "value": "133.2", "html_structure": {"h5_html": "Labor Sold Hours - Maintenance", "h6_html": " 133.2"}}, {"item_index": 2, "title": "Labor Sold Hours % - Maintenance", "value": "8%", "html_structure": {"h5_html": "Labor Sold Hours % - Maintenance", "h6_html": " 8%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 1, "title": "Labor Sold Hours - Maintenance", "value": "133.2", "html_structure": {"h5_html": "Labor Sold Hours - Maintenance", "h6_html": " 133.2"}}, {"item_index": 2, "title": "Labor Sold Hours % - Maintenance", "value": "8%", "html_structure": {"h5_html": "Labor Sold Hours % - Maintenance", "h6_html": " 8%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 1, "title": "Labor Sold Hours - Maintenance", "value": "133.2", "html_structure": {"h5_html": "Labor Sold Hours - Maintenance", "h6_html": " 133.2"}}, {"item_index": 2, "title": "Labor Sold Hours % - Maintenance", "value": "8%", "html_structure": {"h5_html": "Labor Sold Hours % - Maintenance", "h6_html": " 8%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Maintenance Plan", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:45:41.648548", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 4, "method": "sequential_processing"}, {"task_id": "chart_935_point_4", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 4, "pointIndex": 1, "value": "0.17", "xLabel": "2025-07-01", "screenX": 1217.3486147836538, "screenY": 1721.7409847498539, "canvasX": 133.34861478365386, "canvasY": 219.7409847498538, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Warranty", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 127.44484374999999, "y": 184.66140474259637}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 4, "pointIndex": 1, "value": "0.17", "xLabel": "2025-07-01", "screenX": 1217.3486147836538, "screenY": 1721.7409847498539, "canvasX": 133.34861478365386, "canvasY": 219.7409847498538, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:46:10.130861", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 1, "title": "Labor Sold Hours - Warranty", "value": "259.9", "html_structure": {"h5_html": "Labor Sold Hours - Warranty", "h6_html": " 259.9"}}, {"item_index": 2, "title": "Labor Sold Hours % - Warranty", "value": "17%", "html_structure": {"h5_html": "Labor Sold Hours % - Warranty", "h6_html": " 17%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 1, "title": "Labor Sold Hours - Warranty", "value": "259.9", "html_structure": {"h5_html": "Labor Sold Hours - Warranty", "h6_html": " 259.9"}}, {"item_index": 2, "title": "Labor Sold Hours % - Warranty", "value": "17%", "html_structure": {"h5_html": "Labor Sold Hours % - Warranty", "h6_html": " 17%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sold Hours - All Categories", "value": "1,570.1", "html_structure": {"h5_html": "Labor Sold Hours - All Categories", "h6_html": " 1,570.1"}}, {"item_index": 1, "title": "Labor Sold Hours - Warranty", "value": "259.9", "html_structure": {"h5_html": "Labor Sold Hours - Warranty", "h6_html": " 259.9"}}, {"item_index": 2, "title": "Labor Sold Hours % - Warranty", "value": "17%", "html_structure": {"h5_html": "Labor Sold Hours % - Warranty", "h6_html": " 17%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Warranty", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:46:10.562061", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 5, "method": "sequential_processing"}, {"task_id": "chart_935_point_5", "chart_id": "chart_935", "chart_title": "Labor Sold Hours Percentage By Pay Type", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 7, "datasetIndex": 5, "pointIndex": 1, "value": null, "xLabel": "2025-07-01", "screenX": null, "screenY": null, "canvasX": 133.34861478365386, "canvasY": NaN, "datasetLabel": "Factory Service Contract", "chartType": "bar", "coordinatesValid": false, "targetMonthYear": "2025-07-01"}, "error": "cannot access local variable 'extracted_data' where it is not associated with a value", "timestamp": "2025-09-19T14:46:30.007137", "success": false, "legend_controlled": true, "point_sequence": 6, "method": "sequential_processing"}, {"task_id": "chart_948_point_0", "chart_id": "chart_948", "chart_title": "CP 1-Line-RO Count", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 0, "datasetIndex": 0, "pointIndex": 1, "value": "309", "xLabel": "2025-07-01", "screenX": 395.9605438701923, "screenY": 447.5137726000436, "canvasX": 120.96054387019231, "canvasY": 145.51377260004355, "datasetLabel": "Single line Mileage Under 60k", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Single line Mileage Under 60k", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 120.96054387019231, "y": 191.0582693321551}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 0, "datasetIndex": 0, "pointIndex": 1, "value": "309", "xLabel": "2025-07-01", "screenX": 395.9605438701923, "screenY": 447.5137726000436, "canvasX": 120.96054387019231, "canvasY": 145.51377260004355, "datasetLabel": "Single line Mileage Under 60k", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:46:57.314794", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "309", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 309"}}, {"item_index": 1, "title": "Labor Sale", "value": "$30,079.29", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $30,079.29"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "244.90", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 244.90"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "309", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 309"}}, {"item_index": 1, "title": "Labor Sale", "value": "$30,079.29", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $30,079.29"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "244.90", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 244.90"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "309", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 309"}}, {"item_index": 1, "title": "Labor Sale", "value": "$30,079.29", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $30,079.29"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "244.90", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 244.90"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Single line Mileage Under 60k", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:46:57.709880", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_948_point_1", "chart_id": "chart_948", "chart_title": "CP 1-Line-RO Count", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 0, "datasetIndex": 1, "pointIndex": 1, "value": "110", "xLabel": "2025-07-01", "screenX": 395.9605438701923, "screenY": 517.908761723851, "canvasX": 120.96054387019231, "canvasY": 215.90876172385092, "datasetLabel": "Single line Mileage Over 60k", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Single line Mileage Over 60k", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 120.96054387019231, "y": 186.72490944890563}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 0, "datasetIndex": 1, "pointIndex": 1, "value": "110", "xLabel": "2025-07-01", "screenX": 395.9605438701923, "screenY": 517.908761723851, "canvasX": 120.96054387019231, "canvasY": 215.90876172385092, "datasetLabel": "Single line Mileage Over 60k", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:47:26.141913", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "110", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 110"}}, {"item_index": 1, "title": "Labor Sale", "value": "$15,779.54", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $15,779.54"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "104.30", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 104.30"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "110", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 110"}}, {"item_index": 1, "title": "Labor Sale", "value": "$15,779.54", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $15,779.54"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "104.30", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 104.30"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "110", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 110"}}, {"item_index": 1, "title": "Labor Sale", "value": "$15,779.54", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $15,779.54"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "104.30", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 104.30"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Single line Mileage Over 60k", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:47:26.531268", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_948_point_2", "chart_id": "chart_948", "chart_title": "CP 1-Line-RO Count", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 0, "datasetIndex": 2, "pointIndex": 1, "value": "419", "xLabel": "2025-07-01", "screenX": 395.9605438701923, "screenY": 408.6019695667832, "canvasX": 120.96054387019231, "canvasY": 106.60196956678324, "datasetLabel": "Single line Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Single line Total Shop", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 120.96054387019231, "y": 180.71126716194723}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 0, "datasetIndex": 2, "pointIndex": 1, "value": "419", "xLabel": "2025-07-01", "screenX": 395.9605438701923, "screenY": 408.6019695667832, "canvasX": 120.96054387019231, "canvasY": 106.60196956678324, "datasetLabel": "Single line Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:47:55.017106", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total Shop", "value": "419", "html_structure": {"h5_html": "Total Shop", "h6_html": " 419"}}, {"item_index": 1, "title": "Labor Sale", "value": "$45,858.83", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $45,858.83"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "349.20", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 349.20"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total Shop", "value": "419", "html_structure": {"h5_html": "Total Shop", "h6_html": " 419"}}, {"item_index": 1, "title": "Labor Sale", "value": "$45,858.83", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $45,858.83"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "349.20", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 349.20"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total Shop", "value": "419", "html_structure": {"h5_html": "Total Shop", "h6_html": " 419"}}, {"item_index": 1, "title": "Labor Sale", "value": "$45,858.83", "html_structure": {"h5_html": "Labor Sale", "h6_html": " $45,858.83"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "349.20", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 349.20"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Single line Total Shop", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:47:55.392353", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_923_point_0", "chart_id": "chart_923", "chart_title": "CP 1-Line-RO Count Percentage", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 2, "datasetIndex": 0, "pointIndex": 1, "value": "69.12751677852348993300", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 766.6272898131705, "canvasX": 127.44484374999999, "canvasY": 64.62728981317049, "datasetLabel": "Single line Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Single line Mileage Under 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 127.44484374999999, "y": 159.72392728514086}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 2, "datasetIndex": 0, "pointIndex": 1, "value": "69.12751677852348993300", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 766.6272898131705, "canvasX": 127.44484374999999, "canvasY": 64.62728981317049, "datasetLabel": "Single line Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:48:22.604874", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "447", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 447"}}, {"item_index": 1, "title": "1 Line Count", "value": "309", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 309"}}, {"item_index": 2, "title": "Mileage Under 60K", "value": "69.13%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 69.13%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "447", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 447"}}, {"item_index": 1, "title": "1 Line Count", "value": "309", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 309"}}, {"item_index": 2, "title": "Mileage Under 60K", "value": "69.13%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 69.13%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "447", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 447"}}, {"item_index": 1, "title": "1 Line Count", "value": "309", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 309"}}, {"item_index": 2, "title": "Mileage Under 60K", "value": "69.13%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 69.13%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Single line Mileage Under 60K", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:48:23.043588", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_923_point_1", "chart_id": "chart_923", "chart_title": "CP 1-Line-RO Count Percentage", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 2, "datasetIndex": 1, "pointIndex": 1, "value": "51.40186915887850467300", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 815.3965661938597, "canvasX": 127.44484374999999, "canvasY": 113.39656619385975, "datasetLabel": "Single line Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Single line Mileage Over 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 127.44484374999999, "y": 163.9051371093067}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 2, "datasetIndex": 1, "pointIndex": 1, "value": "51.40186915887850467300", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 815.3965661938597, "canvasX": 127.44484374999999, "canvasY": 113.39656619385975, "datasetLabel": "Single line Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:48:51.420969", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "214", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 214"}}, {"item_index": 1, "title": "1 Line Count", "value": "110", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 110"}}, {"item_index": 2, "title": "Mileage Over 60K", "value": "51.4%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 51.4%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "214", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 214"}}, {"item_index": 1, "title": "1 Line Count", "value": "110", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 110"}}, {"item_index": 2, "title": "Mileage Over 60K", "value": "51.4%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 51.4%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "214", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 214"}}, {"item_index": 1, "title": "1 Line Count", "value": "110", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 110"}}, {"item_index": 2, "title": "Mileage Over 60K", "value": "51.4%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 51.4%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Single line Mileage Over 60K", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:48:51.795014", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_923_point_2", "chart_id": "chart_923", "chart_title": "CP 1-Line-RO Count Percentage", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 2, "datasetIndex": 2, "pointIndex": 1, "value": "63.38880484114977307100", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 782.4164352677356, "canvasX": 127.44484374999999, "canvasY": 80.41643526773555, "datasetLabel": "Single line Total Shop perc", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Single line Total Shop perc", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 127.44484374999999, "y": 156.7182419193374}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 2, "datasetIndex": 2, "pointIndex": 1, "value": "63.38880484114977307100", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 782.4164352677356, "canvasX": 127.44484374999999, "canvasY": 80.41643526773555, "datasetLabel": "Single line Total Shop perc", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:49:20.410890", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "661", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 661"}}, {"item_index": 1, "title": "1 Line Count", "value": "419", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 419"}}, {"item_index": 2, "title": "Total Shop", "value": "63.39%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 63.39%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "661", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 661"}}, {"item_index": 1, "title": "1 Line Count", "value": "419", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 419"}}, {"item_index": 2, "title": "Total Shop", "value": "63.39%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 63.39%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "661", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 661"}}, {"item_index": 1, "title": "1 Line Count", "value": "419", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 419"}}, {"item_index": 2, "title": "Total Shop", "value": "63.39%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 63.39%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Single line Total Shop perc", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:49:20.803214", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1354_point_0", "chart_id": "chart_1354", "chart_title": "Multi-Line-RO Count", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 3, "datasetIndex": 0, "pointIndex": 1, "value": "138", "xLabel": "2025-07-01", "screenX": 1204.9605438701924, "screenY": 842.9151049688401, "canvasX": 120.96054387019231, "canvasY": 140.91510496884007, "datasetLabel": "Multi line Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Multi line Mileage Under 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 120.96054387019231, "y": 169.39146991590786}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 3, "datasetIndex": 0, "pointIndex": 1, "value": "138", "xLabel": "2025-07-01", "screenX": 1204.9605438701924, "screenY": 842.9151049688401, "canvasX": 120.96054387019231, "canvasY": 140.91510496884007, "datasetLabel": "Multi line Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:49:47.689885", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "138", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 138"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "138", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 138"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Mileage Under 60K", "value": "138", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 138"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Multi line Mileage Under 60K", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-19T14:49:47.952702", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1354_point_1", "chart_id": "chart_1354", "chart_title": "Multi-Line-RO Count", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 3, "datasetIndex": 1, "pointIndex": 1, "value": "104", "xLabel": "2025-07-01", "screenX": 1204.9605438701924, "screenY": 870.9787689746461, "canvasX": 120.96054387019231, "canvasY": 168.97876897464602, "datasetLabel": "Multi line Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Multi line Mileage Over 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 120.96054387019231, "y": 168.97876897464602}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 3, "datasetIndex": 1, "pointIndex": 1, "value": "104", "xLabel": "2025-07-01", "screenX": 1204.9605438701924, "screenY": 870.9787689746461, "canvasX": 120.96054387019231, "canvasY": 168.97876897464602, "datasetLabel": "Multi line Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:50:16.377307", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "104", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 104"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "104", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 104"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Mileage Over 60K", "value": "104", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 104"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Multi line Mileage Over 60K", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-19T14:50:16.589122", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1354_point_2", "chart_id": "chart_1354", "chart_title": "Multi-Line-RO Count", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 3, "datasetIndex": 2, "pointIndex": 1, "value": "242", "xLabel": "2025-07-01", "screenX": 1204.9605438701924, "screenY": 757.0733091863749, "canvasX": 120.96054387019231, "canvasY": 55.07330918637484, "datasetLabel": "Multi line Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Multi line Total Shop", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 120.96054387019231, "y": 154.94693697174304}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 3, "datasetIndex": 2, "pointIndex": 1, "value": "242", "xLabel": "2025-07-01", "screenX": 1204.9605438701924, "screenY": 757.0733091863749, "canvasX": 120.96054387019231, "canvasY": 55.07330918637484, "datasetLabel": "Multi line Total Shop", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:50:45.134795", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total Shop", "value": "242", "html_structure": {"h5_html": "Total Shop", "h6_html": " 242"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total Shop", "value": "242", "html_structure": {"h5_html": "Total Shop", "h6_html": " 242"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total Shop", "value": "242", "html_structure": {"h5_html": "Total Shop", "h6_html": " 242"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Multi line Total Shop", "total_items_found": 3}, "error": null}, "timestamp": "2025-09-19T14:50:45.332049", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1354", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1355_point_0", "chart_id": "chart_1355", "chart_title": "Multi-Line-RO Count Percentage", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 4, "datasetIndex": 0, "pointIndex": 1, "value": "30.87248322147651006700", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 1203.927330141982, "canvasX": 127.44484374999999, "canvasY": 101.92733014198217, "datasetLabel": "Multi line Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Multi line Mileage Under 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 127.44484374999999, "y": 159.26229312265556}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 4, "datasetIndex": 0, "pointIndex": 1, "value": "30.87248322147651006700", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 1203.927330141982, "canvasX": 127.44484374999999, "canvasY": 101.92733014198217, "datasetLabel": "Multi line Mileage Under 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:51:12.606326", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "447", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 447"}}, {"item_index": 1, "title": "1 Line Count", "value": "138", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 138"}}, {"item_index": 2, "title": "Mileage Under 60K", "value": "30.87%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 30.87%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "447", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 447"}}, {"item_index": 1, "title": "1 Line Count", "value": "138", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 138"}}, {"item_index": 2, "title": "Mileage Under 60K", "value": "30.87%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 30.87%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "447", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 447"}}, {"item_index": 1, "title": "1 Line Count", "value": "138", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 138"}}, {"item_index": 2, "title": "Mileage Under 60K", "value": "30.87%", "html_structure": {"h5_html": "Mileage Under 60K", "h6_html": " 30.87%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Multi line Mileage Under 60K", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:51:13.029401", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1355_point_1", "chart_id": "chart_1355", "chart_title": "Multi-Line-RO Count Percentage", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 4, "datasetIndex": 1, "pointIndex": 1, "value": "48.59813084112149532700", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 1116.1426326567414, "canvasX": 127.44484374999999, "canvasY": 14.142632656741437, "datasetLabel": "Multi line Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Multi line Mileage Over 60K", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 127.44484374999999, "y": 134.48159870692635}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 4, "datasetIndex": 1, "pointIndex": 1, "value": "48.59813084112149532700", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 1116.1426326567414, "canvasX": 127.44484374999999, "canvasY": 14.142632656741437, "datasetLabel": "Multi line Mileage Over 60K", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:51:41.434772", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "214", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 214"}}, {"item_index": 1, "title": "1 Line Count", "value": "104", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 104"}}, {"item_index": 2, "title": "Mileage Over 60K", "value": "48.6%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 48.6%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "214", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 214"}}, {"item_index": 1, "title": "1 Line Count", "value": "104", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 104"}}, {"item_index": 2, "title": "Mileage Over 60K", "value": "48.6%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 48.6%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "214", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 214"}}, {"item_index": 1, "title": "1 Line Count", "value": "104", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 104"}}, {"item_index": 2, "title": "Mileage Over 60K", "value": "48.6%", "html_structure": {"h5_html": "Mileage Over 60K", "h6_html": " 48.6%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Multi line Mileage Over 60K", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:51:41.818325", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1355_point_2", "chart_id": "chart_1355", "chart_title": "Multi-Line-RO Count Percentage", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 4, "datasetIndex": 2, "pointIndex": 1, "value": "36.61119515885022692900", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 1175.506868323765, "canvasX": 127.44484374999999, "canvasY": 73.50686832376503, "datasetLabel": "Multi line Total Shop perc", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Multi line Total Shop perc", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 127.44484374999999, "y": 141.49950448626987}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 4, "datasetIndex": 2, "pointIndex": 1, "value": "36.61119515885022692900", "xLabel": "2025-07-01", "screenX": 402.44484375, "screenY": 1175.506868323765, "canvasX": 127.44484374999999, "canvasY": 73.50686832376503, "datasetLabel": "Multi line Total Shop perc", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:52:10.379874", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Total RO Count", "value": "661", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 661"}}, {"item_index": 1, "title": "1 Line Count", "value": "242", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 242"}}, {"item_index": 2, "title": "Total Shop", "value": "15.73%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 15.73%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Total RO Count", "value": "661", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 661"}}, {"item_index": 1, "title": "1 Line Count", "value": "242", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 242"}}, {"item_index": 2, "title": "Total Shop", "value": "15.73%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 15.73%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Total RO Count", "value": "661", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 661"}}, {"item_index": 1, "title": "1 Line Count", "value": "242", "html_structure": {"h5_html": "1 Line Count", "h6_html": " 242"}}, {"item_index": 2, "title": "Total Shop", "value": "15.73%", "html_structure": {"h5_html": "Total Shop", "h6_html": " 15.73%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Multi line Total Shop perc", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:52:10.760357", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_936_point_0", "chart_id": "chart_936", "chart_title": "CP Parts to Labor Ratio By Category", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 8, "datasetIndex": 0, "pointIndex": 1, "value": "1.93", "xLabel": "2025-07-01", "screenX": 393.00606670673073, "screenY": 1997.5180014300363, "canvasX": 118.00606670673076, "canvasY": 95.51800143003635, "datasetLabel": "Competitive", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Competitive", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 118.00606670673076, "y": 175.1692830935738}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 8, "datasetIndex": 0, "pointIndex": 1, "value": "1.93", "xLabel": "2025-07-01", "screenX": 393.00606670673073, "screenY": 1997.5180014300363, "canvasX": 118.00606670673076, "canvasY": 95.51800143003635, "datasetLabel": "Competitive", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:52:37.914676", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Competitive", "value": "$3,372.75", "html_structure": {"h5_html": "Labor Sale - Competitive", "h6_html": " $3,372.75"}}, {"item_index": 1, "title": "Parts Sale - Competitive", "value": "$6,495.5", "html_structure": {"h5_html": "Parts Sale - Competitive", "h6_html": " $6,495.5"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Competitive", "value": "$1.93", "html_structure": {"h5_html": "Parts To Labor Ratio - Competitive", "h6_html": " $1.93"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Competitive", "value": "$3,372.75", "html_structure": {"h5_html": "Labor Sale - Competitive", "h6_html": " $3,372.75"}}, {"item_index": 1, "title": "Parts Sale - Competitive", "value": "$6,495.5", "html_structure": {"h5_html": "Parts Sale - Competitive", "h6_html": " $6,495.5"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Competitive", "value": "$1.93", "html_structure": {"h5_html": "Parts To Labor Ratio - Competitive", "h6_html": " $1.93"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Competitive", "value": "$3,372.75", "html_structure": {"h5_html": "Labor Sale - Competitive", "h6_html": " $3,372.75"}}, {"item_index": 1, "title": "Parts Sale - Competitive", "value": "$6,495.5", "html_structure": {"h5_html": "Parts Sale - Competitive", "h6_html": " $6,495.5"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Competitive", "value": "$1.93", "html_structure": {"h5_html": "Parts To Labor Ratio - Competitive", "h6_html": " $1.93"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Competitive", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:52:38.261719", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_936_point_1", "chart_id": "chart_936", "chart_title": "CP Parts to Labor Ratio By Category", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 8, "datasetIndex": 1, "pointIndex": 1, "value": "0.91", "xLabel": "2025-07-01", "screenX": 393.00606670673073, "screenY": 2081.7089934474543, "canvasX": 118.00606670673076, "canvasY": 179.70899344745416, "datasetLabel": "Maintenance", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Maintenance", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 126.85913161057692, "y": 142.15320779262564}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 8, "datasetIndex": 1, "pointIndex": 1, "value": "0.91", "xLabel": "2025-07-01", "screenX": 393.00606670673073, "screenY": 2081.7089934474543, "canvasX": 118.00606670673076, "canvasY": 179.70899344745416, "datasetLabel": "Maintenance", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:53:06.686062", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Maintenance", "value": "$104,115.89", "html_structure": {"h5_html": "Labor Sale - Maintenance", "h6_html": " $104,115.89"}}, {"item_index": 1, "title": "Parts Sale - Maintenance", "value": "$94,490.54", "html_structure": {"h5_html": "Parts Sale - Maintenance", "h6_html": " $94,490.54"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Maintenance", "value": "$0.91", "html_structure": {"h5_html": "Parts To Labor Ratio - Maintenance", "h6_html": " $0.91"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Maintenance", "value": "$104,115.89", "html_structure": {"h5_html": "Labor Sale - Maintenance", "h6_html": " $104,115.89"}}, {"item_index": 1, "title": "Parts Sale - Maintenance", "value": "$94,490.54", "html_structure": {"h5_html": "Parts Sale - Maintenance", "h6_html": " $94,490.54"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Maintenance", "value": "$0.91", "html_structure": {"h5_html": "Parts To Labor Ratio - Maintenance", "h6_html": " $0.91"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Maintenance", "value": "$104,115.89", "html_structure": {"h5_html": "Labor Sale - Maintenance", "h6_html": " $104,115.89"}}, {"item_index": 1, "title": "Parts Sale - Maintenance", "value": "$94,490.54", "html_structure": {"h5_html": "Parts Sale - Maintenance", "h6_html": " $94,490.54"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Maintenance", "value": "$0.91", "html_structure": {"h5_html": "Parts To Labor Ratio - Maintenance", "h6_html": " $0.91"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Maintenance", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:53:07.094374", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_936_point_2", "chart_id": "chart_936", "chart_title": "CP Parts to Labor Ratio By Category", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 8, "datasetIndex": 2, "pointIndex": 1, "value": "1.02", "xLabel": "2025-07-01", "screenX": 393.00606670673073, "screenY": 2072.6295727396932, "canvasX": 118.00606670673076, "canvasY": 170.6295727396934, "datasetLabel": "Repair", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Repair", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 126.85913161057692, "y": 170.6295727396934}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 8, "datasetIndex": 2, "pointIndex": 1, "value": "1.02", "xLabel": "2025-07-01", "screenX": 393.00606670673073, "screenY": 2072.6295727396932, "canvasX": 118.00606670673076, "canvasY": 170.6295727396934, "datasetLabel": "Repair", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:53:35.514828", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Repair", "value": "$26,993.11", "html_structure": {"h5_html": "Labor Sale - Repair", "h6_html": " $26,993.11"}}, {"item_index": 1, "title": "Parts Sale - Repair", "value": "$27,540.1", "html_structure": {"h5_html": "Parts Sale - Repair", "h6_html": " $27,540.1"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Repair", "value": "$1.02", "html_structure": {"h5_html": "Parts To Labor Ratio - Repair", "h6_html": " $1.02"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Repair", "value": "$26,993.11", "html_structure": {"h5_html": "Labor Sale - Repair", "h6_html": " $26,993.11"}}, {"item_index": 1, "title": "Parts Sale - Repair", "value": "$27,540.1", "html_structure": {"h5_html": "Parts Sale - Repair", "h6_html": " $27,540.1"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Repair", "value": "$1.02", "html_structure": {"h5_html": "Parts To Labor Ratio - Repair", "h6_html": " $1.02"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Repair", "value": "$26,993.11", "html_structure": {"h5_html": "Labor Sale - Repair", "h6_html": " $26,993.11"}}, {"item_index": 1, "title": "Parts Sale - Repair", "value": "$27,540.1", "html_structure": {"h5_html": "Parts Sale - Repair", "h6_html": " $27,540.1"}}, {"item_index": 2, "title": "Parts To Labor Ratio - Repair", "value": "$1.02", "html_structure": {"h5_html": "Parts To Labor Ratio - Repair", "h6_html": " $1.02"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Repair", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:53:35.870594", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_1239_point_0", "chart_id": "chart_1239", "chart_title": "Revenue - Shop Supplies", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 9, "datasetIndex": 0, "pointIndex": 1, "value": "59116.59", "xLabel": "2025-07-01", "screenX": 1228.5704447115386, "screenY": 1994.170749175838, "canvasX": 144.57044471153847, "canvasY": 92.17074917583798, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Customer Pay", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 144.57044471153847, "y": 163.33004349264505}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 9, "datasetIndex": 0, "pointIndex": 1, "value": "59116.59", "xLabel": "2025-07-01", "screenX": 1228.5704447115386, "screenY": 1994.170749175838, "canvasX": 144.57044471153847, "canvasY": 92.17074917583798, "datasetLabel": "Customer Pay", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:54:06.445772", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "RO Count", "value": "592", "html_structure": {"h5_html": "RO Count", "h6_html": " 592"}}, {"item_index": 1, "title": "Shop Supplies - Customer Pay", "value": "$59,116.59", "html_structure": {"h5_html": "Shop Supplies - Customer Pay", "h6_html": " $59,116.59"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "RO Count", "value": "592", "html_structure": {"h5_html": "RO Count", "h6_html": " 592"}}, {"item_index": 1, "title": "Shop Supplies - Customer Pay", "value": "$59,116.59", "html_structure": {"h5_html": "Shop Supplies - Customer Pay", "h6_html": " $59,116.59"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "RO Count", "value": "592", "html_structure": {"h5_html": "RO Count", "h6_html": " 592"}}, {"item_index": 1, "title": "Shop Supplies - Customer Pay", "value": "$59,116.59", "html_structure": {"h5_html": "Shop Supplies - Customer Pay", "h6_html": " $59,116.59"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Customer Pay", "total_items_found": 6}, "error": null}, "timestamp": "2025-09-19T14:54:06.774402", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1239_point_1", "chart_id": "chart_1239", "chart_title": "Revenue - Shop Supplies", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 9, "datasetIndex": 1, "pointIndex": 1, "value": "73.23", "xLabel": "2025-07-01", "screenX": 1228.5704447115386, "screenY": 2156.619084157587, "canvasX": 144.57044471153847, "canvasY": 254.6190841575872, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Warranty", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 144.57044471153847, "y": 254.0650125088961}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 9, "datasetIndex": 1, "pointIndex": 1, "value": "73.23", "xLabel": "2025-07-01", "screenX": 1228.5704447115386, "screenY": 2156.619084157587, "canvasX": 144.57044471153847, "canvasY": 254.6190841575872, "datasetLabel": "Warranty", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:54:35.106131", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "RO Count", "value": "3", "html_structure": {"h5_html": "RO Count", "h6_html": " 3"}}, {"item_index": 1, "title": "Shop Supplies - Warranty", "value": "$73.23", "html_structure": {"h5_html": "Shop Supplies - Warranty", "h6_html": " $73.23"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "RO Count", "value": "3", "html_structure": {"h5_html": "RO Count", "h6_html": " 3"}}, {"item_index": 1, "title": "Shop Supplies - Warranty", "value": "$73.23", "html_structure": {"h5_html": "Shop Supplies - Warranty", "h6_html": " $73.23"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "RO Count", "value": "3", "html_structure": {"h5_html": "RO Count", "h6_html": " 3"}}, {"item_index": 1, "title": "Shop Supplies - Warranty", "value": "$73.23", "html_structure": {"h5_html": "Shop Supplies - Warranty", "h6_html": " $73.23"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Warranty", "total_items_found": 6}, "error": null}, "timestamp": "2025-09-19T14:54:35.427193", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_1239_point_2", "chart_id": "chart_1239", "chart_title": "Revenue - Shop Supplies", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 9, "datasetIndex": 2, "pointIndex": 1, "value": "1531.18", "xLabel": "2025-07-01", "screenX": 1228.5704447115386, "screenY": 2152.6077685755026, "canvasX": 144.57044471153847, "canvasY": 250.6077685755024, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Internal", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 144.57044471153847, "y": 244.2885743030892}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 9, "datasetIndex": 2, "pointIndex": 1, "value": "1531.18", "xLabel": "2025-07-01", "screenX": 1228.5704447115386, "screenY": 2152.6077685755026, "canvasX": 144.57044471153847, "canvasY": 250.6077685755024, "datasetLabel": "Internal", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:55:03.920344", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "RO Count", "value": "10", "html_structure": {"h5_html": "RO Count", "h6_html": " 10"}}, {"item_index": 1, "title": "Shop Supplies - Internal", "value": "$1,531.18", "html_structure": {"h5_html": "Shop Supplies - Internal", "h6_html": " $1,531.18"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "RO Count", "value": "10", "html_structure": {"h5_html": "RO Count", "h6_html": " 10"}}, {"item_index": 1, "title": "Shop Supplies - Internal", "value": "$1,531.18", "html_structure": {"h5_html": "Shop Supplies - Internal", "h6_html": " $1,531.18"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "RO Count", "value": "10", "html_structure": {"h5_html": "RO Count", "h6_html": " 10"}}, {"item_index": 1, "title": "Shop Supplies - Internal", "value": "$1,531.18", "html_structure": {"h5_html": "Shop Supplies - Internal", "h6_html": " $1,531.18"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Internal", "total_items_found": 6}, "error": null}, "timestamp": "2025-09-19T14:55:04.215174", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1239", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "sequential_processing"}, {"task_id": "chart_938_point_0", "chart_id": "chart_938", "chart_title": "CP Return Rate", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 0, "pointIndex": 1, "value": "7.35", "xLabel": "2025-07", "screenX": 1199.0567728365386, "screenY": 1205.1529688433807, "canvasX": 115.05677283653847, "canvasY": 103.15296884338063, "datasetLabel": "12 Months Return Rate", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "12 Months Return Rate", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 115.05677283653847, "y": 178.98676680024593}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 0, "pointIndex": 1, "value": "7.35", "xLabel": "2025-07", "screenX": 1199.0567728365386, "screenY": 1205.1529688433807, "canvasX": 115.05677283653847, "canvasY": 103.15296884338063, "datasetLabel": "12 Months Return Rate", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:55:41.742586", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": false, "error": null, "attempt": 3, "chart_id": "", "dataset_label": "12 Months Return Rate", "total_items_found": 0}, "error": "Data extraction failed after 3 attempts"}, "timestamp": "2025-09-19T14:55:41.887043", "success": false, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": false, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_938_point_1", "chart_id": "chart_938", "chart_title": "CP Return Rate", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 1, "pointIndex": 1, "value": "8.06", "xLabel": "2025-07", "screenX": 1199.0567728365386, "screenY": 1190.502085428585, "canvasX": 115.05677283653847, "canvasY": 88.50208542858485, "datasetLabel": "6 Months Return Rate", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "6 Months Return Rate", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 115.05677283653847, "y": 171.66132509284805}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 5, "datasetIndex": 1, "pointIndex": 1, "value": "8.06", "xLabel": "2025-07", "screenX": 1199.0567728365386, "screenY": 1190.502085428585, "canvasX": 115.05677283653847, "canvasY": 88.50208542858485, "datasetLabel": "6 Months Return Rate", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:56:20.490353", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": false, "error": null, "attempt": 3, "chart_id": "", "dataset_label": "6 Months Return Rate", "total_items_found": 0}, "error": "Data extraction failed after 3 attempts"}, "timestamp": "2025-09-19T14:56:20.635799", "success": false, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": false, "point_sequence": 2, "method": "sequential_processing"}, {"task_id": "chart_930_point_0", "chart_id": "chart_930", "chart_title": "CP Parts to Labor Ratio", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 6, "datasetIndex": 0, "pointIndex": 1, "value": "0.96", "xLabel": "2025-07-01", "screenX": 401.8591316105769, "screenY": 1519.1048225902844, "canvasX": 126.85913161057692, "canvasY": 17.104822590284456, "datasetLabel": "Parts to Labor Ratio", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Parts to Labor Ratio", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 126.85913161057692, "y": 135.96269367369786}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 6, "datasetIndex": 0, "pointIndex": 1, "value": "0.96", "xLabel": "2025-07-01", "screenX": 401.8591316105769, "screenY": 1519.1048225902844, "canvasX": 126.85913161057692, "canvasY": 17.104822590284456, "datasetLabel": "Parts to Labor Ratio", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:56:47.738112", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$134,481.75", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $134,481.75"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$128,526.14", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $128,526.14"}}, {"item_index": 2, "title": "Parts To Labor Ratio", "value": "$0.96", "html_structure": {"h5_html": "Parts To Labor Ratio", "h6_html": " $0.96"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$134,481.75", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $134,481.75"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$128,526.14", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $128,526.14"}}, {"item_index": 2, "title": "Parts To Labor Ratio", "value": "$0.96", "html_structure": {"h5_html": "Parts To Labor Ratio", "h6_html": " $0.96"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$134,481.75", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $134,481.75"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$128,526.14", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $128,526.14"}}, {"item_index": 2, "title": "Parts To Labor Ratio", "value": "$0.96", "html_structure": {"h5_html": "Parts To Labor Ratio", "h6_html": " $0.96"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Parts to Labor Ratio", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:56:48.100221", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1316_point_0", "chart_id": "chart_1316", "chart_title": "MPI Penetration Percentage", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 10, "datasetIndex": 0, "pointIndex": 0, "value": "2.34", "xLabel": "2025-07-01", "screenX": 328.74908528645835, "screenY": 2440.934140450783, "canvasX": 53.74908528645833, "canvasY": 138.9341404507832, "datasetLabel": "MPI Penetration Percentage", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "MPI Penetration Percentage", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 53.74908528645833, "y": 196.8773526039472}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1316", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 10, "datasetIndex": 0, "pointIndex": 0, "value": "2.34", "xLabel": "2025-07-01", "screenX": 328.74908528645835, "screenY": 2440.934140450783, "canvasX": 53.74908528645833, "canvasY": 138.9341404507832, "datasetLabel": "MPI Penetration Percentage", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:57:45.730194", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1316", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "MPI Count", "value": "22", "html_structure": {"h5_html": "MPI Count", "h6_html": " 22"}}, {"item_index": 1, "title": "Total RO Count", "value": "1,017", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 1,017"}}, {"item_index": 2, "title": "MPI Penetration %", "value": "2.16%", "html_structure": {"h5_html": "MPI Penetration %", "h6_html": " 2.16%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "MPI Count", "value": "22", "html_structure": {"h5_html": "MPI Count", "h6_html": " 22"}}, {"item_index": 1, "title": "Total RO Count", "value": "1,017", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 1,017"}}, {"item_index": 2, "title": "MPI Penetration %", "value": "2.16%", "html_structure": {"h5_html": "MPI Penetration %", "h6_html": " 2.16%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "MPI Count", "value": "22", "html_structure": {"h5_html": "MPI Count", "h6_html": " 22"}}, {"item_index": 1, "title": "Total RO Count", "value": "1,017", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 1,017"}}, {"item_index": 2, "title": "MPI Penetration %", "value": "2.16%", "html_structure": {"h5_html": "MPI Penetration %", "h6_html": " 2.16%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "MPI Penetration Percentage", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:57:46.152189", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1316", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}, {"task_id": "chart_1317_point_0", "chart_id": "chart_1317", "chart_title": "Menu Penetration Percentage", "target_month_year": "2025-07-01", "point_data": {"canvasIndex": 11, "datasetIndex": 0, "pointIndex": 0, "value": "2.34", "xLabel": "2025-07-01", "screenX": 1151.1694954427082, "screenY": 2440.934140450783, "canvasX": 67.16949544270832, "canvasY": 138.9341404507832, "datasetLabel": "Menu Penetration Percentage", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "dataset_label": "Menu Penetration Percentage", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 67.16949544270832, "y": 196.8773526039472}}, "navigation_result": {"success": true, "url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1317", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2025-07-01", "point_data": {"canvasIndex": 11, "datasetIndex": 0, "pointIndex": 0, "value": "2.34", "xLabel": "2025-07-01", "screenX": 1151.1694954427082, "screenY": 2440.934140450783, "canvasX": 67.16949544270832, "canvasY": 138.9341404507832, "datasetLabel": "Menu Penetration Percentage", "chartType": "bar", "coordinatesValid": true, "targetMonthYear": "2025-07-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-09-19T14:58:12.950679", "page_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1317", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "<PERSON><PERSON>", "value": "22", "html_structure": {"h5_html": "<PERSON><PERSON>", "h6_html": " 22"}}, {"item_index": 1, "title": "Total RO Count", "value": "661", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 661"}}, {"item_index": 2, "title": "Menu Penetration %", "value": "3.33%", "html_structure": {"h5_html": "Menu Penetration %", "h6_html": " 3.33%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "<PERSON><PERSON>", "value": "22", "html_structure": {"h5_html": "<PERSON><PERSON>", "h6_html": " 22"}}, {"item_index": 1, "title": "Total RO Count", "value": "661", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 661"}}, {"item_index": 2, "title": "Menu Penetration %", "value": "3.33%", "html_structure": {"h5_html": "Menu Penetration %", "h6_html": " 3.33%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "<PERSON><PERSON>", "value": "22", "html_structure": {"h5_html": "<PERSON><PERSON>", "h6_html": " 22"}}, {"item_index": 1, "title": "Total RO Count", "value": "661", "html_structure": {"h5_html": "Total RO Count", "h6_html": " 661"}}, {"item_index": 2, "title": "Menu Penetration %", "value": "3.33%", "html_structure": {"h5_html": "Menu Penetration %", "h6_html": " 3.33%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1, "chart_id": "", "dataset_label": "Menu Penetration Percentage", "total_items_found": 9}, "error": null}, "timestamp": "2025-09-19T14:58:13.327398", "success": true, "legend_controlled": true, "drilldown_url": "https://carriageag-simt.fixedopspc.com/AnalyzeData?chartId=1317", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "sequential_processing"}]