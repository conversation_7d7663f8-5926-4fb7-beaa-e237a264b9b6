{"cookies": [{"name": "AUTH_SESSION_ID", "value": "eccd1db6-d33b-4757-b578-79d585253eb4.2c7d0f6388c3-31644", "domain": "idp-infra-bc.fixedopspc.com", "path": "/auth/realms/carriageag/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "AUTH_SESSION_ID_LEGACY", "value": "eccd1db6-d33b-4757-b578-79d585253eb4.2c7d0f6388c3-31644", "domain": "idp-infra-bc.fixedopspc.com", "path": "/auth/realms/carriageag/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "Lax"}, {"name": "KEYCLOAK_SESSION", "value": "carriageag/9ac97610-e5cb-42ae-ad2a-afc157a668b1/eccd1db6-d33b-4757-b578-79d585253eb4", "domain": "idp-infra-bc.fixedopspc.com", "path": "/auth/realms/carriageag/", "expires": 1758303656.963145, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "KEYCLOAK_SESSION_LEGACY", "value": "carriageag/9ac97610-e5cb-42ae-ad2a-afc157a668b1/eccd1db6-d33b-4757-b578-79d585253eb4", "domain": "idp-infra-bc.fixedopspc.com", "path": "/auth/realms/carriageag/", "expires": 1758303656.963655, "httpOnly": false, "secure": true, "sameSite": "Lax"}, {"name": "KEYCLOAK_IDENTITY", "value": "eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJlNWExNWVmYy1iZDk5LTRlMzQtYWQ4ZC0zZWI4YzE2MmEzYWUifQ.eyJleHAiOjE3NTgzMDM2NTYsImlhdCI6MTc1ODI2NzY1NiwianRpIjoiYWM0MmExOGYtZWU4ZS00YzY2LWFkNTEtZGFhZWZmNmVjMWIyIiwiaXNzIjoiaHR0cHM6Ly9pZHAtaW5mcmEtYmMuZml4ZWRvcHNwYy5jb20vYXV0aC9yZWFsbXMvY2FycmlhZ2VhZyIsInN1YiI6IjlhYzk3NjEwLWU1Y2ItNDJhZS1hZDJhLWFmYzE1N2E2NjhiMSIsInR5cCI6IlNlcmlhbGl6ZWQtSUQiLCJzZXNzaW9uX3N0YXRlIjoiZWNjZDFkYjYtZDMzYi00NzU3LWI1NzgtNzlkNTg1MjUzZWI0Iiwic2lkIjoiZWNjZDFkYjYtZDMzYi00NzU3LWI1NzgtNzlkNTg1MjUzZWI0Iiwic3RhdGVfY2hlY2tlciI6InZUZVNINFBvSUx1TUk0UU1abWhkcVVxdHRfNVltVlBqVHgya2loWGZGMFkifQ.367rwYxrfNJbh8d2HX9p-zENJwEPrMQXrie04I1-l7k", "domain": "idp-infra-bc.fixedopspc.com", "path": "/auth/realms/carriageag/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "KEYCLOAK_IDENTITY_LEGACY", "value": "eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJlNWExNWVmYy1iZDk5LTRlMzQtYWQ4ZC0zZWI4YzE2MmEzYWUifQ.eyJleHAiOjE3NTgzMDM2NTYsImlhdCI6MTc1ODI2NzY1NiwianRpIjoiYWM0MmExOGYtZWU4ZS00YzY2LWFkNTEtZGFhZWZmNmVjMWIyIiwiaXNzIjoiaHR0cHM6Ly9pZHAtaW5mcmEtYmMuZml4ZWRvcHNwYy5jb20vYXV0aC9yZWFsbXMvY2FycmlhZ2VhZyIsInN1YiI6IjlhYzk3NjEwLWU1Y2ItNDJhZS1hZDJhLWFmYzE1N2E2NjhiMSIsInR5cCI6IlNlcmlhbGl6ZWQtSUQiLCJzZXNzaW9uX3N0YXRlIjoiZWNjZDFkYjYtZDMzYi00NzU3LWI1NzgtNzlkNTg1MjUzZWI0Iiwic2lkIjoiZWNjZDFkYjYtZDMzYi00NzU3LWI1NzgtNzlkNTg1MjUzZWI0Iiwic3RhdGVfY2hlY2tlciI6InZUZVNINFBvSUx1TUk0UU1abWhkcVVxdHRfNVltVlBqVHgya2loWGZGMFkifQ.367rwYxrfNJbh8d2HX9p-zENJwEPrMQXrie04I1-l7k", "domain": "idp-infra-bc.fixedopspc.com", "path": "/auth/realms/carriageag/", "expires": -1, "httpOnly": true, "secure": true, "sameSite": "Lax"}, {"name": "otContext", "value": "{\"traceId\":\"32fab46325bffb82b8db4d3060bc0934\",\"spanId\":\"5ed90d1ecd121d8f\",\"traceFlags\":1}", "domain": "carriageag-simt.fixedopspc.com", "path": "/", "expires": 1789803650, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "_ga", "value": "GA1.2.1782474758.1758267651", "domain": ".fixedopspc.com", "path": "/", "expires": 1792827657.169933, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "_gid", "value": "GA1.2.1550484275.1758267651", "domain": ".fixedopspc.com", "path": "/", "expires": 1758354057, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "_gat_UA-135915218-1", "value": "1", "domain": ".fixedopspc.com", "path": "/", "expires": 1758267711, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "CLID", "value": "f6ed13017e7e40b5be1dba4f45947fda.20250919.20260919", "domain": "www.clarity.ms", "path": "/", "expires": 1789803651.140816, "httpOnly": true, "secure": true, "sameSite": "None"}, {"name": "_clck", "value": "tx47r7%5E2%5Efzg%5E0%5E2088", "domain": ".fixedopspc.com", "path": "/", "expires": 1789803651, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "SM", "value": "T", "domain": ".c.clarity.ms", "path": "/", "expires": -1, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "MUID", "value": "0544137AA1206CAB09670516A52062DA", "domain": ".clarity.ms", "path": "/", "expires": 1791963651.564311, "httpOnly": false, "secure": true, "sameSite": "None"}, {"name": "userIP", "value": "************", "domain": "idp-infra-bc.fixedopspc.com", "path": "/", "expires": 1789803652, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "userLocation", "value": "India-Tamil Nadu-Chennai", "domain": "idp-infra-bc.fixedopspc.com", "path": "/", "expires": 1789803652, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "finger", "value": "94c70e3b6c047d2844ffe8cc1b11d9ab", "domain": "idp-infra-bc.fixedopspc.com", "path": "/", "expires": 1789803652, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "otCtx", "value": "{\"fingerPrint\":\"94c70e3b6c047d2844ffe8cc1b11d9ab\"}", "domain": "idp-infra-bc.fixedopspc.com", "path": "/", "expires": 1789803652, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "otContext", "value": "{\"traceId\":\"0038bb2901eddc8f581366ccc143846c\",\"spanId\":\"3ab6f2c472ed5e8c\",\"traceFlags\":1}", "domain": "idp-infra-bc.fixedopspc.com", "path": "/", "expires": 1789803652, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "userIP", "value": "************", "domain": "carriageag-simt.fixedopspc.com", "path": "/", "expires": 1789803654, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "userLocation", "value": "India-Tamil Nadu-Chennai", "domain": "carriageag-simt.fixedopspc.com", "path": "/", "expires": 1789803654, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "_clsk", "value": "1awstn4%5E1758267655301%5E1%5E1%5Ei.clarity.ms%2Fcollect", "domain": ".fixedopspc.com", "path": "/", "expires": 1758354055, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "_ga_STEFKD044P", "value": "GS2.2.s1758267651$o1$g1$t1758267657$j54$l0$h0", "domain": ".fixedopspc.com", "path": "/", "expires": 1792827657.261541, "httpOnly": false, "secure": false, "sameSite": "Lax"}], "origins": [{"origin": "https://carriageag-simt.fixedopspc.com", "localStorage": [{"name": "13Months", "value": "2024-08-01,2025-08-31"}, {"name": "3Years", "value": "2022-09-01,2025-08-31"}, {"name": "storeGroup", "value": "Carriage Automotive Group"}, {"name": "versionFlag", "value": "TRUE"}, {"name": "kpiDataStatus", "value": "0"}, {"name": "allPermittedStores", "value": "[\"60545461_store3\",\"244284397\"]"}, {"name": "chart-master", "value": "[{\"chartId\":753,\"chartName\":\"CP Job Count-All Categories\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all jobs on a monthly basis for all Op Categories.\",\"hasGoal\":\"0\",\"id\":370,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count---all-categories\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":766,\"chartName\":\"Delta - Sold Hours & Flat Rate Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the difference between sold hours and flat rate hours\",\"hasGoal\":null,\"id\":371,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":766,\"slug\":\"delta---sold-hours-and-flat-rate-hours\",\"sort\":null,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_cp_overview.vw_charts_delta_sold_actual_flat_difference\",\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":924,\"chartName\":\"CP Total Pricing Opportunity\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows how much additional total price could have been earned if the monthly target was met. \",\"hasGoal\":\"0\",\"id\":372,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_total_pricing_opportunity\",\"parentId\":924,\"slug\":\"cp-total-pricing-opportunity\",\"sort\":2,\"dbdName\":\"ELR Opportunity\",\"viewDetails\":\"dbd_cp_parts.vw_parts_profit_by_year\",\"dbdId\":21,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":925,\"chartName\":\"CP RO Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all repair orders on a monthly basis.\",\"hasGoal\":\"0\",\"id\":373,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-ro-count\",\"sort\":6,\"dbdName\":\"\",\"viewDetails\":\"dbd_cp_overview.ux_repair_order_count\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1099,\"chartName\":\"CP Add On Job Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the add on job counts for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":378,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-on-job-count\",\"sort\":1,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_revenues\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"\\\",\\\"calculation\\\":[]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":932,\"chartName\":\"CP Labor Gross Opportunity\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows how much additional gross profit you could have earned on labor if the monthly target has been met.\",\"hasGoal\":\"1\",\"id\":380,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_labor_gross_opportunity\",\"parentId\":null,\"slug\":\"cp-labor-gross-opportunity\",\"sort\":3,\"dbdName\":null,\"viewDetails\":\"dbd_cp_opportunity_labor.vw_charts_labor_gross_opportunity\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":927,\"chartName\":\"CP Parts Volume Opportunity\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows how much additional we can earn when we increase the Hours/RO (volume) of parts.Inputs : -Revenue with Cost for Parts with Labor1\",\"hasGoal\":\"1\",\"id\":381,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_parts_volume_opportunity\",\"parentId\":null,\"slug\":\"cp-parts-volume-opportunity\",\"sort\":2,\"dbdName\":null,\"viewDetails\":\"dbd_cp_opportunity_parts.vw_charts_parts_volume_opportunity\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":940,\"chartName\":\"CP Gross Profit Percentage\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This Graph shows the Gross Profit Percentage of Customer Pay for the Parts, Labor and Combined\",\"hasGoal\":\"0\",\"id\":382,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-gross-profit-percentage\",\"sort\":3,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_gross_profit_percentage_all\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Gross Profit % and Parts Gross Profit % grouped by month.\\\",\\\"calculation\\\":[\\\"Labor Gross Profit Percentage = ((Labor sale - Labor cost)/ Labor Sale)*100\\\",\\\"Parts Gross Profit Percentage = ((Parts Sale - Parts Cost)/ Parts Sale)*100\\\",\\\"Combined Gross Profit Percentage = (((Parts Sale+Labor sale) - (Parts Cost+Labor cost))/ (Parts Sale+Labor sale))*100\\\",\\\"All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":937,\"chartName\":\"CP Effective Labor Rate - All Categories\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the average hourly price charged on all repair orders.\",\"hasGoal\":null,\"id\":383,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":946,\"slug\":\"cp-effective-labor-rate-all-categories\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":941,\"chartName\":\"CP Labor Revenue\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":384,\"matViewName\":\"\",\"parentId\":942,\"slug\":\"cp-labor-revenue\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.ux_revenue\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1028,\"chartName\":\"Parts Gross Profit - Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"\",\"hasGoal\":\"0\",\"id\":385,\"matViewName\":\"\",\"parentId\":952,\"slug\":\"parts-profit-combined\",\"sort\":1,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_profit_by_category\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":949,\"chartName\":\"CP Labor Hours Per RO - All Categories\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO grouped by month.\",\"hasGoal\":\"0\",\"id\":386,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1012,\"chartName\":\"CP Parts Revenue Per RO - All Categories\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This\",\"hasGoal\":\"0\",\"id\":387,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-all-categories\",\"sort\":1,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1022,\"chartName\":\"CP Revenue By Shop Supplies\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":412,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-revenue-by-shop-supplies\",\"sort\":5,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1086,\"chartName\":\"CP Average Labor Sale Per RO - Maintenance\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":432,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-maintenance\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":934,\"chartName\":\"CP Labor Joint Opportunitys1234566789012345678900988777666655555555555555555555555\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows how much additional gross profit you could have earned on labor if the monthly target has been met.\",\"hasGoal\":\"1\",\"id\":375,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_labor_joint_opportunity\",\"parentId\":null,\"slug\":\"cp-labor-joint-opportunity\",\"sort\":null,\"dbdName\":\"\",\"viewDetails\":\"dbd_cp_opportunity_labor.vw_charts_labor_joint_opportunity\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":930,\"chartName\":\"CP Parts to Labor Ratio\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"Parts-to-Labor ratio is a comparison between parts sales to labor sales over a certain period.\",\"hasGoal\":\"0\",\"id\":388,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_parts_to_labor_ratio\",\"parentId\":null,\"slug\":\"cp-parts-to-labor-ratio\",\"sort\":6,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_special_metrics.vw_parts_to_labor_ratio\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"Customer Pay ROs Only.A value of P$2.40 (for example) means that for every dollar of labor sold (L$1.00) a total P$2.40 of parts were also sold.\\\",\\\"calculation\\\":[\\\"Parts To Labor Ratio = Parts Extended Sale/ Labor Sale\\\",\\\"All the opcode categories - Repair, Maintenance, Competitive  are considered in these graphs.\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":955,\"chartName\":\"Average Labor Sale Per RO\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":389,\"matViewName\":\"\",\"parentId\":null,\"slug\":\"cp-average-labor-sale-per-ro\",\"sort\":9,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_average_rate_by_category_by_year\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\" The main graph shows the Average Labor Sale per Customer Pay Repair Order on a 3 year trend grouped by month.The detailed view shows the Average Labor Sale per Repair Order for each of the Pay Types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Average Labor Sale Per RO = Sum of Labor Sale / Count of ROs for each Pay Type\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1013,\"chartName\":\"CP Parts Revenue Per RO By Competitive\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":390,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-by-competitive\",\"sort\":4,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1015,\"chartName\":\"CP Parts Revenue Per RO  by Repair\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":391,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-by-repair\",\"sort\":2,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1029,\"chartName\":\"Parts Gross Profit - Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":392,\"matViewName\":\"\",\"parentId\":952,\"slug\":\"parts-gross-profit-warranty\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_detail_parts_profit_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1034,\"chartName\":\"RO Count - Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":393,\"matViewName\":\"\",\"parentId\":925,\"slug\":\"ro-count-warranty\",\"sort\":3,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_repair_order_count\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1266,\"chartName\":\"Actual Tech Efficiency\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the actual efficiency of all technicians for all customer pay repair orders on a 2 month comparison.\",\"hasGoal\":null,\"id\":394,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":3,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"divya.kumar\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1014,\"chartName\":\"CP Parts Revenue Per RO By Maintenance\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"\",\"hasGoal\":\"0\",\"id\":395,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-by-maintenance\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_detail_parts_revenue_by_category\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":939,\"chartName\":\"CP Gross Profit\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This Chart Shows the Customer Pay Gross Profit for Parts, Labor  and Combined.\",\"hasGoal\":\"0\",\"id\":396,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-gross-profit\",\"sort\":2,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_gross_profit\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the gross profit for Labor and Parts grouped by month for Customer Pay Repair Orders only.\\\",\\\"calculation\\\":[\\\"Labor Gross Profit   = Labor Sale - Labor cost\\\",\\\"Parts Gross Profit   =  Parts Extended Sale - Parts Extended Cost\\\",\\\"Gross Profit Combined = (Labor Sale  + Parts Extended Sale) - (Labor Cost + Parts Extended Cost)\\\",\\\"All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":956,\"chartName\":\"CP Labor Tech Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the actual hours worked by the technicians grouped by month.\",\"hasGoal\":\"0\",\"id\":397,\"matViewName\":\"\",\"parentId\":null,\"slug\":\"cp-labor-tech-hours\",\"sort\":10,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_actual_hours_by_year\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":957,\"chartName\":\"Delta -Labor Tech Hours and Flat Rate Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the difference between labor tech hours and flat rate Hours\",\"hasGoal\":null,\"id\":398,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":957,\"slug\":\"delta---labor-tech-hours-and-flat-rate-hours\",\"sort\":null,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"vw_drill_down_technician_hrs\",\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1008,\"chartName\":\"Labor Gross Profit - Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"\",\"hasGoal\":\"0\",\"id\":399,\"matViewName\":\"\",\"parentId\":944,\"slug\":\"labor-gross-profit-warranty\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_profit_combined\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":987,\"chartName\":\"CP Effective Labor Rate - Maintenance\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":403,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"cp-effective-labor-rate-by-maintenance\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1001,\"chartName\":\"Parts Revenue - Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":404,\"matViewName\":\"\",\"parentId\":1049,\"slug\":\"parts-revenue-combined\",\"sort\":1,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1002,\"chartName\":\"Parts Revenue - Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":405,\"matViewName\":\"\",\"parentId\":1049,\"slug\":\"parts-revenue-customer-pay\",\"sort\":2,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1010,\"chartName\":\"Labor Gross Profit - Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":406,\"matViewName\":\"\",\"parentId\":944,\"slug\":\"labor-gross-profit-customer-pay\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_profit_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":968,\"chartName\":\"Labor Sold Hours Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":407,\"matViewName\":\"\",\"parentId\":920,\"slug\":\"labor-sold-hours-combined\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_labor_sold_hours\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":969,\"chartName\":\"Labor Sold Hours Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":408,\"matViewName\":\"\",\"parentId\":920,\"slug\":\"labor-sold-hours-warranty\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_sold_hours_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":986,\"chartName\":\"CP Effective Labor Rate - Competitive\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":409,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"cp-effective-labor-rate-by-competitive\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":988,\"chartName\":\"CP Effective Labor Rate - Repair\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":410,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"cp-effective-labor-rate-by-repair\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1020,\"chartName\":\"CP Revenue By Repair\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":411,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-revenue-by-repair\",\"sort\":2,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":967,\"chartName\":\"CP Labor Gross Profit Percentage\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor gross profit percentage grouped by month.\",\"hasGoal\":\"0\",\"id\":400,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_labor_gross_profit_percentage\",\"parentId\":940,\"slug\":\"cp-labor-gross-profit-percentage\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.ux_gross_profit_percentage_all\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1313,\"chartName\":\"Gross Profit %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the parts gross profit percentage for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":413,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":5,\"dbdName\":\"Parts Workmix-comparison\",\"viewDetails\":null,\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Gross Profit  as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Parts Gross Profit % = CP Parts Sale - CP Parts Cost grouped by Op Category / CP Parts Sale grouped by Op Category \\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1143,\"chartName\":\"RO Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all repair orders on a 3 year trend.\",\"hasGoal\":\"0\",\"id\":414,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-ro-count\",\"sort\":6,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_repair_order_count_combined\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":[\"Parts Workmix\"],\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows Total Count of Repair Orders grouped by month.This view shows Repair Orders that are paid with Customer Pay only.In the detailed view, the graphs are categorized into 7 based on the type of the payment.Repair Order Count - Combined: Repair Orders that are paid with any of the  means of Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract.Repair Order Count - Customer Pay: Repair Orders with Customer Pay.Repair Order Count - Warranty: Repair Orders that are covered under Warranty.Repair Order Count - Internal: Repair Orders with Internal Pay.Repair Order Count - Maintenance Plan: Repair Orders that are covered under Maintenance Plan.Repair Order Count - Factory Service Contract: Repair Orders that are covered under Factory Service Contract.Repair Order Count - Extended Service Contract: Repair Orders that are covered under Extended Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":[\\\"Repair Order Count: count of all Repair Orders grouped by month.\\\",\\\" All the opcode categories - Repair, Maintenance, Competitive are considered for these graphs\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1031,\"chartName\":\"Parts Gross Profit - Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"notes\",\"hasGoal\":\"0\",\"id\":415,\"matViewName\":\"\",\"parentId\":952,\"slug\":\"parts-profit-customer-pay\",\"sort\":2,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_profit_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1033,\"chartName\":\"RO Count - Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This\",\"hasGoal\":\"0\",\"id\":416,\"matViewName\":\"\",\"parentId\":925,\"slug\":\"ro-count-customer-pay\",\"sort\":2,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_repair_order_count\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1307,\"chartName\":\"Effective Labor Rate\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the effective labor rate for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":null,\"id\":417,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":9,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":25,\"updatedAt\":null,\"updatedBy\":\"divya.kumar\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the ELR for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month. ,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Effective Labor Rate = Sum of Labor Sale/Sum of Labor Sold Hours grouped by Service Advisor, Op Category and month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1085,\"chartName\":\"CP Average Labor Sale Per RO - Repair\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":418,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-repair\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1067,\"chartName\":\"CP Parts Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the monthly total revenue incurred from the sale of parts.\",\"hasGoal\":\"0\",\"id\":419,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_parts_revenue\",\"parentId\":942,\"slug\":\"cp-parts-revenue\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_overview.ux_revenue\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1069,\"chartName\":\"CP Combined Gross Profit\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This Chart Shows the Customer Pay Gross Profit for Labor and Parts combined.\",\"hasGoal\":\"0\",\"id\":420,\"matViewName\":null,\"parentId\":939,\"slug\":\"cp-combined-gross-profit\",\"sort\":1,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_gross_profit\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1072,\"chartName\":\"CP Combined Gross Profit Percentage\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This Graph shows the Gross Profit Percentage of Customer Pay for the Parts, Labor and Combined\",\"hasGoal\":\"0\",\"id\":421,\"matViewName\":null,\"parentId\":940,\"slug\":\"cp-gross-profit-percentage\",\"sort\":1,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_gross_profit_percentage_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1074,\"chartName\":\"RO Count - Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":422,\"matViewName\":\"\",\"parentId\":925,\"slug\":\"ro-count-combined\",\"sort\":1,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_repair_order_count\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1076,\"chartName\":\"CP Labor Hours Per RO - Repair\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO for op catgeory REPAIR grouped by month .\",\"hasGoal\":\"0\",\"id\":423,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1077,\"chartName\":\"CP Labor Hours Per RO - Maintenance\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO for op catgeory Maintenance grouped by month.\",\"hasGoal\":\"0\",\"id\":424,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1078,\"chartName\":\"CP Labor Hours Per RO - Competitive\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO for op catgeory COMPETITIVE grouped by month.\",\"hasGoal\":\"0\",\"id\":425,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1287,\"chartName\":\"Total Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the total revenue incurred from labor and parts for all service advisors for Customer Pay on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":426,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":1,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":\"armatus.fopc.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total of Labor and Parts Revenue for all service advisors on a 2 month comparison. Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Total Revenue = Sum of Labor Sale + Sum of Parts Sale  grouped by Service Advisor, month\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1030,\"chartName\":\"Parts Gross Profit - Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":427,\"matViewName\":\"\",\"parentId\":952,\"slug\":\"parts-gross-profit-internal\",\"sort\":4,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_profit_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1079,\"chartName\":\"CP Labor Hours Per RO Percentage By Category\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO percentage  for all op categories grouped by month.\",\"hasGoal\":\"0\",\"id\":428,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":5,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1082,\"chartName\":\"CP Job Count - Competitive\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"THIS\",\"hasGoal\":\"0\",\"id\":429,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count-competitive\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1084,\"chartName\":\"CP Average Labor Sale Per RO - All Categories\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":430,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-all-categories\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1267,\"chartName\":\"Estimated Technician Efficiency\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the estimated efficiency of technicians for all customer pay repair orders on a 2 month comparison.\",\"hasGoal\":null,\"id\":431,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":4,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1087,\"chartName\":\"CP Average Labor Sale Per RO - Competitive\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":433,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-competitive\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1088,\"chartName\":\"CP Average Labor Sale Per RO - Shop Supplies\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":434,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-shop-supplies\",\"sort\":5,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1089,\"chartName\":\"CP Moving ELR - Repair and Competitive\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the average hourly price charged on all repair orders.\",\"hasGoal\":null,\"id\":435,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":1089,\"slug\":\"cp-moving-elr-repair-and-competitive\",\"sort\":null,\"dbdName\":\"Labor Rates\",\"viewDetails\":\"dbd_cp_labor_rates.vw_elr_moving_average\",\"dbdId\":5,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1035,\"chartName\":\"RO Count - Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":436,\"matViewName\":\"\",\"parentId\":925,\"slug\":\"ro-count-internal\",\"sort\":4,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_repair_order_count\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1091,\"chartName\":\"CP Parts Markup - All Categories\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":437,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-all-categories\",\"sort\":1,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_parts_markup_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1092,\"chartName\":\"CP Parts Markup - Repair\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":438,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-repair\",\"sort\":2,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_markup_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1093,\"chartName\":\"CP Parts Markup - Maintenance\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":439,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-maintenance\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_markup_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1094,\"chartName\":\"CP Parts Markup - Competitive\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":440,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-competitive\",\"sort\":4,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_markup_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1095,\"chartName\":\"CP Moving Parts Markup - Repair & Competitive\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":441,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":null,\"slug\":\"cp-moving-parts-markup-repair-and-competitive\",\"sort\":null,\"dbdName\":\"Parts Markup\",\"viewDetails\":\"dbd_cp_parts_markup.vw_parts_moving_markups\",\"dbdId\":2,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1096,\"chartName\":\"CP Parts Markup (vs) Parts Cost\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":442,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":1096,\"slug\":\"cp-moving-parts-markup-repair-and-competitive\",\"sort\":null,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_parts_itemization.vw_charts_parts_markup_cost_itemization\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Markup vs Parts Cost for the past 6 months/90 Days/30 Days as a scatter plot for Customer Pay Repair Orders with Op Categories - Repair , Competitive and Maintenance.\\\",\\\"calculation\\\":[\\\"Plot the Markup against the Parts Cost for every Customer Pay Job.\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1097,\"chartName\":\"CP Parts Gross Profit Percentage\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the parts gross profit percentage grouped by month \",\"hasGoal\":\"0\",\"id\":443,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_parts_gross_profit_percentage\",\"parentId\":940,\"slug\":\"cp-parts-gross-profit-percentage\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_overview.ux_gross_profit_percentage_all\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1070,\"chartName\":\"CP Labor Gross Profit\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":444,\"matViewName\":\"\",\"parentId\":939,\"slug\":\"cp-labor-gross-profit\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.ux_gross_profit\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1100,\"chartName\":\"CP Add On Job Counts vs Non Add On Job Counts\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the job count for add on jobs vs non-add on jobs for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":445,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-on-job-counts-vs-non-add-on-job-counts\",\"sort\":2,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_revenues\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1101,\"chartName\":\"CP Add On Job Counts per RO\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the job count for add on jobs per Repair Order for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":446,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-on-job-counts-per-ro\",\"sort\":3,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_rocount\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1102,\"chartName\":\"CP  % ROs with Add On Jobs\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the percentage of Repair orders that contain add on jobs for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":447,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-percentage-ros-with-add-on-jobs\",\"sort\":4,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_rocount\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1071,\"chartName\":\"CP Parts Gross Profit\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":448,\"matViewName\":\"\",\"parentId\":939,\"slug\":\"cp-parts-gross-profit\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_overview.ux_gross_profit\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1075,\"chartName\":\"CP Labor Revenue - All Categories\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":449,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-labor-revenue-all-categories\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1104,\"chartName\":\"CP RO Count - Add Ons vs Non Add Ons\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the repair order counts for ROs that contain add on jobs vs ROs that do not  for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":450,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-ro-count-add-ons-vs-non-add-ons\",\"sort\":6,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_rocount\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1105,\"chartName\":\"CP Total Add On Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the total add on revenues for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":451,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-total-add-on-revenue\",\"sort\":7,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_revenues\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1106,\"chartName\":\"CP Add On Labor Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the total add on labor revenues for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":452,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-on-labor-revenue\",\"sort\":8,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_revenues\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1107,\"chartName\":\"CP Add On Parts Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the total add on parts revenues for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":453,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-on-parts-revenue\",\"sort\":9,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_revenues\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1108,\"chartName\":\"CP Labor Sold Hours - Add Ons vs Non Add Ons\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the labor sold hours for add on ROs vs Non Add On ROs for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":454,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-labor-sold_hours_add_ons_vs_non_add_ons\",\"sort\":10,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_revenues\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1109,\"chartName\":\"CP Add On Labor Revenue by Op Category\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the total add on Labor revenues for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":455,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-on-labor-revenue-by-opcategory\",\"sort\":11,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_opcategory\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1110,\"chartName\":\"CP Add On Parts Revenue by Op Category\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the total add on parts revenues for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":456,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-on-parts-revenue-by-opcategory\",\"sort\":12,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_opcategory\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1112,\"chartName\":\"CP Discount Percentage By Description\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the percentages of discounts by the discount descriptions for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":457,\"matViewName\":null,\"parentId\":1112,\"slug\":\"cp-discount-percentage-by-description\",\"sort\":null,\"dbdName\":\"Discounts\",\"viewDetails\":null,\"dbdId\":6,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1113,\"chartName\":\"CP RO Count for Disc by Disc Level\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the RO Counts that contain Line level /RO Level/LOP Level Discounts for Customer Pay ROs over 13 months\",\"hasGoal\":\"0\",\"id\":458,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-ro-count-for-discounts-by-discount-level\",\"sort\":2,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_discounts_by_discountlevel\",\"dbdId\":6,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Discounted RO Counts that have discounts applied either at the Repair Order level or Job Level. Customer Pay ROs only.\\\",\\\"calculation\\\":[\\\"Discounted RO Counts at RO Level  = Distinct RO Count where Discount Level = RO\\\",\\\"Discounted RO Counts at Line Level = Distinct RO Count where Discount Level = LINE\\\",\\\"Discounted RO Counts at LOP Level = Distinct RO Count where Discount Level = LOP\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1115,\"chartName\":\"CP % Disc of Total $ Sold\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the % of Labor and Parts Sale Discounted over a 13 month trend for all Customer Pay ROs.\",\"hasGoal\":\"0\",\"id\":459,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-discounted-sale-percentage\",\"sort\":4,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_discounted_sale_percentage\",\"dbdId\":6,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the % of Labor and Parts Sale Discounted over a 13 month trend for all Customer Pay ROs.\\\",\\\"calculation\\\":[\\\"% of Labor $ discounted = (Total Labor Discount for any given month *100/ Total CP Labor Sale for the given month) \\\",\\\"% of Parts $ discounted = (Total Parts Discount for any given month *100/ Total CP Parts Sale for the given month)\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1116,\"chartName\":\"CP Add Ons - Effective Labor Rate\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the effective labor rate for Customer Pay ROs with Add Ons over 13 months\",\"hasGoal\":\"0\",\"id\":460,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-ons-effective-labor-rate\",\"sort\":13,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_opcategory\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1117,\"chartName\":\"CP Add Ons - Parts Markup\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the parts markup for Customer Pay ROs with Add Ons over 13 months\",\"hasGoal\":\"0\",\"id\":461,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-ons-parts-markup\",\"sort\":14,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_opcategory\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1118,\"chartName\":\"CP Add On Job Counts by Service Advisor\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the add on job counts grouped by service advisor for Customer Pay ROs with Add Ons over a two month comparison\",\"hasGoal\":\"0\",\"id\":462,\"matViewName\":null,\"parentId\":1118,\"slug\":\"cp-add-on-job-counts-by-service-advisor\",\"sort\":3,\"dbdName\":\"AddOns-Month Comparison\",\"viewDetails\":\"dbd_addons_vw_charts_addons_all_serviceadvisors\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1119,\"chartName\":\"CP Add On Labor Revenues by Service Advisor\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the add on labor revenues grouped by service advisor for Customer Pay ROs with Add Ons over a two month comparison\",\"hasGoal\":\"0\",\"id\":463,\"matViewName\":null,\"parentId\":1119,\"slug\":\"cp-add-on-labor-revenues-by-service-advisor\",\"sort\":4,\"dbdName\":\"AddOns-Month Comparison\",\"viewDetails\":\"dbd_addons_vw_charts_addons_all_serviceadvisors\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1120,\"chartName\":\"CP Add On Parts Revenues by Service Advisor\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the add on parts revenues grouped by service advisor for Customer Pay ROs with Add Ons over a two month comparison\",\"hasGoal\":\"0\",\"id\":464,\"matViewName\":null,\"parentId\":1120,\"slug\":\"cp-add-on-parts-revenues-by-service-advisor\",\"sort\":5,\"dbdName\":\"AddOns-Month Comparison\",\"viewDetails\":\"dbd_addons_vw_charts_addons_all_serviceadvisors\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1121,\"chartName\":\"CP Add On vs Non Add On Revenue Percentage by Service Advisor\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the add on vs non add on revenue perventages grouped by service advisor for Customer Pay ROs with Add Ons over a two month comparison\",\"hasGoal\":\"0\",\"id\":465,\"matViewName\":null,\"parentId\":1121,\"slug\":\"cp-add-on-vs-non-add-on-revenue-percentage-by-service-advisor\",\"sort\":1,\"dbdName\":\"AddOns-Month Comparison\",\"viewDetails\":\"dbd_addons_vw_charts_addons_all_serviceadvisors\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1080,\"chartName\":\"CP Job Count - Repair\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":466,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count-repair\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1139,\"chartName\":\"RO Count - Combined\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":467,\"matViewName\":\"\",\"parentId\":1138,\"slug\":\"ro-count-combined\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1081,\"chartName\":\"CP Job Count - Maintenance\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":468,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count-maintenance\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1083,\"chartName\":\"CP Job Count Percentage by Category\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":469,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count-competitive\",\"sort\":6,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_repair_order_count_percentage_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1122,\"chartName\":\"CP Add On Revenues by Service Advisor\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the add on vs non add on revenues grouped by service advisor for Customer Pay ROs with Add Ons over a two month comparison\",\"hasGoal\":\"0\",\"id\":470,\"matViewName\":null,\"parentId\":1122,\"slug\":\"cp-add-on-revenues-by-service-advisor\",\"sort\":2,\"dbdName\":\"AddOns-Month Comparison\",\"viewDetails\":\"dbd_addons_vw_charts_addons_serviceadvisors\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1199,\"chartName\":\"Labor Hours Per RO - Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO grouped by month.\",\"hasGoal\":\"0\",\"id\":471,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":8,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1227,\"chartName\":\"Parts Markup - Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":472,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-all-categories\",\"sort\":8,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_parts_markup_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1126,\"chartName\":\"Discounted Job % by Service Advisor\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the percentage of discounted jobs on the discounted Repair Orders by Service Advisor on a two month comparison. Customer Pay ROs only.\",\"hasGoal\":\"0\",\"id\":473,\"matViewName\":null,\"parentId\":1126,\"slug\":\"cp-discounted-job-percentage-by-service-advisor\",\"sort\":4,\"dbdName\":\"Discounts-Month Comparison\",\"viewDetails\":\"dbd_discounts_fn_discount_service_advisors_monthly_comparison\",\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the percentage of jobs discounted by Service Advisors comparable between two months.Customer Pay jobs only.\\\",\\\"calculation\\\":[\\\"Discounted Job % by Service Advisor = (No. of discounted jobs on the Repair Orders discounted by Service Advisor / Total No. of jobs on the Repair Orders discounted by Service Advisor)*100\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1128,\"chartName\":\"CP Effective Labor Rate - All Categories\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the average hourly price charged on all repair orders.\",\"hasGoal\":null,\"id\":474,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":1127,\"slug\":\"cp-effective-labor-rate-all-categories\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_elr_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1264,\"chartName\":\"Revenue by Technicians\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the total revenue of technicians for all customer pay repair orders on a 2 month comparison\",\"hasGoal\":null,\"id\":475,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":1,\"dbdName\":\"Technician Efficiency-Month Comparison\",\"viewDetails\":null,\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Total Revenue of technicians on a two month comparison basis. Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Revenue = Sum of Labor Sale + Sum of Parts Sale for each technician grouped by month.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1090,\"chartName\":\"CP ELR (vs) Labor Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the average hourly price charged on all repair orders.\",\"hasGoal\":null,\"id\":476,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":1090,\"slug\":\"cp-moving-elr-repair-and-competitive\",\"sort\":null,\"dbdName\":\"Labor Itemization\",\"viewDetails\":\"dbd_labor_itemization.vw_charts_labor_elr_soldhours_itemization\",\"dbdId\":9,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Effective Labor Rate vs Labor Sold Hours for the past 6 months/90 Days/30 Days as a scatter plot for Customer Pay Repair Orders with Op Categories - Repair , Competitive and Maintenance.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Plot the ELR against the Labor Sold Hours for every Customer Pay Job.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1198,\"chartName\":\"Labor Hours Per RO - Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO grouped by month.\",\"hasGoal\":\"0\",\"id\":477,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":7,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1268,\"chartName\":\"Job Count % - Flat Rate Hours With Tech Hours\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts percentage for flat rate hours with tech hours on a 2 month comparison.\",\"hasGoal\":null,\"id\":478,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":5,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1200,\"chartName\":\"Labor Hours Per RO - Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO grouped by month.\",\"hasGoal\":\"0\",\"id\":479,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":9,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1228,\"chartName\":\"Parts Markup - Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":480,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-all-categories\",\"sort\":9,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_parts_markup_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1132,\"chartName\":\"CP Effective Labor Rate - Repair And Competitive\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the Customer Pay Effective Labor Rate for Repair  for the Present, Past and Previous Years.\",\"hasGoal\":\"0\",\"id\":481,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"cp-effective-labor-rate-repair-and-competitive\",\"sort\":5,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_elr_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1163,\"chartName\":\"RO Count - Extended Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":482,\"matViewName\":\"\",\"parentId\":1143,\"slug\":\"ro-count-extended-service-contract\",\"sort\":6,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_repair_order_count_combined\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1129,\"chartName\":\"CP Effective Labor Rate - Repair\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":483,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"cp-effective-labor-rate-by-repair\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_elr_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1130,\"chartName\":\"CP Effective Labor Rate - Maintenance\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":484,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"cp-effective-labor-rate-by-maintenance\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_elr_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1131,\"chartName\":\"CP Effective Labor Rate - Competitive\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":485,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"cp-effective-labor-rate-by-competitive\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_elr_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1166,\"chartName\":\"Labor Sold Hours - Factory Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"\",\"hasGoal\":\"0\",\"id\":486,\"matViewName\":\"\",\"parentId\":920,\"slug\":\"labor-sold-hours-warranty-factory-service-contract\",\"sort\":7,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_sold_hours_combined\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1142,\"chartName\":\"RO Count - Internal\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":487,\"matViewName\":\"\",\"parentId\":1138,\"slug\":\"ro-count-internal\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":929,\"chartName\":\"CP Parts Gross Opportunity\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows  the additional gross profit you could have earned on parts if the monthly target had been met.Inputs :- Revenue with Cost for Parts with Labor\",\"hasGoal\":\"1\",\"id\":488,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_parts_gross_opportunity\",\"parentId\":null,\"slug\":\"cp-parts-gross-opportunity\",\"sort\":3,\"dbdName\":null,\"viewDetails\":\"dbd_cp_opportunity_parts.vw_charts_parts_gross_opportunity\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":923,\"chartName\":\"CP 1-Line-RO Count Percentage\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the Single Job RO Count as a percentage of total Customer Pay Repair Orders.\",\"hasGoal\":\"0\",\"id\":489,\"matViewName\":\"\",\"parentId\":null,\"slug\":\"cp-single-job-ro-count-percentage\",\"sort\":2,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_special_metrics.vw_single_job_ro_count_percentage\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"divya.kumar\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the count of repair orders that have a single Customer Pay job as a percentage of all Customer Pay Repair Orders for a given month.\\\",\\\"calculation\\\":[\\\"Repair Orders with Jobs that have a single Customer Pay job are termed as Single Job ROs.\\\",\\\"CP 1-Line-RO Count % = (CP 1-Line-RO Count/ Total CP RO Count ) *100\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1146,\"chartName\":\"RO Count - Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"bcd\",\"hasGoal\":\"0\",\"id\":490,\"matViewName\":\"\",\"parentId\":1143,\"slug\":\"ro-count-warranty\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_repair_order_count_combined\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1150,\"chartName\":\"RO Count - Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":491,\"matViewName\":\"\",\"parentId\":925,\"slug\":\"ro-count-maintenance-plan\",\"sort\":5,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_repair_order_count\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1151,\"chartName\":\"RO Count - Extended Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":492,\"matViewName\":\"\",\"parentId\":925,\"slug\":\"ro-count-extended-service-contract\",\"sort\":6,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_repair_order_count\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1123,\"chartName\":\"CP Discounted RO %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the percentage of discounted Repair Orders on a 13 month trend. Customer Pay ROs only\",\"hasGoal\":\"0\",\"id\":493,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-discounted-ro-percentage\",\"sort\":3,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_discount_ro_percentage\",\"dbdId\":6,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the percentage of discounted ROs on a 13 month trend. Discounts are applied only to Customer Pay jobs on the Repair Orders.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Discounted RO Percentage = (Discounted Repair Order Count for any month /Total Repair Order Count for the month)*100\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1148,\"chartName\":\"Labor Sold Hours - Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"\",\"hasGoal\":\"0\",\"id\":494,\"matViewName\":\"\",\"parentId\":920,\"slug\":\"labor-sold-hours-maintenance-plan\",\"sort\":5,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_labor_sold_hours\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1149,\"chartName\":\"Labor Sold Hours - Extended Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"\",\"hasGoal\":\"0\",\"id\":495,\"matViewName\":\"\",\"parentId\":920,\"slug\":\"labor-sold-hours-extended-service-contract\",\"sort\":6,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_labor_sold_hours\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1158,\"chartName\":\"Parts Revenue - Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":496,\"matViewName\":\"\",\"parentId\":1049,\"slug\":\"parts-revenue-maintenance-plan\",\"sort\":5,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1159,\"chartName\":\"Parts Revenue - Extended Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":497,\"matViewName\":\"\",\"parentId\":1049,\"slug\":\"parts-revenue-extended-service-contract\",\"sort\":6,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1160,\"chartName\":\"Parts Gross Profit - Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"*This is a sub chart in the drill down to chart 952.\",\"hasGoal\":\"0\",\"id\":498,\"matViewName\":\"\",\"parentId\":952,\"slug\":\"parts-gross-profit-maintenance-plan\",\"sort\":5,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_profit_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1134,\"chartName\":\"Labor Sold Hours - Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":499,\"matViewName\":\"\",\"parentId\":1133,\"slug\":\"labor-sold-hours-combined\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_sold_hours_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1167,\"chartName\":\"RO Count - Factory Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":500,\"matViewName\":\"\",\"parentId\":925,\"slug\":\"ro-count-warranty-factory-service-contract\",\"sort\":7,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_repair_order_count\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1135,\"chartName\":\"Labor Sold Hours - Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":501,\"matViewName\":\"\",\"parentId\":1133,\"slug\":\"labor-sold-hours-customer-pay\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_sold_hours_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1269,\"chartName\":\"Job Count % - Flat Rate Hours With No Tech Hours\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts percentage for flat rate hours with no tech hours on a 2 month comparison.\",\"hasGoal\":null,\"id\":551,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":6,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1165,\"chartName\":\"CP Total Disc $ Avg of Disc ROs\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the discounts per repair order calculated over the total discounted Customer Pay Repair Orders.\",\"hasGoal\":\"0\",\"id\":502,\"matViewName\":null,\"parentId\":null,\"slug\":\"Discounts Per Total CP ROs\",\"sort\":7,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_discounts_per_ro\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor and Parts Discounts per total discounted Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":[\\\"Labor Discount Per RO = (Total Labor Discount for any given month / Total Discounted CP RO for the given month) \\\",\\\"Parts Discount Per RO = (Total Parts Discount for any given month / Total Discounted CP RO for the given month)\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1136,\"chartName\":\"Labor Sold Hours - Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":503,\"matViewName\":\"\",\"parentId\":1133,\"slug\":\"labor-sold-hours-warranty\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_sold_hours_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1137,\"chartName\":\"Labor Sold Hours - Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":504,\"matViewName\":\"\",\"parentId\":1133,\"slug\":\"labor-sold-hours-internal\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_sold_hours_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1183,\"chartName\":\"Effective Labor Rate - Combined\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":505,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"effective-labor-rate\",\"sort\":6,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1184,\"chartName\":\"Effective Labor Rate - Customer Pay\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":506,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"effective-labor-rate\",\"sort\":7,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1188,\"chartName\":\"Effective Labor Rate - Extended Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":507,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"effective-labor-rate\",\"sort\":11,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1189,\"chartName\":\"Effective Labor Rate - Factory Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":508,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"effective-labor-rate\",\"sort\":12,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1172,\"chartName\":\"Parts Revenue - Factory Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":509,\"matViewName\":\"\",\"parentId\":1049,\"slug\":\"parts-revenue-warranty-factory-service-contract\",\"sort\":7,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1174,\"chartName\":\"Average Hours Sold Per Technician\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the average hours sold per technician based on an 8 hour per day schedule. All pay types included.\",\"hasGoal\":\"0\",\"id\":510,\"matViewName\":\"\",\"parentId\":1174,\"slug\":\"average-hours-per-technician\",\"sort\":null,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_special_metrics.vw_average_hours_per_technician\",\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1175,\"chartName\":\"Average Sale Per Technician\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the average sale of labor and parts per technician. All pay types included.\",\"hasGoal\":\"0\",\"id\":511,\"matViewName\":\"\",\"parentId\":1175,\"slug\":\"average-sale-per-technician\",\"sort\":null,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_special_metrics.vw_average_sale_per_technician\",\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1176,\"chartName\":\"Effective Labor Rate - Combined\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":512,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"effective-labor-rate\",\"sort\":6,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1177,\"chartName\":\"Effective Labor Rate - Customer Pay\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":513,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"effective-labor-rate\",\"sort\":7,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1178,\"chartName\":\"Effective Labor Rate - Warranty\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":514,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"effective-labor-rate\",\"sort\":8,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1179,\"chartName\":\"Effective Labor Rate - Internal\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":515,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"effective-labor-rate\",\"sort\":9,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1180,\"chartName\":\"Effective Labor Rate - Maintenance Plan\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":516,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"effective-labor-rate\",\"sort\":10,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1181,\"chartName\":\"Effective Labor Rate - Extended Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":517,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"effective-labor-rate\",\"sort\":11,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1182,\"chartName\":\"Effective Labor Rate - Factory Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":518,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"effective-labor-rate\",\"sort\":12,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1185,\"chartName\":\"Effective Labor Rate - Warranty\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":519,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"effective-labor-rate\",\"sort\":8,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1186,\"chartName\":\"Effective Labor Rate - Internal\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":520,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"effective-labor-rate\",\"sort\":9,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1187,\"chartName\":\"Effective Labor Rate - Maintenance Plan\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":521,\"matViewName\":\"\",\"parentId\":1127,\"slug\":\"effective-labor-rate\",\"sort\":10,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1191,\"chartName\":\"Labor Revenue - Customer Pay\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":522,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-labor-revenue-customer-pay\",\"sort\":7,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1194,\"chartName\":\"Labor Revenue - Pre-Paid Maintenance Plans\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":523,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-labor-revenue-maintenance-plan\",\"sort\":10,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1197,\"chartName\":\"Labor Hours Per RO - Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO grouped by month.\",\"hasGoal\":\"0\",\"id\":524,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":6,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1201,\"chartName\":\"Labor Hours Per RO - Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO grouped by month.\",\"hasGoal\":\"0\",\"id\":525,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":10,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1202,\"chartName\":\"Labor Hours Per RO - Extended Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO grouped by month.\",\"hasGoal\":\"0\",\"id\":526,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":11,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1339,\"chartName\":\"Flat rate hours per days and ro\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":527,\"matViewName\":null,\"parentId\":null,\"slug\":\"Flat rate hours per days and ro\",\"sort\":5,\"dbdName\":\"KPI\",\"viewDetails\":null,\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1225,\"chartName\":\"Parts Markup - Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":552,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-all-categories\",\"sort\":6,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_parts_markup_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1226,\"chartName\":\"Parts Markup - Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":553,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-all-categories\",\"sort\":7,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_parts_markup_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1252,\"chartName\":\"Job Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts over a 13 month trend.\",\"hasGoal\":\"0\",\"id\":554,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":4,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1229,\"chartName\":\"Parts Markup - Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":555,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-all-categories\",\"sort\":10,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_parts_markup_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1340,\"chartName\":\"Average age and miles\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":556,\"matViewName\":null,\"parentId\":null,\"slug\":\"Average age and miles\",\"sort\":6,\"dbdName\":\"KPI\",\"viewDetails\":null,\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1341,\"chartName\":\"Labor GP RO\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":557,\"matViewName\":null,\"parentId\":null,\"slug\":\"Labor GP RO\",\"sort\":7,\"dbdName\":\"KPI\",\"viewDetails\":null,\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1326,\"chartName\":\"RO Count - Parts Only\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the count of repair orders with part sale on a 3 year trend.\",\"hasGoal\":null,\"id\":528,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":8,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows the Total Count of Customer Pay Repair Orders over a 3 year trend grouped by month.  In the detailed view, the graphs are categorized into 7 based on the type of the payment.,Repair Order Count - Combined: Repair Orders that are paid with any of the  means of Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract.Repair Order Count - Customer Pay: Repair Orders with Customer Pay .Repair Order Count - Warranty: Repair Orders that are covered under Warranty .Repair Order Count - Internal: Repair Orders with Internal Pay .Repair Order Count - Maintenance Plan: Repair Orders that are covered under Maintenance Plan.Repair Order Count - Factory Service Contract: Repair Orders that are covered under Factory Service Contract.Repair Order Count - Extended Service Contract: Repair Orders that are covered under Extended Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":[\\\"Repair Order Count = count of all Repair Orders grouped by month where Parts Sale not equal to zero.\\\",\\\"All the opcode categories - Repair, Maintenance, Competitive are considered for these graphs\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1203,\"chartName\":\"Labor Hours Per RO - Factory Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the labor hours sold per RO grouped by month.\",\"hasGoal\":\"0\",\"id\":529,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_hours_per_repair_order_opp_customer_pay\",\"parentId\":1044,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":12,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_hours_per_repair_order_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1204,\"chartName\":\"Job Count-Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all jobs on a monthly basis for all Op Categories.\",\"hasGoal\":\"0\",\"id\":530,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count---all-categories\",\"sort\":7,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1205,\"chartName\":\"Job Count-Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all jobs on a monthly basis for all Op Categories.\",\"hasGoal\":\"0\",\"id\":531,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count---all-categories\",\"sort\":8,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1206,\"chartName\":\"Job Count-Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all jobs on a monthly basis for all Op Categories.\",\"hasGoal\":\"0\",\"id\":532,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count---all-categories\",\"sort\":9,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1207,\"chartName\":\"Job Count-Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all jobs on a monthly basis for all Op Categories.\",\"hasGoal\":\"0\",\"id\":533,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count---all-categories\",\"sort\":10,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1208,\"chartName\":\"Job Count-Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all jobs on a monthly basis for all Op Categories.\",\"hasGoal\":\"0\",\"id\":534,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count---all-categories\",\"sort\":11,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1209,\"chartName\":\"Job Count-Extended Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all jobs on a monthly basis for all Op Categories.\",\"hasGoal\":\"0\",\"id\":535,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count---all-categories\",\"sort\":12,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1210,\"chartName\":\"Job Count-Factory Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all jobs on a monthly basis for all Op Categories.\",\"hasGoal\":\"0\",\"id\":536,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count---all-categories\",\"sort\":13,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1211,\"chartName\":\"Average Labor Sale Per RO - Combined\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":537,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-all-categories\",\"sort\":6,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1212,\"chartName\":\"Average Labor Sale Per RO - Customer Pay\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":538,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-all-categories\",\"sort\":7,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1213,\"chartName\":\"Average Labor Sale Per RO - Warranty\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":539,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-all-categories\",\"sort\":8,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1214,\"chartName\":\"Average Labor Sale Per RO - Internal\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":540,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-all-categories\",\"sort\":9,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1215,\"chartName\":\"Average Labor Sale Per RO - Maintenance Plan\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":541,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-all-categories\",\"sort\":10,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1216,\"chartName\":\"Average Labor Sale Per RO - Extended Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":542,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-all-categories\",\"sort\":11,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1217,\"chartName\":\"Average Labor Sale Per RO - Factory Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Average labor Sale Per RO grouped by month\",\"hasGoal\":\"0\",\"id\":543,\"matViewName\":\"\",\"parentId\":955,\"slug\":\"cp-average-labor-sale-per-ro-all-categories\",\"sort\":12,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_average_rate_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1218,\"chartName\":\"Parts Revenue Per RO - Combined\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This\",\"hasGoal\":\"0\",\"id\":544,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-all-categories\",\"sort\":6,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1219,\"chartName\":\"Parts Revenue Per RO - Customer Pay\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This\",\"hasGoal\":\"0\",\"id\":545,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-all-categories\",\"sort\":7,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1220,\"chartName\":\"Parts Revenue Per RO - Warranty\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This\",\"hasGoal\":\"0\",\"id\":546,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-all-categories\",\"sort\":8,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1221,\"chartName\":\"Parts Revenue Per RO - Internal\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This\",\"hasGoal\":\"0\",\"id\":547,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-all-categories\",\"sort\":9,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1222,\"chartName\":\"Parts Revenue Per RO - Maintenance Plan\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This\",\"hasGoal\":\"0\",\"id\":548,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-all-categories\",\"sort\":10,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1223,\"chartName\":\"Parts Revenue Per RO - Extended Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This\",\"hasGoal\":\"0\",\"id\":549,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-all-categories\",\"sort\":11,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1224,\"chartName\":\"Parts Revenue Per RO - Factory Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This\",\"hasGoal\":\"0\",\"id\":550,\"matViewName\":\"\",\"parentId\":953,\"slug\":\"cp-parts-revenue-per-ro-all-categories\",\"sort\":12,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_per_ro_count_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1230,\"chartName\":\"Parts Markup - Extended Service Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":558,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-all-categories\",\"sort\":11,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_parts_markup_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1231,\"chartName\":\"Parts Markup - Factory Service Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":559,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":916,\"slug\":\"cp-parts-markup-all-categories\",\"sort\":12,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"vw_parts_markup_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1140,\"chartName\":\"RO Count - Customer Pay\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":560,\"matViewName\":\"\",\"parentId\":1138,\"slug\":\"ro-count-customer-pay\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1235,\"chartName\":\"Discounted Sale % - Labor & Parts\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the % of Labor and Parts Sale Discounted over a 13 month trend for all Customer Pay ROs.\",\"hasGoal\":\"0\",\"id\":562,\"matViewName\":null,\"parentId\":1115,\"slug\":\"cp-discounted-sale-percentage\",\"sort\":1,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_discounted_sale_percentage\",\"dbdId\":6,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1237,\"chartName\":\"Discounts Per Total Discounted CP ROs\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the discounts per repair order calculated over the total discounted Customer Pay Repair Orders.\",\"hasGoal\":\"0\",\"id\":563,\"matViewName\":null,\"parentId\":1165,\"slug\":\"Discounts Per Total CP ROs\",\"sort\":1,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_discounts_per_ro\",\"dbdId\":6,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1245,\"chartName\":\"Job Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job count for the Repair, Competitive and Maintenance for customer pay grouped by month.\",\"hasGoal\":\"0\",\"id\":564,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":5,\"dbdName\":\"Labor Workmix\",\"viewDetails\":null,\"dbdId\":11,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Job Count for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Job Count = Sum of CP Jobs grouped by month and Op Category  \\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1240,\"chartName\":\"Shop Supplies Combined\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":565,\"matViewName\":null,\"parentId\":1239,\"slug\":null,\"sort\":1,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1241,\"chartName\":\"Shop Supplies Customer Pay\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":566,\"matViewName\":null,\"parentId\":1239,\"slug\":null,\"sort\":2,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1242,\"chartName\":\"Shop Supplies Internal\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":567,\"matViewName\":null,\"parentId\":1239,\"slug\":null,\"sort\":3,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1244,\"chartName\":\"Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the labor sold hours for the Repair, Competitive and Maintenance on 13 month trend.\",\"hasGoal\":\"0\",\"id\":568,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":3,\"dbdName\":\"Labor Workmix\",\"viewDetails\":null,\"dbdId\":11,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Sold Hours for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":[\\\"Labor Sold Hours = Sum of CP Labor Sold Hours grouped by month and Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1246,\"chartName\":\"Work Mix %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the workmix as a percentage for the Repair, Competitive and Maintenance for customer pay grouped by month.\",\"hasGoal\":\"0\",\"id\":569,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":6,\"dbdName\":\"Labor Workmix\",\"viewDetails\":null,\"dbdId\":11,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Workmix as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Work Mix Percentage = Sold Hours grouped by Op Category/ Total CP Sold Hours*100\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1265,\"chartName\":\"Hours Sold by Technician\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the sum of customer pay labor sold hours of technicians on a 2 month comparison.\",\"hasGoal\":null,\"id\":570,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":2,\"dbdName\":\"Technician Efficiency-Month Comparison\",\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"  The graph shows the Labor Hours Sold for technicians on a two month comparison basis. Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Hours Sold = Sum of Labor Sold Hours for each Technician grouped by month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1247,\"chartName\":\"ELR\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the effective labor rate percentage for the Repair, Competitive and Maintenance for customer pay grouped by month.\",\"hasGoal\":\"0\",\"id\":571,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":4,\"dbdName\":\"Labor Workmix\",\"viewDetails\":null,\"dbdId\":11,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Effective Labor Rate as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":[\\\"Effective Labor Rate = CP Labor Sale grouped by Op Category/CP Labor Sold hours grouped by Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1248,\"chartName\":\"Gross Profit %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the labor gross profit  percentage for the Repair, Competitive and Maintenance for customer pay grouped by month.\",\"hasGoal\":\"0\",\"id\":572,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":2,\"dbdName\":\"Labor Workmix\",\"viewDetails\":null,\"dbdId\":11,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Gross Profit  as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Gross Profit % = CP Labor Sale - CP Labor Cost grouped by Op Category / CP Labor Sale grouped by Op Category *100\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1249,\"chartName\":\"Actual Technician Efficiency\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the actual efficiency of technicians for all customer pay repair orders on a 13 month trend.\",\"hasGoal\":\"0\",\"id\":573,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":1,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1251,\"chartName\":\"Job Count %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job count percentage of customer pay repair orders over a 13 month trend.\",\"hasGoal\":\"0\",\"id\":574,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":3,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1254,\"chartName\":\"Parts Cost\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the parts cost for the Repair, Competitive and Maintenance for customer pay grouped by month.\",\"hasGoal\":\"0\",\"id\":575,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":3,\"dbdName\":\"Parts Workmix\",\"viewDetails\":null,\"dbdId\":12,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Cost for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":[\\\"Parts Cost = Sum of CP Parts Cost grouped by month and Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1342,\"chartName\":\"Parts GP RO\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":576,\"matViewName\":null,\"parentId\":null,\"slug\":\"Parts GP RO\",\"sort\":8,\"dbdName\":\"KPI\",\"viewDetails\":null,\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1343,\"chartName\":\"Total GP RO\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":577,\"matViewName\":null,\"parentId\":null,\"slug\":\"Total GP RO\",\"sort\":9,\"dbdName\":\"KPI\",\"viewDetails\":null,\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1141,\"chartName\":\"RO Count - Warranty\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":578,\"matViewName\":\"\",\"parentId\":1138,\"slug\":\"ro-count-warranty\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1144,\"chartName\":\"RO Count - Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":579,\"matViewName\":\"\",\"parentId\":1143,\"slug\":\"ro-count-combined\",\"sort\":1,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_repair_order_count_combined\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1145,\"chartName\":\"RO Count - Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":580,\"matViewName\":\"\",\"parentId\":1143,\"slug\":\"ro-count-customer-pay\",\"sort\":2,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_repair_order_count_combined\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1147,\"chartName\":\"RO Count - Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":581,\"matViewName\":\"\",\"parentId\":1143,\"slug\":\"ro-count-internal\",\"sort\":4,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_repair_order_count_combined\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1152,\"chartName\":\"Labor Gross Profit - Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":582,\"matViewName\":\"\",\"parentId\":944,\"slug\":\"labor-gross-profit-maintenance-plan\",\"sort\":5,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_profit_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1153,\"chartName\":\"Labor Gross Profit - Extended Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":583,\"matViewName\":\"\",\"parentId\":944,\"slug\":\"labor-gross-profit-extended-service-contract\",\"sort\":6,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_profit_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1255,\"chartName\":\"Job Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job count for the Repair, Competitive and Maintenance for customer pay grouped by month.\",\"hasGoal\":\"0\",\"id\":584,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":5,\"dbdName\":\"Parts Workmix\",\"viewDetails\":null,\"dbdId\":12,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Job Count for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":[\\\"Job Count = Sum of CP Jobs grouped by month and Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1257,\"chartName\":\"Parts Markup\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the parts markup for the Repair, Competitive and Maintenance for customer pay grouped by month.\",\"hasGoal\":\"0\",\"id\":585,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":4,\"dbdName\":\"Parts Workmix\",\"viewDetails\":null,\"dbdId\":12,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Markup  for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":[\\\"Parts Markup = CP Parts Sale grouped by Op Category/CP Parts Cost grouped by Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1258,\"chartName\":\"Gross Profit%\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the parts gross profit percentage for the Repair, Competitive and Maintenance for customer pay grouped by month.\",\"hasGoal\":\"0\",\"id\":586,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":2,\"dbdName\":\"Parts Workmix\",\"viewDetails\":null,\"dbdId\":12,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Gross Profit  as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\" Parts Gross Profit % = CP Parts Sale - CP Parts Cost grouped by Op Category / CP Parts Sale grouped by Op Category *100\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1259,\"chartName\":\"Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the sum of labor sold hours for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":587,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":5,\"dbdName\":\"Labor Workmix-comparison\",\"viewDetails\":null,\"dbdId\":26,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Sold Hours for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":[\\\"Labor Sold Hours = Sum of CP Labor Sold Hours grouped by month and Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1261,\"chartName\":\"Job Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job count for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":588,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":2,\"dbdName\":\"Labor Workmix-comparison\",\"viewDetails\":null,\"dbdId\":26,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Job Count for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":[\\\"Job Count = Sum of CP Jobs grouped by month and Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1262,\"chartName\":\"Effective Labor Rate\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the effective labor rate percentage for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":589,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":4,\"dbdName\":\"Labor Workmix-comparison\",\"viewDetails\":null,\"dbdId\":26,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Effective Labor Rate as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":[\\\"Effective Labor Rate = CP Labor Sale grouped by Op Category/CP Labor Sold Hours grouped by Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1263,\"chartName\":\"Gross Profit%\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the labor gross profit percentage for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":590,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":3,\"dbdName\":\"Labor Workmix-comparison\",\"viewDetails\":null,\"dbdId\":26,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Gross Profit as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":[\\\"Labor Gross Profit % = CP Labor Sale - CP Labor Cost grouped by Op Category / CP Labor Sale grouped by Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1154,\"chartName\":\"Labor Sold Hours - Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":591,\"matViewName\":\"\",\"parentId\":1133,\"slug\":\"labor-sold-hours-maintenance-plan\",\"sort\":5,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_labor_sold_hours\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1270,\"chartName\":\"Job Count % - Tech Hours With No Flat Rate Hours\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts percentage for tech hours with no flat rate hours on a 2 month comparison.\",\"hasGoal\":null,\"id\":592,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":7,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1271,\"chartName\":\"Job Count % - No Flat Rate Hours and No Tech Hours\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts percentage for no tech hours with no flat rate hours on a 2 month comparison.\",\"hasGoal\":null,\"id\":593,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":8,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1272,\"chartName\":\"Job Count - Flat Rate Hours With Tech Hours\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts  for tech hours with flat rate on a 2 month comparison.\",\"hasGoal\":null,\"id\":594,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":9,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1273,\"chartName\":\"Job Count - Flat Rate Hours With No Tech Hours\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts  for no tech hours with flat rate on a 2 month comparison.\",\"hasGoal\":null,\"id\":595,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":10,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1274,\"chartName\":\"Job Count - Tech Hours With No Flat Rate Hours\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts  for tech hours with no flat rate  on a 2 month comparison.\",\"hasGoal\":null,\"id\":596,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":11,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1275,\"chartName\":\"Job Count - No Flat Hours and No Tech Hours\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts  for no tech hours with no flat rate on a 2 month comparison.\",\"hasGoal\":null,\"id\":597,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":12,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1291,\"chartName\":\"RO Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the count of all customer pay repair orders for all service advisors on a 2 month comparison\",\"hasGoal\":\"0\",\"id\":598,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":5,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the repair order count for all service advisors on a 2 month comparison.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Repair Order Count = Distinct Count of Repair Orders  grouped by Service Advisor, month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1277,\"chartName\":\"Labor Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the monthly total revenue incurred from labor for a selected service advisor group by month.\",\"hasGoal\":\"0\",\"id\":599,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":2,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Labor Revenue for all service advisors or a selected service advisor over a 13 month trend.Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Labor Revenue = Sum of Labor Sale for the selected Service Advisor grouped by month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1344,\"chartName\":\"Work Mix\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":600,\"matViewName\":null,\"parentId\":null,\"slug\":\"Work Mix\",\"sort\":10,\"dbdName\":\"KPI\",\"viewDetails\":null,\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1155,\"chartName\":\"Labor Sold Hours - Extended Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":601,\"matViewName\":\"\",\"parentId\":1133,\"slug\":\"labor-sold-hours-extended-service-contract\",\"sort\":6,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_sold_hours_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1256,\"chartName\":\"Work Mix%\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the parts workmix percentage for the Repair, Competitive and Maintenance for customer pay grouped by month.\",\"hasGoal\":\"0\",\"id\":602,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":6,\"dbdName\":\"Parts Workmix\",\"viewDetails\":null,\"dbdId\":12,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Workmix as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Work Mix % = Parts Cost grouped by Op Category/ Total CP Parts Cost *100\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1278,\"chartName\":\"Parts Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the monthly total revenue incurred from parts for a selected service advisor group by month.\",\"hasGoal\":\"0\",\"id\":603,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":3,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Parts Revenue for all service advisors or a selected service advisor over a 13 month trend.Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Parts Revenue = Sum of Parts Sale for the selected Service Advisor grouped by month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1279,\"chartName\":\"Labor Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the sum of customer pay labor sold hours for a selected service advisor group by month.\",\"hasGoal\":\"0\",\"id\":604,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":4,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Labor Sold Hours for all service advisors or a selected service advisor over a 13 month trend.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Labor Sold Hours = Sum of Labor Sold Hours for the selected Service Advisor grouped by month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1281,\"chartName\":\"Job Count\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the job count of all customer pay repair orders for a selected service advisor group by month.\",\"hasGoal\":null,\"id\":605,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":6,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Job Count for all service advisors or a selected service advisor over a 13 month trend. ,Customer Pay Repair Orders Jobs Only.\\\",\\\"calculation\\\":[\\\"Job Count = Count of CP Jobs for the selected Service Advisor grouped by month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1282,\"chartName\":\"Labor Gross Profit\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the gross profit on labor on all repair orders received from the customer payment alone for a selected service advisor group by month.\",\"hasGoal\":null,\"id\":606,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":7,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Gross Profit for all service advisors or a selected service advisor over a 13 month trend.Customer Pay Repair Orders Jobs Only.\\\",\\\"calculation\\\":[\\\"Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost) for the selected Service Advisor grouped by month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1283,\"chartName\":\"Labor Gross Profit %\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the Labor Gross Profit Percentage grouped by month for a selected service advisor .\",\"hasGoal\":null,\"id\":607,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":null,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Gross Profit % for all service advisors or a selected service advisor over a 13 month trend.Customer Pay Repair Orders Jobs Only.\\\",\\\"calculation\\\":[\\\"Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost)*100/Sum of Labor Sale for the selected Service Advisor grouped by month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1284,\"chartName\":\"Parts Gross Profit\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the gross profit on parts on all repair orders received from the customer payment alone for a selected service advisor group by month.\",\"hasGoal\":null,\"id\":608,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":null,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Gross Profit for all service advisors or a selected service advisor over a 13 month trend., Customer Pay Repair Orders Jobs Only.\\\",\\\"calculation\\\":[\\\"Parts Gross Profit = (Sum of Parts Sale - Sum of Parts Cost) for the selected Service Advisor grouped by month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1285,\"chartName\":\"Parts Gross Profit %\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the parts Gross Profit Percentage grouped by month for a selected service advisor.\",\"hasGoal\":null,\"id\":609,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":null,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Gross Profit % for all service advisors or a selected service advisor over a 13 month trend. ,Customer Pay Repair Orders Jobs Only.\\\",\\\"calculation\\\":[\\\"Parts Gross Profit % = (Sum of Parts Sale - Sum of Parts Cost)*100/Sum of Parts Sale for the selected Service Advisor grouped by month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1286,\"chartName\":\"Effective Labor Rate\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the effective labor rate for a selected service advisor on 13 month trend.\",\"hasGoal\":null,\"id\":610,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":null,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the ELR for all service advisors or a selected service advisor over a 13 month trend. ,Customer Pay Repair Orders Jobs Only.\\\",\\\"calculation\\\":[\\\"Effective Labor Rate = Sum of Labor Sale/Sum of Labor Sold Hours for the selected Service Advisor grouped by month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1288,\"chartName\":\"Labor Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the total labor revenue for all service advisors for customer pay on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":611,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":2,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Labor Revenue for all service advisors  on a 2 month comparison.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Labor Revenue = Sum of Labor Sale  grouped by Service Advisor, month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1289,\"chartName\":\"Parts Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the total parts revenue for all service advisors for customer pay on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":612,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":3,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Parts Revenue for all service advisors on a 2 month comparison. ,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Parts Revenue = Sum of Parts Sale for the selected Service Advisor  grouped by month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1290,\"chartName\":\"Labor Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the sum of labor sold hours for all service advisors for customer pay on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":613,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":4,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Labor Sold Hours for all service advisors on a 2 month comparison. , Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Labor Sold Hours = Sum of Labor Sold Hours  grouped by Service Advisor, month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1280,\"chartName\":\"RO Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all repair orders for a selected service advisor group by month.\",\"hasGoal\":\"0\",\"id\":614,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":5,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the repair order count for all service advisors or a selected service advisor over a 13 month trend.Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Repair Order Count = Distinct Count of Repair Orders for the selected Service Advisor grouped by month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1292,\"chartName\":\"Job Count\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the job count of all customer pay repair orders for all service advisors on a 2 month comparison.\",\"hasGoal\":null,\"id\":615,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":6,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Job Count for all service advisors on a 2 month comparison.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Job Count = Count of CP Jobs grouped by Service Advisor, month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1293,\"chartName\":\"Labor Gross Profit\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the labor gross profit for all service advisors on a 2 month comparison.\",\"hasGoal\":null,\"id\":616,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":7,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Gross Profit for all service advisors on a 2 month comparison.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost)  grouped by Service Advisor, month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1294,\"chartName\":\"Labor Gross Profit %\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the labor gross profit percentage for all service advisors on a 2 month comparison.\",\"hasGoal\":null,\"id\":617,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":8,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Gross Profit % for all service advisors on a 2 month comparison.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost)*100/Sum of Labor Sale  grouped by Service Advisor, month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1295,\"chartName\":\"Parts Gross Profit\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the parts gross profit for all service advisors on a 2 month comparison.\",\"hasGoal\":null,\"id\":618,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":9,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Gross Profit for all service advisors on a 2 month comparison.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Parts Gross Profit = (Sum of Parts Sale - Sum of Parts Cost)  grouped by Service Advisor, month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1296,\"chartName\":\"Parts Gross Profit %\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the parts gross profit percentage for all service advisors on a 2 month comparison.\",\"hasGoal\":null,\"id\":619,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":10,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Gross Profit % for all service advisors on a 2 month comparison.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Parts Gross Profit % = (Sum of Parts Sale - Sum of Parts Cost)*100/Sum of Parts Sale  grouped by Service Advisor, month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1297,\"chartName\":\"Effective Labor Rate\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the effective labor rate for all service advisors for customer pay on a 2 month comparison.\",\"hasGoal\":null,\"id\":620,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":11,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the ELR for all service advisors on a 2 month comparison.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Effective Labor Rate = Sum of Labor Sale/Sum of Labor Sold Hours grouped by Service Advisor, month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1298,\"chartName\":\"Parts Markup\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the parts markup for all service advisors for customer pay on a 2 month comparison.\",\"hasGoal\":null,\"id\":621,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":12,\"dbdName\":\"Service Advisor-Month Comparison\",\"viewDetails\":null,\"dbdId\":24,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Markup for all service advisors on a 2 month comparison.,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Parts Markup = Sum of Parts Sale/Sum of Parts Cost  grouped by Service Advisor, month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1299,\"chartName\":\"Total Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the total revenue incurred from labor and parts for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":\"0\",\"id\":622,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":1,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":23,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total of Labor and Parts Revenue for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month. ,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Total Revenue = Sum of Labor Sale + Sum of Parts Sale  grouped by Service Advisor, Op Category and month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1300,\"chartName\":\"Labor Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the total labor revenue for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":\"0\",\"id\":623,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":2,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":23,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Labor Revenue for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month ,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Labor Revenue = Sum of Labor Sale grouped by Service Advisor, Op Category and month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1301,\"chartName\":\"Parts Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the total parts revenue for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":\"0\",\"id\":624,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":3,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":23,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Parts Revenue for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month. ,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Parts Revenue = Sum of Parts Sale grouped by Service Advisor, Op Category and month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1302,\"chartName\":\"Labor Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the sum of labor sold hours for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":\"0\",\"id\":625,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":4,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":23,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Labor Sold Hours for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Labor Sold Hours = Sum of Labor Sold Hours grouped by Service Advisor, Op Category and month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1303,\"chartName\":\"RO Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the count of all customer pay repair orders for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":\"0\",\"id\":626,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":5,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":23,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the repair order count for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Repair Order Count = Distinct Count of Repair Orders grouped by Service Advisor, Op Category and month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1304,\"chartName\":\"Job Count\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the job count of all customer pay repair orders for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":null,\"id\":627,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":6,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":23,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Job Count for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Job Count = Count of CP Jobs grouped by Service Advisor, Op Category and month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1305,\"chartName\":\"Labor Gross Profit\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the labor gross profit for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":null,\"id\":628,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":7,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":23,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Gross Profit for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost) grouped by Service Advisor, Op Category and month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1306,\"chartName\":\"Parts Gross Profit\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the parts gross profit for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":null,\"id\":629,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":8,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":23,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Gross Profit  for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\"Parts Gross Profit = (Sum of Parts Sale - Sum of Parts Cost) grouped by Service Advisor, Op Category and month \\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1308,\"chartName\":\"Parts Markup\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the parts markup for all service advisors for the Repair, Competitive and Maintenance for a selected month.\",\"hasGoal\":null,\"id\":630,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":10,\"dbdName\":\"Service Advisor-Opcategory By Month\",\"viewDetails\":null,\"dbdId\":23,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Markup for all service advisors for the Op Categories - Repair, Maintenance and Competitive for a selected month. ,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":[\\\" Parts Markup = Sum of Parts Sale/Sum of Parts Cost grouped by Service Advisor, Op Category and month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1309,\"chartName\":\"Parts Cost\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the parts cost for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":631,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":1,\"dbdName\":\"Parts Workmix-comparison\",\"viewDetails\":null,\"dbdId\":25,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Cost for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":[\\\" Parts Cost = Sum of CP Parts Cost grouped by month and Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1310,\"chartName\":\"Parts Work Mix %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the parts workmix percentage for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":632,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":2,\"dbdName\":\"Parts Workmix-comparison\",\"viewDetails\":null,\"dbdId\":25,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\" The graph shows the Workmix as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":[\\\"Work Mix % = Parts Cost grouped by Op Category/ Total CP Parts Cost\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1311,\"chartName\":\"Job Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job count for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":633,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":4,\"dbdName\":\"Parts Workmix-comparison\",\"viewDetails\":null,\"dbdId\":25,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Job Count for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":[\\\"Job Count = Sum of CP Jobs grouped by month and Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1312,\"chartName\":\"Parts Markup\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the parts markup for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":634,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":3,\"dbdName\":\"Parts Workmix-comparison\",\"viewDetails\":null,\"dbdId\":25,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Markup for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":[\\\"Parts Markup = CP Parts Sale grouped by Op Category/CP Parts Cost grouped by Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1315,\"chartName\":\"Parts Markup\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the parts markup for a selected service advisor on 13 month trend.\",\"hasGoal\":\"0\",\"id\":635,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":null,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Markup for all service advisors or a selected service advisor over a 13 month trend., Customer Pay Repair Jobs Only.\\\",\\\"calculation\\\":[\\\" Parts Markup = Sum of Parts Sale/Sum of Parts Cost for the selected Service Advisor grouped by month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1156,\"chartName\":\"RO Count - Maintenance Plan\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":636,\"matViewName\":\"\",\"parentId\":1138,\"slug\":\"ro-count-combined\",\"sort\":5,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_repair_order_count\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1319,\"chartName\":\"Parts Hours Per RO - Combined\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":637,\"matViewName\":null,\"parentId\":1318,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":1,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1320,\"chartName\":\"Parts Hours Per RO - Customer Pay\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":638,\"matViewName\":null,\"parentId\":1318,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":2,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1321,\"chartName\":\"Parts Hours Per RO - Warranty\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":639,\"matViewName\":null,\"parentId\":1318,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1322,\"chartName\":\"Parts Hours Per RO - Internal\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":640,\"matViewName\":null,\"parentId\":1318,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":4,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1323,\"chartName\":\"Parts Hours Per RO - Maintenance Plan\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":641,\"matViewName\":null,\"parentId\":1318,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":5,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1324,\"chartName\":\"Parts Hours Per RO - Extended Service Contract\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":642,\"matViewName\":null,\"parentId\":1318,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":6,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1325,\"chartName\":\"Parts Hours Per RO - Factory Service Contract\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":643,\"matViewName\":null,\"parentId\":1318,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":7,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1157,\"chartName\":\"RO Count - Extended Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":644,\"matViewName\":\"\",\"parentId\":1138,\"slug\":\"ro-count-combined\",\"sort\":6,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_repair_order_count\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1327,\"chartName\":\"Parts RO Count - Combined\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":645,\"matViewName\":null,\"parentId\":1326,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":1,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1328,\"chartName\":\"Parts RO Count - Customer Pay\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":646,\"matViewName\":null,\"parentId\":1326,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":2,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1329,\"chartName\":\"Parts RO Count - Warranty\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":647,\"matViewName\":null,\"parentId\":1326,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1330,\"chartName\":\"Parts RO Count - Internal\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":648,\"matViewName\":null,\"parentId\":1326,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":4,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1331,\"chartName\":\"Parts RO Count - Maintenance Plan\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":649,\"matViewName\":null,\"parentId\":1326,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":5,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1332,\"chartName\":\"Parts RO Count - Extended Service Contract\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":650,\"matViewName\":null,\"parentId\":1326,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":6,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1333,\"chartName\":\"Parts RO Count - Factory Service Contract\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":651,\"matViewName\":null,\"parentId\":1326,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":7,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1161,\"chartName\":\"Parts Gross Profit - Extended Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":652,\"matViewName\":\"\",\"parentId\":952,\"slug\":\"parts-gross-profit-extended-service-contract\",\"sort\":6,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_profit_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1162,\"chartName\":\"RO Count - Maintenance Plan\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":653,\"matViewName\":\"\",\"parentId\":1143,\"slug\":\"ro-count-maintenance-plan\",\"sort\":5,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_repair_order_count_combined\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1337,\"chartName\":\"Line RO\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":654,\"matViewName\":null,\"parentId\":null,\"slug\":\"Line RO\",\"sort\":3,\"dbdName\":\"KPI\",\"viewDetails\":\"\",\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1338,\"chartName\":\"Line RO 60K+Mi\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":655,\"matViewName\":null,\"parentId\":null,\"slug\":\"Line RO 60K+Mi\",\"sort\":4,\"dbdName\":\"KPI\",\"viewDetails\":\"\",\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1168,\"chartName\":\"Labor Gross Profit - Factory Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":656,\"matViewName\":\"\",\"parentId\":944,\"slug\":\"labor-gross-profit-warranty-factory-service-contract\",\"sort\":7,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_profit_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1169,\"chartName\":\"Labor Sold Hours - Factory Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":657,\"matViewName\":\"\",\"parentId\":1133,\"slug\":\"labor-sold-hours-warranty-factory-service-contract\",\"sort\":7,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_sold_hours_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1335,\"chartName\":\"C/W/I/Total\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":658,\"matViewName\":null,\"parentId\":null,\"slug\":\"C/W/I/Total\",\"sort\":1,\"dbdName\":\"KPI\",\"viewDetails\":\"\",\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1346,\"chartName\":\"Labor Grid\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":659,\"matViewName\":null,\"parentId\":null,\"slug\":\"Labor Grid\",\"sort\":11,\"dbdName\":\"KPI\",\"viewDetails\":null,\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1349,\"chartName\":\"Job Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph displays the Job Counts for Technicians for the selected months.\",\"hasGoal\":\"0\",\"id\":660,\"matViewName\":null,\"parentId\":null,\"slug\":\"Job Count\",\"sort\":5,\"dbdName\":\"Technician Efficiency-Month Comparison\",\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\" The graph shows the weekly Job Counts for technicians on a two month comparison basis.Customer Pay Jobs Only.\\\",\\\"calculation\\\":[\\\" Job Count = Sum of Labor Sold Hours grouped by week for selected Technician.\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1350,\"chartName\":\"Job Count %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph displays the Job Count % for Technicians for the selected months.\",\"hasGoal\":\"0\",\"id\":661,\"matViewName\":null,\"parentId\":null,\"slug\":\"Job Count %\",\"sort\":6,\"dbdName\":\"Technician Efficiency-Month Comparison\",\"viewDetails\":null,\"dbdId\":22,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the weekly Job Count% for technicians on a two month comparison basis. Customer Pay Jobs Only.\\\",\\\"calculation\\\":[\\\"Job Count %= Count of CP Jobs for Technician/Total CP Jobs\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1351,\"chartName\":\"Line RO 60k-Mi\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"Line RO 60k-Mi.\",\"hasGoal\":\"0\",\"id\":662,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":5,\"dbdName\":\"KPI\",\"viewDetails\":null,\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1354,\"chartName\":\"Multi-Line-RO Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of Repair Orders that involve multiple Customer Pay job.\",\"hasGoal\":\"0\",\"id\":663,\"matViewName\":null,\"parentId\":null,\"slug\":\"Multi-Line-RO Count\",\"sort\":3,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_special_metrics.get_multi_job_ro_count\",\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the count of Repair Orders that  have Multiple Customer Pay job.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"CP Multi-Line-RO Count = Count of Repair Orders where Count of CP Jobs > 1 \\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1111,\"chartName\":\"CP Labor and Parts Disc.\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the total Labor and Parts discounts for Customer Pay ROs over 13 months.\",\"hasGoal\":\"0\",\"id\":664,\"matViewName\":null,\"parentId\":1111,\"slug\":\"cp-labor-and-parts-discounts\",\"sort\":1,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_labor_and_parts_discount_by_month\",\"dbdId\":6,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Labor and Parts Discounts for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":[\\\"Labor Discount = Sum of Discounts applied on Labor grouped by month \\\",\\\"Parts Discount = Sum of Discounts applied on Parts grouped by month\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1164,\"chartName\":\"Total Discounts Per Total ROs\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the discounts per repair order calculated over the total Customer Pay Repair Orders.\",\"hasGoal\":\"0\",\"id\":665,\"matViewName\":null,\"parentId\":1164,\"slug\":\"Discounts Per Total CP ROs\",\"sort\":6,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_discounts_per_ro\",\"dbdId\":6,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor and Parts Discounts per total Customer Pay Repair Orders over a 13 MTH trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Discount Per RO = (Total Labor Discount for any given month / Total CP RO for the given month)\\\\n\\\",\\\"1\\\":\\\"Labor Discount Per RO = (Total Labor Discount for any given month / Total CP RO for the given month)\\\\n\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1347,\"chartName\":\"Job Count %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job count percentage of customer pay repair orders over a 13 month trend.\",\"hasGoal\":\"0\",\"id\":666,\"matViewName\":null,\"parentId\":null,\"slug\":\"Job Count %\",\"sort\":5,\"dbdName\":\"Technician Efficiency\",\"viewDetails\":null,\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the monthly Job Count% for the selected Technician over a 13 month trend. Customer Pay Jobs Only.\\\",\\\"calculation\\\":[\\\"Job Count %= Count of CP Jobs for Technician/Total CP Jobs\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1,\"chartName\":\"cliffharristest1\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"cliffharristest1\",\"hasGoal\":\"0\",\"id\":667,\"matViewName\":null,\"parentId\":null,\"slug\":\"cliffharristest1\",\"sort\":0,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1345,\"chartName\":\"Tech Productivity – CP Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the hours flagged on the selected Technician grouped by week.\",\"hasGoal\":\"0\",\"id\":668,\"matViewName\":null,\"parentId\":null,\"slug\":\"Tech Productivity – CP Sold Hours\",\"sort\":3,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the weekly hours sold for the selected Technician for all Customer Pay  Repair Orders over the last six months.\\\",\\\"calculation\\\":[\\\"CP Hours Sold = Total CP Sold Hours for selected technician grouped by week\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":456,\"chartName\":\"Parts Revenue - Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"test\",\"hasGoal\":\"0\",\"id\":669,\"matViewName\":\"\",\"parentId\":1049,\"slug\":\"parts-revenue-internal\",\"sort\":4,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":\"netspective.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":457,\"chartName\":\"Parts Revenue - Warranty\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"Test 75\",\"hasGoal\":\"0\",\"id\":670,\"matViewName\":\"\",\"parentId\":1049,\"slug\":\"parts-revenue-warranty\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_revenue_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":\"netspective.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1170,\"chartName\":\"RO Count - Factory Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":671,\"matViewName\":\"\",\"parentId\":1143,\"slug\":\"ro-count-warranty-factory-service-contract\",\"sort\":7,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_repair_order_count_combined\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1334,\"chartName\":\"CP Parts Markup - Repair and Competitive\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the Parts Markup for Customer Pay Repair Orders for Op Categories - REPAIR and COMPETITIVE only on a three year trend.\",\"hasGoal\":null,\"id\":672,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":9,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Markup as a weighted average of the Op Categories - Repair and Competitive over a 3 year trend grouped by month.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Parts Markup  = Sum of Parts Sale / Sum of Parts Cost where Op Category = Repair & Competitive\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1171,\"chartName\":\"Parts Gross Profit - Factory Service Contract\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":673,\"matViewName\":\"\",\"parentId\":952,\"slug\":\"parts-gross-profit-warranty-factory-service-contract\",\"sort\":7,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_detail_parts_profit_by_category\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1173,\"chartName\":\"RO Count - Factory Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":674,\"matViewName\":\"\",\"parentId\":1138,\"slug\":\"ro-count-warranty-factory-service-contract\",\"sort\":7,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1190,\"chartName\":\"Labor Revenue - Combined\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":675,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-labor-revenue-combined\",\"sort\":6,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":2,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1192,\"chartName\":\"Labor Revenue - Warranty\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":676,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-labor-revenue-warranty\",\"sort\":8,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":2,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1353,\"chartName\":\"Parts Grid\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":677,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":12,\"dbdName\":\"KPI\",\"viewDetails\":null,\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1253,\"chartName\":\"Parts Sale\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the parts revenue for the Repair, Competitive and Maintenance for customer pay on 13 month trend.\",\"hasGoal\":\"0\",\"id\":678,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":1,\"dbdName\":\"Parts Workmix\",\"viewDetails\":null,\"dbdId\":12,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts revenue for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":[\\\"Parts Sale = Sum of CP Parts Sale grouped by month and Op Category\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1068,\"chartName\":\"CP Revenue Combined\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":679,\"matViewName\":\"\",\"parentId\":942,\"slug\":\"cp-labor-revenue\",\"sort\":1,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_revenue\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1193,\"chartName\":\"Labor Revenue - Internal\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":680,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-labor-revenue-internal\",\"sort\":9,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":2,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1114,\"chartName\":\"CP Discounts By Service Advisor\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the total Labor and Parts discounts per Service Advisor for Customer Pay ROs for any given month\",\"hasGoal\":\"0\",\"id\":681,\"matViewName\":null,\"parentId\":1114,\"slug\":\"cp-discounts-by-service-advisor\",\"sort\":1,\"dbdName\":\"Discounts-Month Comparison\",\"viewDetails\":\"dbd_discounts_vw_discounts_by_service_advisor\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Labor and Parts discounts grouped by Service Advisor comparable over two months. Customer Pay ROs only.\\\",\\\"calculation\\\":[\\\"Discounts By Service Advisor = Total Labor Discount + Total Parts Discounts grouped by Service Advisor for each chosen month.\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1098,\"chartName\":\"ELR - Repair\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the Customer Pay Effective Labor Rate for Repair  for the Present, Past and Previous Years.\",\"hasGoal\":\"0\",\"id\":682,\"matViewName\":\"\",\"parentId\":null,\"slug\":\"cp-effective-labor-rate-repair-and-competitive\",\"sort\":6,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_ux_effective_labor_rate_by_category_not_maintenance_by_year\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":\"netspective.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Rates as a weighted average of the Op Categories - Repair  over a 3 year trend grouped by month.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Effective Labor Rate  = Sum of Labor Sale / Sum of Labor Sold Hours where Op Category = Repair .\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1195,\"chartName\":\"Labor Revenue - Extended Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":683,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-labor-revenue-extended-service-contract\",\"sort\":11,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":2,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1196,\"chartName\":\"Labor Revenue - Factory Service Contract\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":684,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-labor-revenue-factory-service-contract\",\"sort\":12,\"dbdName\":\"CP Labor\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":2,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1250,\"chartName\":\"Tech Productivity (to be redone by David S)\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":685,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":2,\"dbdName\":null,\"viewDetails\":null,\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1336,\"chartName\":\"RO Share\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":686,\"matViewName\":null,\"parentId\":null,\"slug\":\"RO Share\",\"sort\":2,\"dbdName\":\"KPI\",\"viewDetails\":\"\",\"dbdId\":27,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1124,\"chartName\":\"Discounted RO % by Service Advisor\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the percentage of discounted Repair Orders grouped by Service Advisor on a two month comparison. Customer Pay ROs only.\",\"hasGoal\":\"0\",\"id\":702,\"matViewName\":null,\"parentId\":1124,\"slug\":\"cp-discounted-ro-percentage-by-service-advisor\",\"sort\":3,\"dbdName\":\"Discounts-Month Comparison\",\"viewDetails\":\"dbd_discounts_fn_discount_service_advisors_monthly_comparison\",\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the percentage of ROs discounted by Service Advisors comparable between two months.\\\",\\\"calculation\\\":[\\\"Discounted RO % by Service Advisor = (No. of Repair Orders discounted by Service Advisor/ Total No. of Repair Orders by Service Advisor)*100\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1352,\"chartName\":\"Tech Productivity - All Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the weekly hours sold for the selected Technician for All, Customer Pay, Warranty and Internal Repair Orders over the last thirteen months.\",\"hasGoal\":\"0\",\"id\":687,\"matViewName\":null,\"parentId\":null,\"slug\":\"Tech Productivity - All Sold Hours\",\"sort\":2,\"dbdName\":\"Technician Efficiency\",\"viewDetails\":null,\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":\"armatus.fopc.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\" The graph shows the weekly hours sold for the selected Technician for All, Customer Pay, Warranty and Internal  Repair Orders over the last thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"All Hours Sold = Total Sold Hours for selected technician grouped by week\\\",\\\"1\\\":\\\"Customer Pay Hours Sold = Total Customer Pay Sold Hours for selected technician grouped by week\\\",\\\"2\\\":\\\"Warranty Hours Sold = Total Warranty Sold Hours for selected technician grouped by week\\\",\\\"3\\\":\\\"Internal Hours Sold = Total Internal Sold Hours for selected technician grouped by week.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1348,\"chartName\":\"Job Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts over a 13 month trend.\",\"hasGoal\":\"0\",\"id\":689,\"matViewName\":null,\"parentId\":null,\"slug\":\"Job Count\",\"sort\":4,\"dbdName\":\"Technician Efficiency\",\"viewDetails\":null,\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the monthly Job Counts for the selected Technician over the last 13 months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Job Count = Count of CP Jobs grouped by month for selected Technician.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1066,\"chartName\":\"CP Effective Labor Rate - Repair And Competitive\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the Customer Pay EffectiveLabor Rate for Repair for the Present, Past ans Previous Years.\",\"hasGoal\":\"0\",\"id\":690,\"matViewName\":\"\",\"parentId\":946,\"slug\":\"cp-effective-labor-rate-repair-and-competitive\",\"sort\":5,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1243,\"chartName\":\"Labor Sale\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the labor revenue for the Repair, Competitive and Maintenance for customer pay on 13 month trend.\",\"hasGoal\":\"0\",\"id\":691,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":1,\"dbdName\":\"Labor Workmix\",\"viewDetails\":null,\"dbdId\":11,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor revenue for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Sale = Sum of CP Labor Sale grouped by month and Op Category.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1363,\"chartName\":\"Tech Productivity - All ROs\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the RO count for the selected Technician over 13 months on a weekly basis. \",\"hasGoal\":\"0\",\"id\":692,\"matViewName\":null,\"parentId\":null,\"slug\":\"Tech Productivity - All ROs\",\"sort\":1,\"dbdName\":\"Technician Efficiency\",\"viewDetails\":null,\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":\"\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\" The graph shows the weekly RO count for the selected Technician for All, Customer Pay, Warranty and Internal over the last thirteen months.\\\",\\\"calculation\\\":[\\\"All RO Count = Total ROs for selected technician grouped by week\\\",\\\"Customer RO Count = Total Customer Pay ROs for selected technician grouped by week\\\",\\\"Warranty RO Count = Total Warranty ROs for selected technician grouped by week\\\",\\\"Internal RO Count = Total Internal ROs for selected technician grouped by week\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":946,\"chartName\":\"Effective Labor Rate\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the average hourly price charged on all repair orders\",\"hasGoal\":null,\"id\":693,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":null,\"slug\":\"cp-effective-labor-rate\",\"sort\":5,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_elr_all\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows the Labor Rates grouped by month for Customer Pay Repair Orders.The detailed graphs represent the Labor Rates for each of the Pay Types - Customer Pay, Warranty, Internal, Extended Service Contract and Factory Service Contract.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Effective Labor Rate = Sum of Labor Sale / Sum of Labor Sold Hours for each Pay Type.\\\",\\\"1\\\":\\\" All Op Categories - Repair, Maintenance and Competitive included.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1316,\"chartName\":\"MPI Penetration Percentage\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the mpi penetration percentage of a customer payment repair orders by month.\",\"hasGoal\":null,\"id\":694,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":9,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the MPI penetration as a percentage of total Repair orders over a 13 month trend month.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"MPI Penetration = Count of Repair Orders that have MPI Jobs on Them / Total Repair Orders Month\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":928,\"chartName\":\"CP Parts Joint Opportunity\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows how much additional gross profit you could have earned on parts if the monthly target has been met.Inputs :- Revenue with Cost for Parts with Labor\",\"hasGoal\":\"1\",\"id\":695,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_parts_joint_opportunity\",\"parentId\":null,\"slug\":\"cp-parts-joint-opportunity\",\"sort\":4,\"dbdName\":null,\"viewDetails\":\"dbd_cp_opportunity_parts.vw_charts_parts_joint_opportunity\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"armatus.fopc.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1361,\"chartName\":\"Revenue - Shop Supplies - Warranty\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":696,\"matViewName\":null,\"parentId\":1239,\"slug\":\"Revenue - Shop Supplies\",\"sort\":3,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":948,\"chartName\":\"CP 1-Line-RO Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of Repair Orders that involve only a single Customer Pay job.\",\"hasGoal\":\"0\",\"id\":697,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-single-job-ro-count\",\"sort\":1,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_special_metrics.vw_single_job_ro_count\",\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":\"priya.guptan\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the count of Repair Orders that only have single Customer Pay job.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"CP 1-Line-RO Count = Count of Repair Orders where Count of CP Jobs = 1.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1356,\"chartName\":\"CP ELR - Maintenance and Competitive\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the Customer Pay Effective Labor Rate for Maintenance and Competitive for the Present, Past and Previous Years.\",\"hasGoal\":\"0\",\"id\":698,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":7,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":null,\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Rates as a weighted average of the Op Categories - Competitive and Maintenance  over a 3 year trend grouped by month.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Effective Labor Rate  = Sum of Labor Sale / Sum of Labor Sold Hours where Op Category = Competitive and Maintenance \\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1360,\"chartName\":\"Revenue - Shop Supplies - Customer Pay\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":699,\"matViewName\":null,\"parentId\":1239,\"slug\":\"Revenue - Shop Supplies\",\"sort\":2,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1359,\"chartName\":\"Revenue - Shop Supplies - Combined\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":700,\"matViewName\":null,\"parentId\":1239,\"slug\":\"Revenue - Shop Supplies\",\"sort\":1,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1362,\"chartName\":\"Revenue - Shop Supplies - Internal\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":null,\"id\":701,\"matViewName\":null,\"parentId\":1239,\"slug\":\"Revenue - Shop Supplies\",\"sort\":4,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1236,\"chartName\":\"Discounts Per Total CP ROs\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the Labor and Parts Discounts per total Customer Pay Repair Orders over a 13 month trend.\",\"hasGoal\":\"0\",\"id\":703,\"matViewName\":null,\"parentId\":null,\"slug\":\"Discounts Per Total CP ROs\",\"sort\":6,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_discounts_per_ro\",\"dbdId\":6,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor and Parts Discounts per total Customer Pay Repair Orders over a 13 MTH trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Discount Per RO = (Total Labor Discount for any given month / Total CP RO for the given month)\\\\n\\\",\\\"1\\\":\\\"Parts Discount Per RO = (Total Parts Discount for any given month / Total CP RO for the given month)\\\\n\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1238,\"chartName\":\"Parts Markup\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":704,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":6,\"dbdName\":\"CP Overview\",\"viewDetails\":null,\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Markup for Customer Pay Repair Orders on a 3 year trend grouped by month.The detailed view shows the Markup for each of the Pay Types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Parts Markup = Sum of Parts Sale/ Sum of Parts Cost grouped by month.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":916,\"chartName\":\"Parts Markup\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows customer pay parts markup values.\",\"hasGoal\":null,\"id\":707,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":null,\"slug\":\"cp-parts-markup\",\"sort\":5,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_parts_markup_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":\"armatus.fopc.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Markup for Customer Pay Repair Orders on a 3 year trend grouped by month.The detailed view shows the Markup for each of the Pay Types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Parts Markup = Sum of Parts Sale/ Sum of Parts Cost grouped by month.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1355,\"chartName\":\"Multi-Line-RO Count Percentage\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the multi job RO count as a percentage of total Customer Pay Repair Orders\",\"hasGoal\":\"0\",\"id\":708,\"matViewName\":null,\"parentId\":null,\"slug\":\"Multi-Line-RO Count Percentage\",\"sort\":4,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_special_metrics.get_multi_job_ro_count_percentage\",\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the count of repair orders that have multiple Customer Pay job as a percentage of all Customer Pay Repair Orders for a given month.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"CP  Multi-Line-RO Count % = (CP Multi-Line-RO Count/ Total CP RO Count ) *100\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1317,\"chartName\":\"Menu Penetration Percentage\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the menu penetration percentage of a customer payment repair orders by month.\",\"hasGoal\":null,\"id\":709,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":10,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Menu Penetration as a percentage of total Customer Pay Repair orders over a 13 month trend month.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Menu Penetration = Count of Repair Orders where OpCode = Dealer Menu Opcodes * 100/ Count of Repair Orders where PayType = 'Customer Pay' month\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":926,\"chartName\":\"CP Total Parts Opportunity\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the combined view of Parts Hours Per RO opportunity , Parts Gross Profit % opportunity and Parts Joint Effect opportunity. \",\"hasGoal\":\"0\",\"id\":710,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_total_parts_opportunity\",\"parentId\":null,\"slug\":\"cp-total-parts-opportunity\",\"sort\":1,\"dbdName\":\"CP Parts Gross Volume Opportunity\",\"viewDetails\":\"dbd_cp_opportunity_parts.vw_charts_total_parts_opportunity\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Combined view of Parts Hours Per RO, Parts Gross Profit % and Parts Joint Effect Opportunity.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Parts Gross Opportunity = (Sum of Parts Cost / (1 - Target Parts GP % /100)) - Sum of Parts Sale\\\",\\\"1\\\":\\\"Parts Volume Opportunity = (Target Hrs/RO - Actual Hrs/RO) * Count of RO * Parts GP/Hr \\\",\\\"2\\\":\\\"Parts Joint Opportunity = Increase in Labor Sold Hours * Increase in Parts GP/Hr when GP % is increased to Target GP % and Hrs/RO is increased to Target Hrs/RO\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":970,\"chartName\":\"Labor Sold Hours Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"this\",\"hasGoal\":\"0\",\"id\":711,\"matViewName\":\"\",\"parentId\":920,\"slug\":\"labor-sold-hours-internal\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_labor_sold_hours\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":\"armatus.fopc.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1276,\"chartName\":\"Total Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the monthly total revenue incurred from labor and parts for a selected service advisor group by month.\",\"hasGoal\":\"0\",\"id\":712,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":1,\"dbdName\":\"Service Advisor Perfomance\",\"viewDetails\":null,\"dbdId\":15,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total of Labor and Parts Revenue for all service advisors or a selected service advisor over a 13 month trend,Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Total Revenue = Sum of Labor Sale + Sum of Parts Sale for the selected Service Advisor grouped by month \\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1239,\"chartName\":\"Revenue - Shop Supplies\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the revenue from Shop Supplies  for all Repair Orders\",\"hasGoal\":null,\"id\":713,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":8,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the revenue from Shop Supplies for all Repair Orders grouped by pay type over a trend of 13 months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Revenue - Shop Supplies = Total Shop Supplies By Pay type\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":931,\"chartName\":\"CP Total Labor Opportunity\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the Combined of Labor Hours Per RO, Labor Gross Profit % and Labor Joint Effect Opportunity.\",\"hasGoal\":null,\"id\":705,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_total_labor_opportunity\",\"parentId\":null,\"slug\":\"cp-total-labor-opportunity\",\"sort\":1,\"dbdName\":\"CP Labor Gross Volume Opportunity\",\"viewDetails\":\"dbd_cp_opportunity_labor.vw_charts_total_labor_opportunity\",\"dbdId\":17,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Combined view of Labor Hours Per RO, Labor Gross Profit % and Labor Joint Effect Opportunity.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Gross Opportunity = (Sum of Labor Cost / (1 - Target Labor GP % / 100)) - Sum of Labor Sale \\\",\\\"1\\\":\\\"Labor Volume Opportunity = (Target Hrs/RO - Actual Hrs/RO) * Count of RO * Labor GP/Hr\\\",\\\"2\\\":\\\"Labor Joint Opportunity = Increase in Labor Sold Hours * Increase in Labor GP/Hr when GP % is increased to Target GP % and Hrs/RO is increased to Target Hrs/RO  \\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":921,\"chartName\":\"CP Pricing Opportunity By Category\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows how much additional price could have been earned if the monthly target was met based on opcode category.\",\"hasGoal\":\"1\",\"id\":714,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_pricing_opportunity_by_category\",\"parentId\":null,\"slug\":\"cp-pricing-opportunity-by-category\",\"sort\":1,\"dbdName\":\"ELR Opportunity\",\"viewDetails\":null,\"dbdId\":21,\"updatedAt\":null,\"updatedBy\":\"armatus.fopc.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows how much additional Labor Sale Price per Opcode Category could have been earned if the monthly target was met.It is shown for the 3 Opcode Categories - Repair, Maintenance and Competitive. Here we consider Labor Sale Price for Customer Pay only. One monthly Target Sale rate input can be given as goal for this chart.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Total Pricing Opportunity = (Target ELR - Actual ELR) * Job Count for the month\\\",\\\"1\\\":\\\"ELR = Labor Sale / Labor Sold Hours\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1260,\"chartName\":\"Labor Work Mix %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the workmix percentage for the Repair, Competitive and Maintenance for customer pay repair orders on a 2 month comparison.\",\"hasGoal\":\"0\",\"id\":715,\"matViewName\":null,\"parentId\":null,\"slug\":null,\"sort\":1,\"dbdName\":\"Labor Workmix-comparison\",\"viewDetails\":null,\"dbdId\":26,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Workmix as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Work Mix Percentage = Sold Hours grouped by Op Category/ Total CP Sold Hours\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1125,\"chartName\":\"CP Discounted Job Count % by Opcategory by Service Advisor\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the percentage of discounted job count percentage by Op Category grouped further by Service Advisor on a two month comparison. Customer Pay ROs only.\",\"hasGoal\":\"0\",\"id\":716,\"matViewName\":null,\"parentId\":1125,\"slug\":\"cp-discounted-job-count-percentage-by-op-category-by-service-advisor\",\"sort\":2,\"dbdName\":\"Discounts-Month Comparison\",\"viewDetails\":\"dbd_discounts_vw_discount_jobcount_percentage_service_advisor_opcategory\",\"dbdId\":14,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the percentage of jobs discounted by Service Advisors for each of the Op Categories - Repair, Competitive and Maintenance comparable between two months.Customer Pay jobs only.\\\",\\\"calculation\\\":[\\\"Discounted Job Count % by Service Advisor - Repair = (No. of discounted jobs on the Repair Orders discounted by Service Advisor / Total No. of jobs on the Repair Orders discounted by Service Advisor )*100  where Op Category = REPAIR \\\",\\\" Discounted Job Count % by Service Advisor -Competitive = (No. of discounted jobs on the Repair Orders discounted by Service Advisor / Total No. of jobs on the Repair Orders discounted by Service Advisor )*100  where Op Category = COMPETITIVE \\\",\\\" Discounted Job Count % by Service Advisor - Maintenance = (No. of discounted jobs on the Repair Orders discounted by Service Advisor / Total No. of jobs on the Repair Orders discounted by Service Advisor )*100  where Op Category = MAINTENANCE\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":944,\"chartName\":\"Labor Gross Profit\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the gross profit on labor on all repair orders received from the customer payment alone in Present, Past and Previous Years.\",\"hasGoal\":\"0\",\"id\":717,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-labor-gross-profit\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_labor_profit_by_year\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows the Gross Profit from Labor for Customer Pay Repair Orders on a 3 year trend grouped by month.The detailed view shows the Gross Profit from Labor for each of the Pay Types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract.Labor Gross Profit - Combined: All Pay Types - Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract included.Labor Gross Profit - Customer Pay: Customer Pay Repair Orders only.Labor Gross Profit - Warranty: Warranty Repair Orders only.Labor Gross Profit - Internal: Internal Repair Orders only.Labor Gross Profit - Maintenance Plan: Maintenance Repair Orders only.Labor Gross Profit - Factory Service Contract: Repair Orders on the Factory Service Contract.Labor Gross Profit - Extended Service Contract: Repair Orders on the Extended Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Gross Profit = Sum of Labor Sale - Sum of Labor Cost\\\",\\\"1\\\":\\\"All the Opcode categories - Repair, Maintenance & Competitive are considered in these graphs.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":935,\"chartName\":\"Labor Sold Hours Percentage By Pay Type\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the sum of all sold hours on a monthly basis by Pay Type\",\"hasGoal\":\"0\",\"id\":718,\"matViewName\":null,\"parentId\":null,\"slug\":\"labor-sold-hours-percentage-by-paytype\",\"sort\":6,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_cp_overview.ux_detail_elr_all\",\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":\"armatus.fopc.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Labor Sold Hours for each Pay Type - Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract and Extended Service Plan as a percentage of the total Labor Sold Hours for Service Repair Orders.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Sold Hours % - Customer Pay = Total Customer Pay Labor Sold Hours *100/ Total Labor Sold Hours where Pay Type = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan\\\",\\\"1\\\":\\\"Labor Sold Hours % - Internal = Total Internal Pay Labor Sold Hours *100/ Total Labor Sold Hours where Pay Type = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan\\\",\\\"2\\\":\\\"Labor Sold Hours % - Warranty = Total Warranty Pay Labor Sold Hours *100/ Total Labor Sold Hours where Pay Type = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan \\\",\\\"3\\\":\\\"Labor Sold Hours % - Maintenance Plan = Total Maintenance Pay Labor Sold Hours *100/ Total Labor Sold Hours where Pay Type = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan \\\",\\\"4\\\":\\\"Labor Sold Hours % - Factory Service Contract = Total Factory Service Contract Labor Sold Hours *100/ Total Labor Sold Hours where Pay Type = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan \\\",\\\"5\\\":\\\"Labor Sold Hours % - Extended Service Plan = Total Extended Service Plan Pay Labor Sold Hours *100/ Total Labor Sold Hours where Pay Type = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1133,\"chartName\":\"Labor Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the sum of customer pay labor sold hours on a 3 year trend\",\"hasGoal\":\"0\",\"id\":720,\"matViewName\":\"\",\"parentId\":null,\"slug\":\"cp-labor-sold-hours\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_labor_sold_hours_by_year\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":[\"Labor Workmix\",\"Parts Workmix\"],\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows total hours sold for Labor for Customer Pay Repair Orders over a 3 year trend grouped by month. In the detailed view, the graphs are categorized into 7 based on the type of the payment.,Labor Sold Hours - Combined: All Pay Types  - Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract included.Labor Sold Hours - Customer Pay: This graph shows the Labor Sold Hours for Customer Pay Repair Orders.Labor Sold Hours - Warranty: This graph shows the Labor Sold Hours for Warranty Repair Orders.,Labor Sold Hours - Internal: This graph shows the Labor Sold Hours for Internal Repair Orders.Labor Sold Hours - Maintenance Plan: This graph shows the Labor Sold Hours for Repair Orders on the Maintenance Plan.Labor Sold Hours - Factory Service Contract: This graph shows the Labor Sold Hours for Repair Orders on the Factory Service Contract.Labor Sold Hours - Extended Service Contract: This graph shows the Labor Sold Hours for Repair Orders on the Extended Service Contract  over a period of thirteen months.\\\",\\\"calculation\\\":[\\\"Labor Sold Hours =  Sum of Hours Sold grouped by month.\\\",\\\"All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1044,\"chartName\":\"Labor Hours Per RO\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the customer pay labor hours Per RO in Present, Past and Previous Years\",\"hasGoal\":\"0\",\"id\":721,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-labor-hours-per-ro\",\"sort\":8,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_hours_per_repair_order_by_year\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows the Labor Hours sold per Customer Pay Repair Order over a 3 year trend grouped by month.The detailed view shows the Labor Hours Sold Repair Order for each of the Pay Types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Hours Per RO = Sum of Labor Sold Hours / Count of Repair Orders for each Pay Type.\\\",\\\"1\\\":\\\" \\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1357,\"chartName\":\"Average RO Open Days\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the average number of days an RO remains open for any given month over a 13 month trend by Pay Type.\",\"hasGoal\":\"1\",\"id\":722,\"matViewName\":\"\",\"parentId\":null,\"slug\":\"\",\"sort\":1,\"dbdName\":\"Special Metrics\",\"viewDetails\":null,\"dbdId\":13,\"updatedAt\":null,\"updatedBy\":\"armatus.fopc.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the average number of days an RO remains open for any given month over a 13 month trend by Pay Type.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Average RO Open Days = Sum of Open Days for all ROs/ Total no of ROs for any given month.\\\",\\\"1\\\":\\\"\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1049,\"chartName\":\"Parts Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the monthly total revenue incurred from the sale of parts in the Present, Past and Previous Years.\",\"hasGoal\":\"0\",\"id\":723,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-parts-revenue\",\"sort\":1,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_parts_revenue_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":[\"Parts Workmix\"],\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows total revenue from Parts for  Customer Pay Repair Orders over a 3 year trend grouped by month.In the detailed view, the graphs are categorized into 7 based on the type of the payment.Parts Revenue - Combined: All Patypes - Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract and Extended Service Contract included.Parts Revenue - Customer Pay: Customer Pay Repair Orders only.Parts Revenue - Warranty: Warranty Repair Orders only.Parts Revenue - Internal:  Internal Repair Orders only.Parts Revenue - Maintenance Plan: Repair Orders on the Maintenance Plan only.Parts Revenue - Factory Service Contract:Repair Orders on the Factory Service Contract only.Parts Revenue - Extended Service Contract:Repair Orders on the Extended Service Contract only over a period of thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Parts Revenue = Sum of Parts Extended Sale grouped by month.\\\",\\\"1\\\":\\\"All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":952,\"chartName\":\"Parts Gross Profit\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the gross profit on parts on all repair orders received from customer payment alone in the Present, Past and Previous Years\",\"hasGoal\":\"0\",\"id\":726,\"matViewName\":null,\"parentId\":null,\"slug\":\"customer-pay-parts-gross-profit\",\"sort\":2,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_parts_profit_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows Gross Profit from Parts sold for Customer Pay Repair Orders on a 3 year trend grouped by month.In the detailed view, the graphs are categorized into 7 based on the type of the payment.Parts Gross Profit - Combined: All Pay Types - Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract included.Parts Gross Profit - Customer Pay: Customer Pay Repair Orders only.Parts Gross Profit - Warranty: Warranty Repair Orders only.Parts Gross Profit - Internal: Internal Pay Repair Orders only.Parts Gross Profit - Maintenance Plan: Repair Orders on the Maintenance Plan.Parts Gross Profit - Factory Service Contract: Repair Orders on the Factory Service Contract.Parts Gross Profit - Extended Service Contract: Repair Orders on the Extended Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":[\\\"Parts Gross Profit = Sum of Parts Sale - Sum of Parts Cost \\\",\\\"All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":920,\"chartName\":\"Labor Sold Hours\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the sum of all sold hours on a monthly basis.\",\"hasGoal\":\"0\",\"id\":727,\"matViewName\":\"\",\"parentId\":null,\"slug\":\"cp-labor-sold-hours\",\"sort\":4,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_labor_sold_hours\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows total hours sold for Labor for Customer Pay Repair Orders. In the detailed view, the graphs are categorized into 7 based on the type of the payment.Labor Sold Hours - Combined: All Pay Types  - Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract included.Labor Sold Hours - Customer Pay: This graph shows the Labor Sold Hours for Customer Pay Repair Orders.Labor Sold Hours - Warranty: This graph shows the Labor Sold Hours for Warranty Repair Orders.Labor Sold Hours - Internal: This graph shows the Labor Sold Hours for Internal Repair Orders.Labor Sold Hours - Maintenance Plan: This graph shows the Labor Sold Hours for Repair Orders on the Maintenance Plan.Labor Sold Hours - Factory Service Contract:This graph shows the Labor Sold Hours for Repair Orders on the Factory Service Contract.Labor Sold Hours - Extended Service Contract: This graph shows the Labor Sold Hours for Repair Orders on the Extended Service Contract.\\\",\\\"calculation\\\":[\\\"Labor Sold Hours: Total Hours Spent on Labor grouped by month.\\\",\\\"All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.\\\",\\\"\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":960,\"chartName\":\"CP Labor Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the monthly total revenue incurred from labor in Present, Past and Previous Years.\",\"hasGoal\":\"0\",\"id\":724,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-labor-revenue\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\" dbd_cp_labor.vw_revenue_by_year\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":[\"Labor Workmix\"],\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows the Labor Revenue for Customer Pay Repair orders over a three year trend grouped by month. The detailed view shows the Labor Revenue for each of the Pay Types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract  over a period of thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Revenue = Sum of Labor Sale grouped by months.\\\",\\\"1\\\":\\\"All Op Categories - Repair, Maintenance, Competitive and Shop Supplies included.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":918,\"chartName\":\"Job Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the job counts for all categories for customer pay\",\"hasGoal\":\"0\",\"id\":728,\"matViewName\":\"\",\"parentId\":null,\"slug\":\"cp-job-count---all-categories\",\"sort\":10,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_repair_order_count_cp_by_category_by_year\",\"dbdId\":4,\"updatedAt\":\"2020-03-18T05:20:09.611\",\"updatedBy\":\"current_user()\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\" The main graph shows Job Counts for Customer Pay ROs over a 3 year trend grouped by month.The detailed graphs show the Jobs Counts for each of the Pay Types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":[\\\"Job Count by Category = Count of Jobs each Pay Types.\\\",\\\"All Op Categories - Repair, Maintenance, Competitive & Shop Supplies included.\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1318,\"chartName\":\"Hours Per RO - Parts Only\",\"clientId\":null,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the customer pay parts hours Per RO in Present, Past and Previous Years.\",\"hasGoal\":null,\"id\":729,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-parts-hours-per-ro\",\"sort\":7,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":null,\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows the Parts Hours sold per Customer Pay Repair Order over a 3 year trend grouped by month.The detailed view shows the Parts Hours Sold Repair Order for each of the Pay Types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Parts Hours Per RO = Sum of  Sold Hours where Parts Sale not equal to zero / Count of Repair Orders for each Pay Type where Parts Sale not equal to zero.\\\",\\\"1\\\":\\\" \\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":966,\"chartName\":\"CP Parts Gross Profit Percentage\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the parts gross profit percentage grouped by month in Present, Past and Previous Year\",\"hasGoal\":\"0\",\"id\":730,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_parts_gross_profit_percentage\",\"parentId\":null,\"slug\":\"cp-parts-gross-profit-percentage\",\"sort\":3,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_gross_profit_percentage_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":[\"Parts Workmix\"],\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\" The graph shows the Parts Gross Profit % for Customer Pay Repair Orders on a 3 year trend grouped by month.\\\",\\\"calculation\\\":[\\\"Parts Gross Profit %  = ((Sum of Parts Sale - Sum of Parts Cost)/Sum of Parts Sale)*100\\\",\\\"All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1103,\"chartName\":\"CP Add On Job Count By Op Category\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"<script>\",\"hasGoal\":\"0\",\"id\":731,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-add-on-job-count-by-op-category\",\"sort\":5,\"dbdName\":\"AddOns\",\"viewDetails\":\"dbd_addons.vw_charts_addons_opcategory\",\"dbdId\":18,\"updatedAt\":null,\"updatedBy\":\"armatus.fopc.support\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":953,\"chartName\":\"Parts Revenue Per RO\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows sum of parts sales for jobs per Repair Order in present, past and previous years.\",\"hasGoal\":\"0\",\"id\":732,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-parts-revenue-per-ro\",\"sort\":4,\"dbdName\":\"CP Parts Overview\",\"viewDetails\":\"dbd_cp_parts.vw_parts_revenue_per_ro_by_year\",\"dbdId\":1,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the Parts Revenue per Repair Order on a 3 year trend grouped by month.The detailed view shows the Parts Revenue per Repair Order for each of the Pay Types - Customer Pay, Warranty, Internal, Maintenance Plan,Extended Service Contract and Factory Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":[\\\"Parts Revenue Per RO = Sum of Parts Extended Sale/Repair Order Count\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1073,\"chartName\":\"CP Labor GP %\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The chart shows the Labor GP % per month, with an annual selection filtering option.\",\"hasGoal\":\"0\",\"id\":733,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-labor-gross-profit-percentage\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_gross_profit_percentage_by_year\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":[\"Labor Workmix\"],\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\" The graph shows the Labor Gross Profit % for Customer Pay Repair Orders grouped by month on a 3 year trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Gross Profit %  = ((Labor Sale - Labor Cost)/ Labor Sale)*100 \\\",\\\"1\\\":\\\"All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":998,\"chartName\":\"CP Job Count - Shop Supplies\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":734,\"matViewName\":\"\",\"parentId\":918,\"slug\":\"cp-job-count-by-shop-supply\",\"sort\":5,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_repair_order_count_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1007,\"chartName\":\"Labor Gross Profit - Combined\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":735,\"matViewName\":\"\",\"parentId\":944,\"slug\":\"labor-gross-profit-combined\",\"sort\":1,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_profit_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1009,\"chartName\":\"Labor Gross Profit - Internal\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":736,\"matViewName\":\"\",\"parentId\":944,\"slug\":\"labor-gross-profit-internal\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_labor_profit_combined\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1018,\"chartName\":\"CP Revenue By Competitive\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":737,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-revenue-by-competitive\",\"sort\":4,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1019,\"chartName\":\"CP Revenue By Maintenance\",\"clientId\":0,\"createdAt\":null,\"createdBy\":null,\"description\":null,\"hasGoal\":\"0\",\"id\":738,\"matViewName\":\"\",\"parentId\":960,\"slug\":\"cp-revenue-by-maintenance\",\"sort\":3,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_detail_revenue_by_category\",\"dbdId\":4,\"updatedAt\":null,\"updatedBy\":null,\"duplicateDbd\":null,\"active\":\"0\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1127,\"chartName\":\"ELR - Total Shop\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows the average hourly price charged on all repair orders over a 3 year trend\",\"hasGoal\":null,\"id\":725,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_effective_labor_rate\",\"parentId\":null,\"slug\":\"cp-effective-labor-rate\",\"sort\":5,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_ux_effective_labor_rate_customer_pay_by_year\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows the Labor Rates grouped by month for Customer Pay Repair Orders .The detailed graphs represent the Labor Rates for each of the Pay Types - Customer Pay, Warranty, Internal, Extended Service Contract and Factory Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Effective Labor Rate = Sum of Labor Sale / Sum of Labor Sold Hours for each Pay Type. \\\",\\\"1\\\":\\\"All Op Categories - Repair, Maintenance and Competitive included. \\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":938,\"chartName\":\"CP Return Rate\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This graph shows rate of customers who return within 6 or 12 months of their first visit.\",\"hasGoal\":\"0\",\"id\":706,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-return-rate\",\"sort\":5,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_special_metrics.vw_return_rate\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"Return rate measures the service to service retention.The graph takes the number of unique vehicles that visited six or 12 months before the current month and calculates the return rate by identifying how many of them returned during the current month. Customer Pay Repair Orders only.\\\",\\\"calculation\\\":[\\\"6 Months Return Rate = (No of VINs that returned in the current month / Distinct VIN count in last six months before current month )*100 \\\",\\\"12 Months Return Rate = (No of VINs that returned in the current month / Distinct VIN count in last twelve months before current month )*100\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":933,\"chartName\":\"CP Labor Volume Opportunity\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows how much additional we can earn when we increase the Hours/RO(Volume) of work..\",\"hasGoal\":\"1\",\"id\":374,\"matViewName\":\"dms_physical_rw.cdk_xml_mvw_charts_labor_volume_opportunity\",\"parentId\":null,\"slug\":\"cp-labor-volume-opportunity\",\"sort\":2,\"dbdName\":\"\",\"viewDetails\":\"dbd_cp_opportunity_labor.vw_charts_labor_volume_opportunity\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1138,\"chartName\":\"RO Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the count of all repair orders on a 3 year trend.\",\"hasGoal\":\"0\",\"id\":688,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-ro-count\",\"sort\":11,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_labor.vw_repair_order_count_by_year\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":[\"Labor Workmix\"],\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The main graph shows the Total Count of Customer Pay Repair Orders over a 3 year trend grouped by month.In the detailed view, the graphs are categorized into 7 based on the type of the payment.Repair Order Count - Combined: Repair Orders that are paid with any of the  means of Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract. Repair Order Count - Customer Pay: Repair Orders with Customer Pay.Repair Order Count - Warranty: Repair Orders that are covered under Warranty.Repair Order Count - Internal: Repair Orders with Internal pay.,Repair Order Count - Maintenance Plan: Repair Orders that are covered under Maintenance Plan.Repair Order Count - Factory Service Contract: Repair Orders that are covered under Factory Service Contract.Repair Order Count - Extended Service Contract: Repair Orders that are covered under Extended Service Contract over a period of thirteen months.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Repair Order Count = count of all Repair Orders grouped by month.\\\",\\\"1\\\":\\\"All the opcode categories - Repair, Maintenance, Competitive are considered for these graphs.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":971,\"chartName\":\"Labor Sold Hours Customer Pay\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"Labor Sold hours\",\"hasGoal\":\"0\",\"id\":402,\"matViewName\":\"\",\"parentId\":920,\"slug\":\"labor-sold-hours-customer-pay\",\"sort\":2,\"dbdName\":\"CP Labor Overview\",\"viewDetails\":\"dbd_cp_overview.vw_detail_labor_sold_hours\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1233,\"chartName\":\"% Discounted Per Discounted CP ROs - Labor & Parts\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\" The graph shows the percentage CP Discounts over all CP Discounted CP ROs over a trend of 13 months.test\",\"hasGoal\":\"0\",\"id\":561,\"matViewName\":null,\"parentId\":1232,\"slug\":\"cp-labor-and-parts-discounts\",\"sort\":1,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_labor_and_parts_discount_by_month\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":null,\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":942,\"chartName\":\"CP Revenue\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the revenue of Customer Pay for the Labor, Parts and Combined Labor & Parts.\",\"hasGoal\":\"0\",\"id\":719,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-revenue\",\"sort\":1,\"dbdName\":\"CP Overview\",\"viewDetails\":\"dbd_cp_overview.ux_revenue\",\"dbdId\":3,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the revenue for Labor and Parts respectively, grouped by month for Customer Pay Repair Orders Only.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Revenue  =  Sum of Labor Sale.\\\",\\\"1\\\":\\\"Parts Revenue  =  Sum of Parts Extended Sale.\\\",\\\"2\\\":\\\"Combined Revenue = Sum of Labor Sale + Sum of Parts Extended Sale. \\\",\\\"3\\\":\\\"All the opcode categories - Repair, Maintenance, Competitive are considered in these graph.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1232,\"chartName\":\"CP % Disc Per Discounted CP ROs test\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the % of Labor and Parts Discounts over the total Customer Pay RO Discounts test.\",\"hasGoal\":\"0\",\"id\":401,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-discounted-ro-percentage-by-service-advisor\",\"sort\":5,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts_fn_discount_service_advisors_monthly_comparison\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the % of Labor and Parts Discounts over the total CP RO Discounts. test\\\",\\\"calculation\\\":[\\\" % Discounted Per Discounted CP ROs  - Labor = (Total Labor Discount $ / Total Discounted CP Sale) * 100  \\\",\\\" % Discounted Per Discounted CP ROs - Parts  = (Total Parts Discount $ / Total Discounted CP Sale)) * 100\\\"]}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":936,\"chartName\":\"CP Parts to Labor Ratio By Category\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"Parts-to-Labor ratio is a comparison between parts sales to labor sales over a certain period.\",\"hasGoal\":\"0\",\"id\":376,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-parts-to-labor-ratio-by-category\",\"sort\":7,\"dbdName\":\"Special Metrics\",\"viewDetails\":\"dbd_special_metrics.vw_parts_to_labor_ratio_by_category\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"Customer Pay ROs Only.A value of P$2.40 (for example) means that for every dollar of labor sold (L$1.00) a total P$2.40 of parts were also sold.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Parts to Labor Ratio = Parts Sale / Labor Sale (for each op category).\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1358,\"chartName\":\"CP 1-Line-RO Count\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"This chart shows the Single Line ROs - Total Shop, Above 60K miles and Below 60K miles - All Technicians / Individual Techs.\",\"hasGoal\":\"1\",\"id\":377,\"matViewName\":\"\",\"parentId\":null,\"slug\":\"\",\"sort\":3,\"dbdName\":\"Technician Efficiency\",\"viewDetails\":\"\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the count of Repair Orders that only have a single Customer Pay \\\\n\\\",\\\"calculation\\\":{\\\"0\\\":\\\"CP 1-Line-RO Count = Count of Repair Orders where Count of CP Jobs = 1\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"},{\"chartId\":1234,\"chartName\":\"CP Discounts - Labor & Parts\",\"clientId\":1,\"createdAt\":null,\"createdBy\":null,\"description\":\"The graph shows the Labor and Parts discounts for Customer Pay ROs over 13 months.\",\"hasGoal\":\"0\",\"id\":379,\"matViewName\":null,\"parentId\":null,\"slug\":\"cp-labor-and-parts-discounts\",\"sort\":1,\"dbdName\":\"Discounts\",\"viewDetails\":\"dbd_discounts.vw_labor_and_parts_discount_by_month\",\"dbdId\":null,\"updatedAt\":null,\"updatedBy\":\"<EMAIL>\",\"duplicateDbd\":null,\"active\":\"1\",\"markdown\":\"{\\\"overview\\\":\\\"The graph shows the total Labor and Parts Discounts for Customer Pay Repair Orders over a 13 month trend.\\\",\\\"calculation\\\":{\\\"0\\\":\\\"Labor Discount = Sum of Discounts applied on Labor grouped by month \\\",\\\"1\\\":\\\"Parts Discount = Sum of Discounts applied on Parts grouped by month.\\\"}}\",\"__typename\":\"StatelessCcPhysicalRwGetChartMasterRecord\"}]"}, {"name": "partsMatrixTypes", "value": "[\"Matrix - Fleet Customer\",\"Source Matrix\",\"Source Matrix\",\"Source Matrix\",\"Source Matrix\"]"}, {"name": "kpiFilterEndDate", "value": ""}, {"name": "12Months", "value": "2024-09-01,2025-08-31"}, {"name": "selectedStoreId", "value": "[\"244284397\"]"}, {"name": "keycloakToken", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJCZ3EzVXRTcHEtSTAzNjVLZUFvblFsQTZFcERLREJtd0twcml0SDRjSXNJIn0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eMbtJiHmR2anI2BBOM_GKQPshIIqk5mRecfIdLnpcq3I4JwSBuMXnEp1U0841MBsLow_pKoST8WzhobOPde6cxXA6OAp8DxDxtDx8Nd2ipvTk1s-Ka18oZJUY-eTD71ZPtzoOtUvvCY2yJnZ6EFeHZQhMC7KkKjN2q81eOlXqAxCKM5XHoeHw8sliJjC5gQP2qBBGH-6D0oG9Jel_BUY3wqogO24WFb4KjsXpyrGkKOKJsR-ru_V71zcGV56u9Dic5xVXLTZ6qO0FId2Q4QCkRVS2hoB-_5GvaZj2HxCORltY9YYXGqtAW3eZOzObWjwBzmYTqk4iDmTiSNOgQT0KQ"}, {"name": "provenance", "value": "fopc"}, {"name": "laborGridTypes", "value": "[\"Grid - Fleet Customer\",\"Model Grid\",\"Grid - Fleet Pay Type\"]"}, {"name": "realm", "value": "carriageag"}, {"name": "selectedStoreName", "value": "Carriage Kia of Woodstock"}, {"name": "userID", "value": "<EMAIL>"}, {"name": "storeSelected", "value": "Carriage Kia of Woodstock"}, {"name": "kpiStartDate", "value": ""}]}]}